{
  "files.associations": {
    ".code-workspace": "jsonc",
    ".babelrc": "json",
    ".eslintrc": "jsonc",
    ".eslintrc*.json": "jsonc",
    ".stylelintrc": "jsonc",
    "stylelintrc": "jsonc",
    ".htmlhintrc": "jsonc",
    "htmlhintrc": "jsonc",
    "Procfile*": "shellscript",
    "README": "markdown"
  },
  "search.useIgnoreFiles": true,
  "search.exclude": {
    "**/build": true,
    "**/output": true,
    "**/dist": true,
    "**/yarn.lock": true,
    "**/package-lock.json": true,
    "**/*.log": true,
    "**/*.pid": true,
    "**/.git": true,
    "**/node_modules": true,
    "**/bower_components": true
  },
  //
  "editor.rulers": [80, 120],
  "files.eol": "\n",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  //
  "todo-tree.general.tags": ["TODO:", "FIXME:"],
  "todo-tree.highlights.defaultHighlight": {
    "gutterIcon": true
  },
  "todo-tree.highlights.customHighlight": {
    "TODO:": {
      "foreground": "#fff",
      "background": "#ffbd2a",
      "iconColour": "#ffbd2a"
    },
    "FIXME:": {
      "foreground": "#fff",
      "background": "#f06292",
      "icon": "flame",
      "iconColour": "#f06292"
    }
  },
  //
  "eslint.alwaysShowStatus": true,
  "eslint.nodePath": "./node_modules",
  "eslint.probe": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue"
  ],
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  "javascript.validate.enable": false,
  "typescript.validate.enable": true,
  "flow.enabled": false,
  //
  "css.validate": false,
  "scss.validate": false,
  "less.validate": false,
  //
  "prettier.trailingComma": "all",
  "prettier.printWidth": 80,
  "prettier.semi": true,
  "prettier.arrowParens": "avoid",
  "prettier.bracketSpacing": true,
  "prettier.jsxBracketSameLine": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.tslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },
  "javascript.format.enable": true,
  "typescript.format.enable": true,

  "prettier.requireConfig": true,
  //
  "json.format.enable": true,
  "[json]": {
    "editor.tabSize": 2,
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true
  },
  "editor.formatOnSave": false,

  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "[jsonc]": {
    "editor.tabSize": 2,
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true
  },
  "emmet.triggerExpansionOnTab": true,
  "typescript.tsdk": "node_modules/typescript/lib",
  "editor.bracketPairColorization.enabled": true,
  "[shellscript]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[dotenv]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "byted-ide.dev-environment-manager.showForwardPortNotification": "hidden",
  "[sql]": {
    "editor.defaultFormatter": "adpyke.vscode-sql-formatter"
  },
  "[html]": {
    "editor.defaultFormatter": "vscode.html-language-features"
  }
}
