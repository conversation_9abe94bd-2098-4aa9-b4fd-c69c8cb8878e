{"react组件模版": {"scope": "typescriptreact", "prefix": "rcfc", "body": ["import React, { useCallback } from 'react';", "interface I$1Props {}", "", "const $1: React.FC<I$1Props> = () => {", "  return (", "    <>$0</>", "  );", "};", "", "export default $1"], "description": "react组件模版"}, "react组件模版_styled_root": {"scope": "typescriptreact", "prefix": "rcfcr", "body": ["import React, { useCallback } from 'react';", "import styled from 'styled-components';", "const Root = styled.div``", "interface I$1Props {}", "", "const $1: React.FC<I$1Props> = () => {", "  return (", "    <Root>$0</Root>", "  );", "};", "", "export default $1"], "description": "react组件模版"}, "chart_config_内部组件模板": {"scope": "typescriptreact", "prefix": "rcfccc", "body": ["import React, { useCallback } from 'react';", "import styled from 'styled-components';", "import { useModel } from '@reduck/core';", "import { ChartConfigModel } from '@/pages/MultiAnalyze/_model';", "const Root = styled.div``", "interface I$1Props {}", "", "const $1: React.FC<I$1Props> = () => {", " const [{ id }] = useModel([", "  ChartConfigModel,", "  state => ({", "   id: state.id,", " }),", " ]);", "  return (", "    <Root>$0</Root>", "  );", "};", "", "export default $1"], "description": "react组件模版"}}