{"name": "@devmind/server", "version": "1.0.0", "description": "This is a serverless project", "license": "MIT", "scripts": {"dev": "sls dev", "dev:ht": "sls dev", "build": "sls prepare -o output", "deploy": "sls deploy", "deploy:boe": "sls deploy -c serverless.boe.yml"}, "dependencies": {"@bsls/engine": "^0.18.56", "@bsls/gulu": "^0.18.56", "@bsls/react": "0.18.109", "@bsls/scm": "^0.18.56", "@bsls/tce": "0.18.109", "@bsls/typescript": "0.18.109", "@byted-service/consul": "^2.1.4", "@byted/ferry-node-gulu": "^1.7.8", "@gulu/fetch": "^2.2.0", "@gulu/runtime-base": "^5.0.9", "@gulu/security": "^1.13.0", "@gulu/static": "^1.1.4", "@gulu/views": "^2.0.3", "axios": "^0.28.0", "cross-env": "^7.0.3", "dayjs": "^1.8.29", "lodash": "^4.17.21", "query-string": "^7.1.1"}, "devDependencies": {"@bsls/bytefaas": "^0.18.56", "@bsls/cli": "^0.18.56", "@gulu/proxies": "^1.2.0", "@rdservices/function": "^0.1.10", "@rdservices/webpack-rdservice-starling-plugin": "^0.3.7", "@types/jest": "^25.1.3", "@types/node": "^13.7.6"}, "resolutions": {"axios": "0.28.0", "crypto-js": "4.2.0", "file-type": "16.5.4", "immer": "9.0.6", "ip": "1.1.9", "jsonwebtoken": "9.0.0", "postcss": "8.4.31", "sanitize-html": "^2.12.1", "semver": "7.5.2", "tough-cookie": "4.1.3"}}