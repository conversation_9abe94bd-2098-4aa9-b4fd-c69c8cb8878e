FROM hub.byted.org/epscprelease/nodejs:16-multiarch as builder

ARG build_info

RUN echo "Build info: $build_info"
# 封装了下载产物解压到制定目录的脚本
RUN sh -c "curl -O http://tosv.byted.org/obj/mars-base/download_scm.sh && chmod 777 download_scm.sh"
RUN sh -c "./download_scm.sh bytedance/vethought/measure/node /usr/app/"

FROM hub.byted.org/base/security.debian12.nodejs18:latest
COPY --from=builder /usr/app/ /usr/app/
WORKDIR /usr/app
CMD [ "node", "bootstrap.js" ]
