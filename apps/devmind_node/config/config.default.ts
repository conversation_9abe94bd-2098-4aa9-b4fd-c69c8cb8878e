export default () => ({
  psm: 'p.s.m',
  /**
   * 基础运行时插件[runtime-base](https://code.byted.org/nodejs/gulu/tree/master/packages/byted-gulu-runtime-base)
   * 该插件集成了常用的中间件和插件，如下：
   * koa-body中间件：默认开启`multipart`
   * ms-metrics插件：用于记录请求打点，对接MS平台，便于监控服务运行情况；
   * access-log插件：用于记录请求的访问日志；
   * ms-logger插件：提供日志器，通过日志器打印的日志会上报至MS平台，便于监控日志；
   * metrics插件：提供metrics实例，便于业务打点；
   * env插件：提供env实例，便于访问线上环境信息；
   * session插件：用于对接公司的session服务；
   * views插件：用于后端模板渲染；
   * static插件：用于静态资源服务；
   * onerror插件：用于进行统一的错误处理，可根据不同的请求类型返回不同的错误信息；
   * [更多功能插件](https://code.byted.org/nodejs/gulu)
   */

  fetch: {
    defaultTimeout: 160000, // fetch的默认超时时间，单位ms，0表示禁用超时限制，默认不做任何限制。
  },
  plugin: [
    '@byted/ferry-node-gulu', // ++
    '@gulu/runtime-base',
    '@gulu/fetch',
    '@gulu/views',
    '@gulu/static',
    '@gulu/security',
  ],
  views: {
    map: {
      html: 'handlebars',
    },
    extension: 'html',
  },
  ferry: {
    idl: './http_idl/toutiao.pgcfe.ferry_gulu_demo.thrift', // ++
  },
  security: {
    cors: {
      enable: true,
      credentials: true,
      headers: [],
      methods: ['GET', 'HEAD', 'PUT', 'POST', 'DELETE', 'PATCH'],
      rules: ['https://hg.bytedance.net', 'https://bits.bytedance.net'],
    },
    xframe: {
      enable: false,
    },
  },
  devmind: {
    redirectOnesite: 'bits',
    url: process.env.QUALITY_API_HOST,
    bytecycleAuthCode: 319506,
    bytecycleNodeId: '7140091339407935532',
  },
  imagex: {
    host: 'https://p9-devmind.byteimg.com/',
    serviceId: 'jq5uwkkk3h',
    accessKey: 'AKLTOTk5MjUyZDA4YzA1NDNlNmEwNjcwMTRjNjJhMmQ3MzQ',
    secretAccessKey:
      'WldWaFpXWXhNR1psWmpjd05HTXdOV0k1WkRkbE16RXlZV0UyTnpZeE9HUQ==',
    templateName: '~tplv-jq5uwkkk3h-image.png',
  },
});
