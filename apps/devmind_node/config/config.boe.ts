export default () => ({
  security: {
    cors: {
      enable: true,
      credentials: true,
      headers: [],
      methods: ['GET', 'HEAD', 'PUT', 'POST', 'DELETE', 'PATCH'],
      rules: ['*'],
    },
  },
  devmind: {
    redirectOnesite: 'hg-boe',
    url: 'https://devmind-boe.bytedance.net',
    bytecycleAuthCode: 120356,
    bytecycleNodeId: '7140181399780247596',
  },
  imagex: {
    host: 'http://devmind.p-boe.bytedance.net/',
    serviceId: 'xufffgrkle',
    accessKey: 'AKLTOTFhODVhYmZlNmNiNGE5MmI2ODBiNTcyZWU4NTNjM2U',
    secretAccessKey:
      'TkRJd05URTFaV0V5WkRnME5HVTBNemhsTVdGaFpXRTVaRGcyWVdVellUaw==',
    templateName: '~tplv-xufffgrkle-image.png',
  },
});
