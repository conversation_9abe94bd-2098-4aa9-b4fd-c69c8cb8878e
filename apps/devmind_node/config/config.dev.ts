const origin = 'http://*************:32080/';
export default () => ({
  security: {
    cors: {
      enable: true,
      credentials: true,
      headers: [],
      methods: ['GET', 'HEAD', 'PUT', 'POST', 'DELETE', 'PATCH'],
      rules: ['*'],
    },
  },
  devmind: {
    url: 'https://devmind-boe.bytedance.net',
    bytecycleAuthCode: 120356,
    bytecycleNodeId: '7140181399780247596',
  },
  plugin: [
    '@gulu/proxies',
    '@byted/ferry-node-gulu', // ++
    '@gulu/runtime-base',
    '@gulu/fetch',
    '@gulu/views',
    '@gulu/static',
    '@gulu/security',
  ],
  proxies: {
    '/devinsight/teko/': {
      target: origin,
    },
    '/devinsight/auth/': {
      target: origin,
    },
    '/devinsight/devmind-open/': {
      target: origin,
    },
  },
  // koa-proxy代理form-data类型的接口，代理异常；用@gulu/proxies替换
  // koaProxy: {
  //   host: 'http://*************:32080/',
  //   match: /^\/devinsight\/(teko|devmind-open|auth)\//,
  // },
  imagex: {
    host: 'https://p9-devmind.byteimg.com/',
    serviceId: 'jq5uwkkk3h',
    accessKey: 'AKLTOTk5MjUyZDA4YzA1NDNlNmEwNjcwMTRjNjJhMmQ3MzQ',
    secretAccessKey:
      'WldWaFpXWXhNR1psWmpjd05HTXdOV0k1WkRkbE16RXlZV0UyTnpZeE9HUQ==',
    templateName: '~tplv-jq5uwkkk3h-image.png',
  },
});
