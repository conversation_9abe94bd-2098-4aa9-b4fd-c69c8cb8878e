export default () => ({
  security: {
    cors: {
      enable: true,
      credentials: true,
      headers: [],
      methods: ['GET', 'HEAD', 'PUT', 'POST', 'DELETE', 'PATCH'],
      rules: ['https://bytecycle.bytedance.net'],
    },
  },
  devmind: {
    url: process.env.QUALITY_API_HOST,
    bytecycleAuthCode: 319506,
    bytecycleNodeId: '7140091339407935532',
  },
  imagex: {
    host: 'https://p9-devmind.byteimg.com/',
    serviceId: 'jq5uwkkk3h',
    accessKey: 'AKLTOTk5MjUyZDA4YzA1NDNlNmEwNjcwMTRjNjJhMmQ3MzQ',
    secretAccessKey:
      'WldWaFpXWXhNR1psWmpjd05HTXdOV0k1WkRkbE16RXlZV0UyTnpZeE9HUQ==',
    templateName: '~tplv-jq5uwkkk3h-image.png',
  },
});
