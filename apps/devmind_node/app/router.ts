import HttpApplication from '@gulu/application-http';

export default (app: HttpApplication) => {
  const { router, controller } = app;
  router.all('/precache/gen_async_req', controller.report.queryAsyncReport);
  router.all('/precache/gen_url', controller.report.queryReport);
  // router.all('/precache/metrics', controller.metric.queryMetric);
  // router.post('/url/report', controller.url.getDevmindReportUrl);
  // router.post('/url/bytecycle', controller.url.getBytecycleReportUrl);

  router.all('/precache/metric_data', controller.metric.queryMetricAllTypes);
  router.post('/url/query', controller.url.getDevmindUrlQuery);
  router.get('/email/report_link', controller.url.getReportUrl);
  router.get('/api/get_env(.*)?', controller.home.getEnv);
  router.get('/api', controller.home.index);
  router.get('/embed/people/review', controller.view.peoplePerformance);

  router.get('/(.*)', controller.view.devmindPlatfrom);
};
