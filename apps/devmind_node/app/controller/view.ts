import { Controller, HttpContext } from '@gulu/application-http';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat);

export default class ViewsController extends Controller {
  public async devmindPlatfrom(ctx: HttpContext) {
    // ctx.redirect(
    //   `${ctx.app.config.devmind.redirectOnesite}${ctx.path}${ctx.search}`,
    // );
    ctx.handleMethod = 'devmindPlatfrom';
    await ctx.render('devmind_platform/index', {
      user: ctx.userinfo,
    });
  }

  public async peoplePerformance(ctx: HttpContext) {
    const { username = '' } = ctx.query;

    let {
      granularity = 'ap',
      startTime = dayjs().format('YYYY-MM-DD 00:00:00'),
    } = ctx.query;

    let error = '';

    granularity = granularity.toLowerCase();
    if (!['ap', 'myp'].includes(granularity)) {
      granularity = 'ap';
    }

    if (
      !dayjs(startTime, 'YYYY-MM-DD', true).isValid() ||
      dayjs(startTime).year() <= 1970
    ) {
      startTime = dayjs().format('YYYY-MM-DD 00:00:00');
    }

    try {
      const res = await ctx.service.user.GetNodeIdByUsername(username);
      if (res.code === 200) {
        const nodeId = res.data.node_id;
        if (!nodeId) {
          error = '用户不存在，请检查链接格式。如有疑问，请联系@金雅谊 @姜磊';
        } else {
          const redirectUrl = ctx.service.view.getDashboardUrl(
            nodeId,
            startTime,
            granularity,
          );
          ctx.redirect(redirectUrl);
        }
      } else {
        error = '用户不存在，请检查链接格式。如有疑问，请联系@金雅谊 @姜磊';
      }
    } catch {
      error = '未知错误, 请检查链接格式';
    }

    ctx.body = `
    <div>${error}</div>
    <script>
      setTimeout(() => {
        location.href = 'https://${ctx.app.config.devmind.redirectOnesite}.bytedance.net/datamind/employee'
      }, 1500)
    </script>`;
  }
}
