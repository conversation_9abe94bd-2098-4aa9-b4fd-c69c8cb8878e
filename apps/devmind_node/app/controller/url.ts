import { Controller, HttpContext } from '@gulu/application-http';
import dayjs from 'dayjs';
import { omit } from 'lodash';
import { _request } from '../request';
import {
  SpaceType,
  TimeCycle,
  TimeFilterValue,
  TimeGranularity,
  TimeGranularityText,
  TimeCycleText,
  getTimeRange,
  getTimeGranularity,
} from '../utils';

interface ReportUrlQuery {
  spaceType: SpaceType;
  templateId: string;
  nodeId: string;
  timeFilter: {
    granularity: TimeGranularity | string;
    cycle: TimeCycle | string;
    range: string[];
  };
  domainMeasureObjFilter?: {
    measureObjId: string;
    filter: string[];
  };
}

interface MetricDrawerUrlQuery {
  _metricId: string;
  _metricName: string;
  _metricType: string;
  _metricNodeId: string;
  _metricTimeGranularity: TimeGranularity;
  _metricTimeCycle: TimeCycle;
  _metricStartTime: string;
  _metricEndTime: string;
  _metricDomainMeasureObjId?: string;
  _metricDomainMeasureObjFilter?: string[];
}

// const reportUrlPathRecord = {
//   [SpaceType.Business]: '/biz/report',
//   [SpaceType.Department]: '/dept/report',
//   [SpaceType.Report]: '/report/report',
// };

export default class UrlController extends Controller {
  // public async getDevmindReportUrl(ctx: HttpContext) {
  //   const {
  //     spaceType,
  //     templateId,
  //     nodeId,
  //     timeFilter,
  //     domainMeasureObjFilter = { measureObjId: '', filter: [] },
  //     _metricId: metricId,
  //     _metricType: metricType,
  //     _metricName: metricName = '',
  //   } = ctx.request.body as ReportUrlQuery & MetricDrawerUrlQuery;

  //   const basePath = `${(reportUrlPathRecord as any)[spaceType]}/${templateId}`;

  //   const { cycle } = timeFilter;
  //   const { granularity } = timeFilter;

  //   const search = ctx.service.url.zipUrlQuery({
  //     nodeId,
  //     granularity,
  //     cycle,
  //     range: timeFilter.range,
  //     domainMeasureObjId: domainMeasureObjFilter.measureObjId,
  //     domainMeasureObjFilter: domainMeasureObjFilter.filter,
  //     _metricId: metricId,
  //     _metricType: metricType,
  //     _metricName: metricName,
  //     _metricNodeId: nodeId,
  //     _metricTimeGranularity: granularity,
  //     _metricTimeCycle: cycle,
  //     _metricStartTime: timeFilter.range?.[0],
  //     _metricEndTime: timeFilter.range?.[1],
  //     _metricDomainMeasureObjId: domainMeasureObjFilter.measureObjId,
  //     _metricDomainMeasureObjFilter: domainMeasureObjFilter.filter,
  //   });

  //   const { url } = ctx.app.config.devmind;
  //   const path = `${url}${basePath}?${search}`;

  //   // 返回响应体
  //   ctx.body = {
  //     status_code: 0,
  //     message: 'ok',
  //     data: path,
  //   };
  // }

  // public async getBytecycleReportUrl(ctx: HttpContext) {
  //   const { timeFilter, psm } = ctx.request.body as {
  //     timeFilter: TimeFilterValue;
  //     psm: string;
  //   };

  //   const { bytecycleNodeId, bytecycleAuthCode, url } = ctx.app.config.devmind;

  //   const reportListUri = `/api/v1/insight/report/list`;

  //   try {
  //     const [start_time, end_time] = getTimeRange(dayjs(), {
  //       granularity: timeFilter.granularity,
  //     });
  //     const res = await _request(ctx, reportListUri, {
  //       method: 'POST',
  //       headers: { 'auth-code': bytecycleAuthCode },
  //       body: JSON.stringify({
  //         business_id: bytecycleNodeId,
  //         granularity_type: TimeGranularityText[timeFilter.granularity],
  //         generate_cycle: TimeCycleText.standard,
  //         time_range: {
  //           start_time,
  //           end_time,
  //         },
  //       }),
  //     });

  //     const templateId = res.data?.[0].template_id;

  //     if (!templateId) {
  //       ctx.body = {
  //         code: 1,
  //         message: '不存在该时间粒度bytecycle报告',
  //         data: '',
  //       };
  //       return;
  //     }

  //     const basePath = `${
  //       reportUrlPathRecord[SpaceType.Business]
  //     }/${templateId}`;

  //     const search = ctx.service.url.zipUrlQuery({
  //       nodeId: bytecycleNodeId, // 服务空间节点
  //       granularity: timeFilter.granularity,
  //       cycle: timeFilter.cycle,
  //       range: timeFilter.range,
  //       domainMeasureObjId: psm ? '7' : '',
  //       domainMeasureObjFilter: psm ? [psm] : [],
  //       mode: 'read',
  //     });

  //     const path = `${url}${basePath}?${search}`;

  //     // 返回响应体
  //     ctx.body = {
  //       code: 0,
  //       message: 'ok',
  //       data: path,
  //     };
  //   } catch {
  //     ctx.body = {
  //       code: 1,
  //       message: '获取bytecycle报告失败',
  //       data: '',
  //     };
  //   }
  // }

  public async getDevmindUrlQuery(ctx: HttpContext) {
    if (
      Object.prototype.toString.call(ctx.request.body) !== '[object Object]'
    ) {
      ctx.body = {
        status_code: 1,
        message: '请传入对象',
        data: '',
      };
      return;
    }
    const search = ctx.service.url.zipUrlQuery(ctx.request.body);

    // 返回响应体
    ctx.body = {
      status_code: 0,
      message: 'ok',
      data: search,
    };
  }

  public async getReportUrl(ctx: HttpContext) {
    const { spaceType, templateId, start_time, end_time } = ctx.query;
    const query = omit(ctx.query, [
      'spaceType',
      'templateId',
      'start_time',
      'end_time',
    ]);
    query.range = [start_time, end_time];
    const search = ctx.service.url.zipUrlQuery(query);
    const redirectUrl = `/devinsight/virtual/${spaceType}/report/${templateId}?${search}`;
    // ctx.redirect(redirectUrl);
    // 返回响应体
    ctx.body = {
      status_code: 0,
      message: 'ok',
      data: redirectUrl,
    };
  }
}
