import { Controller, HttpContext } from '@gulu/application-http';

class IndexController extends Controller {
  /**
   * Controller方法示列
   *
   * @param ctx - Gulu上下文对象
   */
  public async index(ctx: HttpContext) {
    // 健康检查
    ctx.body = {
      code: 200,
    };
  }

  public async getEnv(ctx: HttpContext) {
    const data = process.env.LOG_ENV ? JSON.parse(process.env.LOG_ENV) : {};
    // 健康检查
    ctx.body = {
      code: 200,
      data,
      env: process.env.SSO_ENV,
    };
  }

  time(ctx: HttpContext) {
    // 返回响应体
    ctx.body = {
      status_code: 0,
      message: 'ok',
      data: Date.now(),
    };
  }
}

export default IndexController;
