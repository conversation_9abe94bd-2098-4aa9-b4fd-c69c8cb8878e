import { Controller, HttpContext } from '@gulu/application-http';
import type {
  AnalysisDimInfo,
  BaseLine,
  BaseLineInfo,
  MeasureObject,
} from '@quality/common-apis/src/apis/api/base/base_data';
import dayjs from 'dayjs';

import {
  API_V1,
  MetricTypeMeta,
  MetricTypeComplexMeta,
  getDefaultTimeRange,
  getTimeGranularity,
  getTimeRange,
  MetricData,
} from '../utils';

import { CHART_TYPE } from '../constants';

export type MeasureObj = Pick<
  MeasureObject,
  'dimension_id' | 'display_name' | 'dimension_data_type'
>;

// eslint-disable-next-line consistent-return
export const getParams = (ctx: HttpContext, noReport?: true) => {
  const template_id = ctx.request.body.template_id || ctx.query.template_id;
  const metric_id = ctx.request.body.metric_id || ctx.query.metric_id;
  const node_id = ctx.request.body.node_id || ctx.query.node_id;
  const node_name = ctx.request.body.node_name || ctx.query.node_name;
  const report_id = ctx.request.body.report_id || ctx.query.report_id;

  const space_type = ctx.request.body.space_type || ctx.query.space_type;
  const chart_type = ctx.request.body.chart_type || ctx.query.chart_type;
  const granularity_type =
    ctx.request.body.granularity_type || ctx.query.granularity_type;
  const start_time = ctx.request.body.start_time || ctx.query.start_time;
  const end_time = ctx.request.body.end_time || ctx.query.end_time;
  const tag_id = ctx.request.body.tag_id || ctx.query.tag_id;
  if (noReport || (template_id && start_time && end_time)) {
    return {
      templateId: template_id,
      timeRange: [start_time, end_time],
      metricId: metric_id,
      nodeId: node_id,
      nodeName: node_name,
      spaceType: space_type,
      chartType: chart_type,
      granularityType: granularity_type,
      reportId: report_id,
      tag_id,
    };
  }
  return false;
};

export default class MetricController extends Controller {
  public async queryMetricAllTypes(ctx: HttpContext) {
    ctx.handleMethod = 'queryMetricAllTypes';
    const params = getParams(ctx, true);
    if (!params) {
      ctx.body = {
        code: 200,
        msg: 'ok',
        data: [],
      };
      return;
    }
    const { metric_type, start_time, end_time } = ctx.request.body;
    const {
      metricId,
      spaceType,
      nodeId,
      nodeName,
      granularityType: granularity_type,
      tag_id,
    } = params;
    const dataDetail: any = await ctx.service.metric.queryDetail(ctx, {
      metric_id: metricId,
      metric_type,
      version: API_V1,
      space_type: spaceType,
      project_id: nodeId,
    });
    const isMetaMetric = metric_type === MetricTypeMeta;
    const isComplexMetric = metric_type === MetricTypeComplexMeta;
    const metricData = (
      isMetaMetric
        ? dataDetail?.data?.meta_metric_data
        : isComplexMetric
        ? dataDetail?.data?.complex_metric_data
        : null
    ) as any;
    if (!metricData) {
      ctx.body = {
        code: 200,
        msg: 'noMetricData',
        data: [],
      };
      return;
    }

    const granularity = getTimeGranularity(granularity_type);
    const timeFilterInfo = {
      range:
        start_time && end_time
          ? [start_time, end_time]
          : getTimeRange(dayjs(), {
              granularity,
              standard: true,
              duration: -1,
            }),
      granularity,
      cycle: 'standard',
    };
    const baselineList: BaseLine[] = metricData.base_line.flatMap(
      (item: BaseLineInfo) => item.baselines ?? [],
    );

    let warmupExtra: any = {};
    if (isMetaMetric) {
      warmupExtra = {
        metric_id: metricData?.metric_id,
        metric_type: 'meta_metric',
        detail_drill_config: {
          [metricData?.metric_id]: Boolean(
            metricData?.metric_expand_attribute?.drill_config?.length,
          ),
        },
      };
    } else {
      const dillConfig: any = {};
      metricData?.atom_meta_metric_list?.forEach(({ metric_data }: any) => {
        dillConfig[metric_data?.id] = Boolean(
          metric_data?.metric_expand_attribute?.drill_config?.length,
        );
      });
      warmupExtra = {
        metric_id: metricData?.metric_id,
        metric_type: 'score_metric',
        detail_drill_config: {
          ...dillConfig,
          [metricData?.metric_id]: Boolean(
            metricData?.metric_expand_attribute?.drill_config?.length,
          ),
        },
      };
    }
    const queryList: {
      [key: string]: {
        api_path: string;
        method: string;
        payload: any;
        headers: any;
        keys: any;
      }[];
    } = { card: [], double_axis: [], sheet: [] };
    // 指标分析，有多个card卡片时，单个复合+多个原子 或 单个原子
    const list: Array<MetricData> = [];
    list.push(metricData);

    metricData.atom_metric_caliber_list?.length &&
      list.push(
        ...metricData.atom_metric_caliber_list?.map(
          ({ metric_data }: any) => metric_data,
        ),
      );
    await Promise.all(
      list?.map(async (item: any) =>
        ctx.service.metric
          .queryParams(ctx, item, {
            tag_id,
            chartType: CHART_TYPE.CARD,
            timeFilterInfo,
            nodeName,
            spaceType,
          })
          .then(async (_params: any) => {
            const params = _params;
            if (params?.query_info) {
              params.query_info.tag_id = tag_id;
              params.query_info.node_id = nodeId;
            }
            // ctx.service.metric.queryData(ctx, params);
            // 指标卡片分析和结论
            queryList.card.push({
              ...(await ctx.service.query.Query(
                ctx,
                {
                  ...params,
                  warmup_extra: warmupExtra,
                },
                true,
              )),
              keys: {
                metric_id: item.id,
                metric_name: item?.display_name || item?.name,
              },
            });
          }),
      ),
    );

    // 作为传空数组的模拟请求
    baselineList.push({
      ...baselineList?.[0],
      id: '',
      display_name: '',
    });
    // 趋势分析
    await Promise.all(
      baselineList.map(async ({ id, display_name }) => {
        const commonParams = await ctx.service.metric.queryParams(
          ctx,
          metricData,
          {
            chartType: CHART_TYPE.DOUBLE_AXIS,
            nodeName,
            spaceType,
            tag_id,
            customParams: {
              baselines: id || [],
              gen_rule_text: true,
            },
            timeFilterInfo: {
              ...timeFilterInfo,
              range: getDefaultTimeRange(
                timeFilterInfo.range,
                timeFilterInfo.granularity,
              ),
            },
          },
        );
        if (commonParams?.query_info) {
          commonParams.query_info.tag_id = tag_id;
          commonParams.query_info.node_id = nodeId;
        }

        // 获取指标详情和分析结论
        queryList.double_axis.push({
          ...(await ctx.service.query.Query(
            ctx,
            {
              ...commonParams,
              warmup_extra: warmupExtra,
            },
            true,
          )),
          keys: {
            base_line_id: id,
            base_line_name: display_name,
          },
        });
      }),
    );

    const measureObjList = metricData?.analysis_info?.analysis_dim_ids ?? [];

    // 各维度sheet
    await Promise.all(
      measureObjList.map(async (e: AnalysisDimInfo) => {
        const params = await ctx.service.metric.queryParams(ctx, metricData, {
          tag_id,
          chartType: CHART_TYPE.SHEET,
          timeFilterInfo,
          nodeName,
          spaceType,
          measureDimensionId: e.dim_id,
          customParams: {
            query_info: {
              tag_id,
              order_list: [
                {
                  alias_id: isMetaMetric
                    ? metricData?.id
                    : metricData.complex_metric_list?.[0]?.id,
                  order:
                    metricData?.metric_attribute?.analysis_direction ===
                    'Negative'
                      ? 'desc'
                      : 'asc', // 正向指标小到大，负向指标大到小
                },
              ],
            },
          },
        });

        if (params?.query_info) {
          params.query_info.tag_id = tag_id;
          params.query_info.node_id = nodeId;
        }

        queryList.sheet.push({
          ...(await ctx.service.query.Query(
            ctx,
            {
              ...params,
              warmup_extra: warmupExtra,
            },
            true,
          )),
          keys: {
            measureDimensionId: e.dim_id,
            measureDimensionName: e.display_name,
          },
        });
      }),
    );

    ctx.body = {
      code: 200,
      msg: 'ok',
      data: {
        reqinfo_map: queryList,
      },
    };
  }
}
