import { Controller, HttpContext } from '@gulu/application-http';
import type { MeasureObject } from '@quality/common-apis/src/apis/api/base/base_data';
import type { DevmindQueryReq } from '@quality/common-apis/src/apis/api/query/devmind';

import dayjs from 'dayjs';
import InsightService from '../service/insight';
import {
  API_V1,
  API_V2,
  MetricTypeMeta,
  MetricTypeComplexMeta,
  CHART_TYPE,
  MeasureTypeBussiness,
  MeasureObjectBusinessTree,
  MeasureObjectDepartmentTree,
  MeasureObjectReportTree,
  SpaceType,
  endTimeFormat,
  getDefaultTimeRange,
  getMetrics,
  isMetaMetricData,
  MetricData,
  startTimeFormat,
  getTimeGranularity,
  getTimeCycle,
  getMetricDisplayType,
  StoryTypeComplexMeta,
  StoryTypeMeta,
  RequestTypeExecuteQuery,
} from '../utils';
import { getParams } from './metric';

// export const SpaceTreeField = {
//   [SpaceType.Department]: MeasureObjectDepartmentTree,
//   [SpaceType.Business]: MeasureObjectBusinessTree,
//   [SpaceType.Report]: MeasureObjectReportTree,
// } as any;

export default class ReportController extends Controller {
  public async queryAsyncReport(ctx: HttpContext) {
    ctx.handleMethod = 'queryAsyncReport';
    const query = getParams(ctx);
    if (!query) {
      ctx.body = {
        code: 200,
        msg: 'ok',
        data: {},
      };
      return;
    }

    const { templateId, timeRange, spaceType: defaultSpaceType } = query;
    const report = await ctx.service.report.queryReport(ctx, {
      template_id: templateId,
      version: API_V2,
    });

    const spaceType =
      defaultSpaceType ||
      String(report?.template_info?.business_info?.space_type);

    ctx.logger.info('devmind_node: report data measure list = %v', report);

    const parentMetricList = getMetrics(report.parent_editor_info) || [];
    const childMetricList = getMetrics(report.editor_info) || [];

    const timeFilterValue = {
      range: timeRange,
      granularity: getTimeGranularity(report?.template_info?.granularity_type),
      cycle: getTimeCycle(report?.template_info?.generate_cycle),
    };

    const metricList = [...parentMetricList, ...childMetricList];
    const params = ctx.service.widtable.genHeaderReq(
      metricList,
      timeFilterValue,
      {
        parent_id: report?.template_info?.parent_id,
        space_type: spaceType,
        template_id: templateId,
      },
    );
    const p2 = ctx.service.widtable.genExecuteReq(spaceType, '');
    params.request.filters = {
      ...params.request.filters,
      ...p2.request.filters,
    };
    ctx.body = {
      code: 200,
      msg: 'ok',
      data: params,
    };
  }

  public async queryReport(ctx: HttpContext) {
    ctx.handleMethod = 'queryReport';
    const query = getParams(ctx);
    if (!query) {
      ctx.body = {
        code: 200,
        msg: 'ok',
        data: {},
      };
      return;
    }

    const {
      templateId,
      metricId,
      reportId,
      nodeId,
      tag_id,
      nodeName: businessName,
      timeRange,
      spaceType: defaultSpaceType,
      chartType,
      granularityType,
    } = query;
    const report = await ctx.service.report.queryReport(ctx, {
      template_id: templateId,
      version: API_V2,
    });

    const spaceType =
      defaultSpaceType || report?.template_info?.business_info?.space_type;

    const businessId =
      nodeId || report?.template_info?.business_info?.business_id;
    const nodeInfo = await ctx.service.user.GetNodeInfo(ctx, {
      node_id: businessId,
      space_type: spaceType,
      version: API_V1,
    });

    // const businessTreeInfo = await ctx.service.user.GetBusinessTree(ctx, {
    //   space_type: spaceType,
    //   version: API_V1,
    // });
    // const { treeIdRecord: bizTreeIdRecord } = transformNodeTree(
    //   businessTreeInfo?.data || [],
    // );

    ctx.logger.info('devmind_node: report data measure list = %v', report);

    const nodeName = businessName || nodeInfo?.data?.inject_filter_value;

    const queryList: {
      [key: string]: {
        api_path: string;
        method: string;
        payload: any;
        headers: any;
        keys: any;
      }[];
    } = { '1': [], '2': [], '3': [] };

    const metaMetricList: any[] = [];
    const complexMetricList: any[] = [];

    const parentMetricList = getMetrics(report.parent_editor_info) || [];
    const childMetricList = getMetrics(report.editor_info) || [];

    const metricList = [...parentMetricList, ...childMetricList];

    const metricDetails = await Promise.all(
      metricList
        ?.filter(({ metric_id }: { metric_id: string }) =>
          metricId ? metric_id === metricId : true,
        )
        ?.map(
          async (m: {
            metric_id: string;
            metric_type: string;
            display_type: string;
            default_dim_id: string;
          }) => {
            ctx.logger.info(
              'devmind_node: fetch metric data = %v',
              m.metric_id,
            );

            const res: any = await ctx.service.metric.queryDetail(ctx, {
              metric_id: m.metric_id,
              metric_type: m.metric_type,
              space_type: spaceType,
              version: API_V1,
              project_id: businessId,
              node_path: nodeInfo.node_path,
            });

            ctx.logger.info('devmind_node: get metric data = %v', res);

            const isMetaMetric = m.metric_type === MetricTypeMeta;
            const isComplexMetric = m.metric_type === MetricTypeComplexMeta;

            if (isMetaMetric) {
              const req = {
                metric_id: m.metric_id,
                project_id: businessId,
                space_type: spaceType,
                version: API_V1,
              };
              const resp = await InsightService.GetMetaMetricById(ctx, req);
              metaMetricList.push({
                req,
                resp,
              });
            } else {
              const req = {
                complex_metric_id: m.metric_id,
                project_id: businessId,
                space_type: spaceType,
                version: API_V1,
              };
              const resp = await InsightService.GetComplexMetricById(ctx, req);
              complexMetricList.push({
                req,
                resp,
              });
            }

            ctx.logger.info(
              'devmind_node: get meta/score metric data = %v',
              metricId,
            );

            const metricData = isMetaMetric
              ? res?.data?.meta_metric_data
              : isComplexMetric
              ? res?.data?.complex_metric_data
              : null;

            let warmupExtra: any = {};
            if (isMetaMetric) {
              warmupExtra = {
                metric_id: metricData?.metric_id,
                metric_type: 'meta_metric',
                detail_drill_config: {
                  [m.metric_id]: Boolean(
                    metricData?.metric_expand_attribute?.drill_config?.length,
                  ),
                },
              };
            } else {
              const dillConfig: any = {};
              metricData?.atom_meta_metric_list?.forEach(
                ({ metric_data }: any) => {
                  dillConfig[metric_data?.id] = Boolean(
                    metric_data?.metric_expand_attribute?.drill_config?.length,
                  );
                },
              );
              warmupExtra = {
                metric_id: metricData?.metric_id,
                metric_type: 'score_metric',
                detail_drill_config: {
                  ...dillConfig,
                  [m.metric_id]: Boolean(
                    metricData?.metric_expand_attribute?.drill_config?.length,
                  ),
                },
              };
            }

            ctx.logger.info(
              'devmind_node: get metricData metric data = %v',
              metricData,
            );
            if (!metricData) {
              return {
                metric_id: m.metric_id,
                metric_type: m.metric_type,
                resp: res,
              };
            }

            const timeFilterInfo = {
              range: timeRange,
              granularity: getTimeGranularity(
                granularityType || report?.template_info?.granularity_type,
              ),
              cycle: getTimeCycle(report?.template_info?.generate_cycle),
            };

            const baselineList =
              metricData?.base_line?.find(
                ({ measure_obj }: { measure_obj: MeasureObject }) =>
                  // 找到空间类型对应的度量对象的基线
                  measure_obj.display_name === MeasureTypeBussiness,
              )?.baselines ?? [];

            const baselineId = baselineList.find(
              ({ is_key }: { is_key: boolean }) => is_key,
            )?.id;

            // All 度量对象 = 度量对象 + 分析维度
            // 对比分析和分析结论会用到
            // 分析维度

            // const { data = [] } =
            //   await ctx.service.metric.GetMetricCombineGroup(ctx, {
            //     version: API_V1,
            //     node_id: nodeId,
            //     story_id: m.metric_id,
            //     story_type: isMetaMetric
            //       ? MetricTypeMeta
            //       : MetricTypeComplexMeta,
            //   });
            // 后端暂时没这个表
            const measureObjList =
              metricData?.analysis_info?.analysis_dim_ids ?? [];

            ctx.logger.info('metric data measure list = %v', measureObjList);

            const extra_info = {
              node_id: nodeId || nodeInfo?.node_id,
              node_depth: nodeInfo?.node_depth,
              template_id: templateId,
              parent_id: report?.template_info?.parent_id,
              time_range: {
                start_time: timeRange[0],
                end_time: timeRange[1],
              },
              space_type: spaceType,
            };

            const commonParams = await ctx.service.metric.queryParams(
              ctx,
              metricData,
              {
                tag_id,
                chartType: getMetricDisplayType(m?.display_type),

                nodeName,
                spaceType,
                customParams: {
                  baselines: [],
                  gen_rule_text: true,
                  template_id: templateId,
                  report_id: reportId || '',
                  /* 标识 query 请求的卡片类型，方便和组件里边每个指标卡类型一一对应，否则可能导致请求结果覆盖 */
                  dashboard_display_type: m?.display_type,
                  extra_info,
                },
                timeFilterInfo: {
                  ...timeFilterInfo,
                  range: getDefaultTimeRange(
                    timeFilterInfo.range,
                    timeFilterInfo.granularity,
                  ),
                },
                anchorStartTime: timeRange[0],
                measureDimensionId: m?.default_dim_id,
              },
              { hackTrendAnalysis: false },
            );

            if (commonParams?.query_info) {
              commonParams.query_info.tag_id = tag_id;
            }

            ctx.logger.info(
              'devmind_node: get common params chartype = %v params = %v',
              metricId,
            );
            const params = await ctx.service.query.Query(
              ctx,
              {
                ...commonParams,
                baselines: [],
                warmup_extra: warmupExtra,
              },
              true,
            );

            if (params?.query_info) {
              params.query_info.tag_id = tag_id;
            }

            queryList['1'].push({
              ...params,
              keys: {
                cn_name: '指标详情和结论',
                query_name: 'QUERY',
                chart_type: m?.display_type || CHART_TYPE.DOUBLE_AXIS,
                metric_id: m?.metric_id,
                metric_name: metricData?.display_name || metricData?.name,
                space_type: spaceType,
                node_id: businessId,
                metric_type: m.metric_type,
              },
            });

            ctx.logger.info(
              'devmind_node: metric chartype = %v data = %v',
              CHART_TYPE.DOUBLE_AXIS,
              '指标详情和结论',
            );

            // 专家分析
            queryList['3'].push({
              ...(await ctx.service.autoinsight.GetExpertAnalysis(
                ctx,
                {
                  version: API_V1,
                  metric_id: m?.metric_id,
                  start_time: timeFilterInfo?.range?.[0],
                  end_time: timeFilterInfo?.range?.[1],
                  business_id: businessId!,
                },
                true,
              )),
              keys: {
                cn_name: '专家分析',
                query_name: 'ExpertAnalysis',
                metric_id: m?.metric_id,
                metric_name: metricData?.display_name || metricData?.name,
              },
            });

            const list: Array<MetricData | undefined> = [];
            list.push(metricData);
            const transformAtomMetricCaliberList = await Promise.all(
              metricData?.atom_metric_caliber_list?.map(async (item: any) => {
                const { complex_meta_data } = item;
                if (complex_meta_data) {
                  // eslint-disable-next-line @typescript-eslint/no-shadow
                  const res = await InsightService.GetComplexMetricById(ctx, {
                    version: API_V1,
                    complex_metric_id: complex_meta_data.id,
                  });
                  return {
                    ...item,
                    complex_meta_data: res.data,
                  };
                }
                return item;
              }) || [],
            );
            metricData.atom_metric_caliber_list?.length &&
              list.push(
                ...transformAtomMetricCaliberList?.map(
                  ({ metric_data, complex_meta_data }: any) =>
                    metric_data || complex_meta_data,
                ),
              );

            if (chartType !== 'double_axis') {
              // 获取指标卡片详情数据query
              await Promise.all(
                list?.map(async (item: any) =>
                  ctx.service.metric
                    .queryParams(ctx, item, {
                      tag_id,
                      chartType: CHART_TYPE.CARD,
                      timeFilterInfo,
                      nodeName,
                      spaceType,
                    })
                    .then(async (_params: any) => {
                      ctx.logger.info(
                        'devmind_node: metric detail chartype = %v cardParams = %v',
                        CHART_TYPE.CARD,
                        '获取指标卡片详情数据',
                      );

                      const params = _params;
                      if (params?.query_info) {
                        params.query_info.tag_id = tag_id;
                      }

                      // ctx.service.metric.queryData(ctx, params);
                      // 指标卡片分析和结论
                      queryList['1'].push({
                        ...(await ctx.service.query.Query(
                          ctx,
                          {
                            ...params,
                            warmup_extra: warmupExtra,
                          },
                          true,
                        )),
                        keys: {
                          cn_name: '指标卡片分析和结论',
                          query_name: 'Query',
                          chart_type: CHART_TYPE.CARD,
                          metric_id: m?.metric_id,
                          metric_name:
                            metricData?.display_name || metricData?.name,
                        },
                      });
                    }),
                ),
              );

              // 获取指标卡片详情数据devmindquery
              // await Promise.all(
              //   list?.map(async (item: any) => {
              //     ctx.service.metric
              //       .queryParams(ctx, item, {
              //         tag_id,
              //         chartType: CHART_TYPE.CARD,
              //         timeFilterInfo,
              //         nodeName,
              //         spaceType,
              //       })
              //       .then(async (_queryParams: any) => {
              //         ctx.logger.info(
              //           'devmind_node: metric detail chartype = %v cardParams = %v',
              //           CHART_TYPE.CARD,
              //           '获取指标卡片详情数据',
              //         );
              //         const queryParams = _queryParams;
              //         if (queryParams?.query_info) {
              //           queryParams.query_info.tag_id = tag_id;
              //         }

              //         console.log('list', list);
              //         const injectFilterKey = SpaceTreeField[spaceType];
              //         const injectFilterValue = [nodeName];

              //         const params: DevmindQueryReq = {
              //           query_type: RequestTypeExecuteQuery,
              //           metric_list: [
              //             {
              //               alias_id: metricData.id,
              //               type: isMetaMetricData(metricData)
              //                 ? StoryTypeMeta
              //                 : StoryTypeComplexMeta,
              //               query_key: metricData.id,
              //             },
              //           ],
              //           std_granularity: queryParams.query_info.std_granularity,
              //           filters: {
              //             [injectFilterKey]: injectFilterValue,
              //             time_range: timeFilterInfo.range,
              //           },
              //           parent_id: report?.template_info?.parent_id,
              //           template_id: report?.template_info?.template_id,
              //           with_chart_data: true,
              //           with_rule_conclusion: true,
              //           node_depth: nodeInfo?.node_depth,
              //           space_type: spaceType,
              //           tag_id,
              //           report_id: reportId,
              //           time_range: {
              //             start_time: timeFilterInfo?.range?.[0] ?? '',
              //             end_time: timeFilterInfo?.range?.[1] ?? '',
              //           },
              //           node_path: nodeInfo?.node_path,
              //           dipsplay_type: CHART_TYPE.CARD,
              //         };
              //         console.log('params', params);

              //         // ctx.service.metric.queryData(ctx, params);
              //         // 指标卡片分析和结论
              //         queryList['1'].push({
              //           ...(await ctx.service.query.DevmindQuery(
              //             ctx,
              //             {
              //               data: params,
              //               version: API_V1,
              //             },
              //             true,
              //           )),
              //           keys: {
              //             cn_name: '指标卡片分析和结论',
              //             query_name: 'Query',
              //             chart_type: CHART_TYPE.CARD,
              //             metric_id: m?.metric_id,
              //             metric_name:
              //               metricData?.display_name || metricData?.name,
              //           },
              //         });
              //       });
              //   }),
              // );

              // 获取指标趋势图
              await ctx.service.metric
                .queryParams(ctx, metricData, {
                  tag_id,
                  chartType: CHART_TYPE.DOUBLE_AXIS,
                  timeFilterInfo: {
                    ...timeFilterInfo,
                    range: getDefaultTimeRange(
                      timeFilterInfo.range,
                      timeFilterInfo.granularity,
                    ),
                  },
                  anchorStartTime: timeFilterInfo.range[0],
                  nodeName,
                  spaceType,
                  customParams: {
                    baselines: baselineId ? [baselineId] : [],
                    extra_info,
                  },
                })
                .then(async (_params: any) => {
                  ctx.logger.info(
                    'devmind_node: metric chartype = %v axisParams = %v',
                    CHART_TYPE.DOUBLE_AXIS,
                    '获取指标趋势图',
                  );
                  // 获取指标趋势图
                  // ctx.service.metric.queryData(ctx, params);

                  const params = _params;
                  if (params?.query_info) {
                    params.query_info.tag_id = tag_id;
                  }

                  queryList['2'].push({
                    ...(await ctx.service.query.GetValidBaseLine(
                      ctx,
                      {
                        ...params,
                        baselines: [],
                        warmup_extra: warmupExtra,
                      },
                      true,
                    )),
                    keys: {
                      cn_name: '获取基线',
                      query_name: 'GetValidBaseLine',
                      metric_id: m?.metric_id,
                      metric_name: metricData?.display_name || metricData?.name,
                    },
                  });

                  const res = await ctx.service.query.GetValidBaseLine(ctx, {
                    ...params,
                    baselines: [],
                  });

                  queryList['2'].push({
                    ...(await ctx.service.query.Query(
                      ctx,
                      {
                        ...params,
                        baselines: [],
                        warmup_extra: warmupExtra,
                      },
                      true,
                    )),
                    keys: {
                      cn_name: '趋势分析',
                      query_name: 'Query',
                      chart_type: CHART_TYPE.DOUBLE_AXIS,
                      metric_id: m?.metric_id,
                      metric_name: metricData?.display_name || metricData?.name,
                    },
                  });

                  const baseLineParams = await Promise.all(
                    res?.data?.reduce(
                      (arr: any, item: any) =>
                        arr.concat(
                          ...item?.baselines?.map((baseline: any) => {
                            if (baseline?.id && baseline.is_key) {
                              return ctx.service.query.Query(
                                ctx,
                                {
                                  ...params,
                                  baselines: [baseline.id],
                                  warmup_extra: warmupExtra,
                                },
                                true,
                              );
                            }
                            return Promise.resolve(null);
                          }),
                        ),
                      [],
                    ) || [],
                  );

                  baseLineParams.forEach((param: any) => {
                    param &&
                      queryList['2'].push({
                        ...param,
                        keys: {
                          cn_name: '趋势分析',
                          query_name: 'Query',
                          chart_type: CHART_TYPE.DOUBLE_AXIS,
                          metric_id: m?.metric_id,
                          metric_name:
                            metricData?.display_name || metricData?.name,
                        },
                      });
                  });
                });

              const queryParams = await ctx.service.metric.queryParams(
                ctx,
                metricData,
                {
                  tag_id,
                  chartType: CHART_TYPE.DOUBLE_AXIS,
                  nodeName,
                  spaceType,
                  customParams: {
                    baselines: [],
                    gen_rule_text: true,
                    extra_info,
                  },
                  timeFilterInfo,
                },
              );

              if (queryParams?.query_info) {
                queryParams.query_info.tag_id = tag_id;
              }
              // 获取指标详情和分析结论
              const data = await ctx.service.metric.queryData(ctx, queryParams);
              const { series = [] } = data?.data?.chart_data || {};

              await Promise.all(
                measureObjList?.map(async (e: any) => {
                  // 获取指标CHART_TYPE.DOUBLE_AXIS

                  const params = await ctx.service.metric.queryParams(
                    ctx,
                    metricData,
                    {
                      tag_id,
                      chartType: CHART_TYPE.SHEET,
                      timeFilterInfo,
                      nodeName,
                      spaceType,
                      measureDimensionId: e.dim_id,
                      customParams: {
                        query_info: {
                          tag_id,
                          order_list: [
                            {
                              alias_id: isMetaMetricData(metricData)
                                ? metricData?.id
                                : metricData.complex_metric_list?.[0]?.id,
                              order:
                                metricData?.metric_attribute
                                  ?.analysis_direction === 'Negative'
                                  ? 'desc'
                                  : 'asc', // 正向指标小到大，负向指标大到小
                            },
                          ],
                        },
                        extra_info,
                      },
                    },
                  );

                  if (params?.query_info) {
                    params.query_info.tag_id = tag_id;
                  }

                  ctx.logger.info(
                    'devmind_node: metric chartype = %v sheetParams = %v',
                    CHART_TYPE.SHEET,
                    params,
                  );
                  // 获取指标对比分析（拆分维度）
                  // ctx.service.metric.queryData(ctx, params);
                  queryList['2'].push({
                    ...(await ctx.service.query.Query(
                      ctx,
                      {
                        ...params,
                        warmup_extra: warmupExtra,
                      },
                      true,
                    )),
                    keys: {
                      cn_name: '对比分析',
                      query_name: 'Query',
                      chart_type: CHART_TYPE.SHEET,
                      metric_id: m?.metric_id,
                      metric_name: metricData?.display_name || metricData?.name,
                    },
                  });

                  // 分析详情
                  // const { x_axis = [] } = data?.data?.chart_data || {};

                  const res = await ctx.service.report.GetLastTwoXTime(ctx, {
                    version: API_V1,
                    time_range: {
                      start_time: timeFilterInfo?.range?.[0],
                      end_time: timeFilterInfo?.range?.[1],
                    },
                    std_granularity: commonParams?.query_info?.std_granularity,
                  });

                  return Promise.all(
                    ['Positive', 'Negative'].map(async (direction: any) => {
                      const fluctuationParams = {
                        version: API_V1,
                        query_req: commonParams,
                        dim_radix: 1,
                        influence_direction: direction,
                        analysis_direction:
                          metricData.metric_attribute?.analysis_direction ?? '',
                        analysis_dims: [e.dim_id],
                        analysis_time: {
                          refer_time: res?.data?.[0],
                          target_time: res?.data?.[1],
                        },
                        default_value:
                          metricData.metric_attribute?.default_value,
                      };
                      // 波动分析
                      queryList['3'].push({
                        ...(await ctx.service.autoinsight.GetFluctuationAnalysis(
                          ctx,
                          { ...fluctuationParams, warmup_extra: warmupExtra },
                          true,
                        )),
                        keys: {
                          cn_name: '波动影响',
                          query_name: 'GetFluctuationAnalysis',
                          chart_type: CHART_TYPE.DOUBLE_AXIS,
                          direction,
                          dimension: e.dim_id,
                          metric_id: m?.metric_id,
                          metric_name:
                            metricData?.display_name || metricData?.name,
                        },
                      });

                      const baselineValue =
                        series?.[0]?.extra_data?.[
                          series?.[0]?.extra_data?.length - 1
                        ]?.baseline_value ?? '';
                      const [startDay, endDay] = timeFilterInfo.range;

                      const default_value =
                        metricData?.metric_attribute?.default_value ?? '';

                      const mainDistanceParams = {
                        version: API_V1,
                        query_req: commonParams,
                        dim_id: e.dim_id,
                        baseline_value: baselineValue,
                        default_value,
                        valid_baseline: data?.data?.valid_baseline,
                        start_time: dayjs(startDay).format(startTimeFormat),
                        end_time: dayjs(endDay).format(endTimeFormat),
                        metric_direction:
                          metricData.metric_attribute?.analysis_direction ?? '',
                        analysis_type: measureObjList?.find(
                          ({
                            display_name,
                            dim_id,
                          }: {
                            display_name: string;
                            dim_id: string;
                          }) => display_name === '人员' && dim_id === e.dim_id,
                        )
                          ? 1
                          : undefined,
                      };
                      // 基线差距
                      queryList['3'].push({
                        ...(await ctx.service.autoinsight.MainDistance(
                          ctx,
                          { ...mainDistanceParams, warmup_extra: warmupExtra },
                          true,
                        )),
                        keys: {
                          cn_name: '主要差距',
                          query_name: 'MainDistance',
                          chart_type: CHART_TYPE.DOUBLE_AXIS,
                          direction,
                          dimension: e.dim_id,
                          metric_id: m?.metric_id,
                          metric_name:
                            metricData?.display_name || metricData?.name,
                        },
                      });
                    }),
                  );
                }),
              );
            }
            // eslint-disable-next-line consistent-return
            return {
              metric_id: m.metric_id,
              metric_type: m.metric_type,
              resp: res,
            };
          },
        ) || [],
    );

    const queryCount: { [key: string]: number } = {};
    for (const key in queryList) {
      queryList[key].forEach(item => {
        queryCount[`${item.keys.cn_name} + ${item.keys.query_name}`] =
          (queryCount[`${item.keys.cn_name} + ${item.keys.query_name}`] || 0) +
          1;
      });
    }

    ctx.body = {
      code: 200,
      msg: 'ok',
      data: {
        reqinfo_map: queryList,
        metric_info_list: metricDetails,
        meta_metric_info_list: metaMetricList,
        complex_metric_info_list: complexMetricList,
        reqinfo_count: queryCount,
      },
    };
  }
}
