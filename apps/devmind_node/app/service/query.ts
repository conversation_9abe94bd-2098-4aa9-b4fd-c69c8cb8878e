import { HttpContext } from '@gulu/application-http';
import { API_V1 } from '../utils';

import { _request } from '../request';

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export default class QueryService {
  static GetDrillWhereList(
    ctx: HttpContext,
    params: { [key: string]: any },
    nofetch: boolean,
  ) {
    const uri = `/api/v${params.version || API_V1}/data_mart/drill_where`;
    const body = JSON.stringify(params);
    return _request(
      ctx,
      uri,
      {
        method: 'POST',
        body,
      },
      nofetch,
    );
  }

  Query(ctx: HttpContext, params: { [key: string]: any }, nofetch?: boolean) {
    const uri = `/api/v${params?.version || API_V1}/data_mart/query`;
    const body = JSON.stringify(params);
    ctx.logger.info('devmind_node: query Query = %v', body);
    const data = _request(
      ctx,
      uri,
      {
        method: 'POST',
        body,
      },
      nofetch,
    );
    return data;
  }

  DevmindQuery(
    ctx: HttpContext,
    params: { [key: string]: any },
    nofetch?: boolean,
  ) {
    const uri = `/api/v${params?.version || API_V1}/data_mart/devmind_query`;

    const body = JSON.stringify(params);
    ctx.logger.info('devmind_node: query DevmindQuery = %v', body);

    const data = _request(
      ctx,
      uri,
      {
        method: 'POST',
        body,
      },
      nofetch,
    );
    return data;
  }

  GetValidBaseLine(
    ctx: HttpContext,
    params: { [key: string]: any },
    nofetch?: boolean,
  ) {
    const uri = `/api/v${params?.version || API_V1}/data_mart/valid_baseline`;
    delete params?.version;
    const body = JSON.stringify(params);
    const data = _request(
      ctx,
      uri,
      {
        method: 'POST',
        body,
      },
      nofetch,
    );
    return data;
  }
}
