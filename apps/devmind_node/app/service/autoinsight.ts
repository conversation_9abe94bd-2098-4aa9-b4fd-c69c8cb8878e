import { HttpContext } from '@gulu/application-http';
import { queryStringify } from '../utils/';
import { _request } from '../request';

export default class InsightAscribeService {
  GetFluctuationAnalysis(
    ctx: HttpContext,
    req: { [key: string]: any },
    nofetch: boolean,
  ) {
    const uri = `/api/v${req.version}/insight/fluctuation-analysis`;
    delete req.version;
    const body = JSON.stringify(req);
    ctx.logger.info(
      'devmind_node: autoinsight GetFluctuationAnalysis = %v',
      body,
    );
    return _request(ctx, uri, { method: 'POST', body }, nofetch);
  }

  GetCompareAnalysis(
    ctx: HttpContext,
    req: { [key: string]: any },
    nofetch: boolean,
  ) {
    const uri = `/api/v${req.version}/insight/compare-analysis`;
    delete req.version;
    const body = JSON.stringify(req);
    ctx.logger.info('devmind_node: autoinsight GetCompareAnalysis = %v', body);
    return _request(ctx, uri, { method: 'POST', body }, nofetch);
  }

  BaseLineDistance(
    ctx: HttpContext,
    req: { [key: string]: any },
    nofetch: boolean,
  ) {
    const uri = `/api/v${req.version}/insight/baseline_distance`;
    delete req.version;
    const body = JSON.stringify(req);
    ctx.logger.info('devmind_node: autoinsight BaseLineDistance = %v', body);

    return _request(ctx, uri, { method: 'POST', body }, nofetch);
  }

  MainDistance(
    ctx: HttpContext,
    req: { [key: string]: any },
    nofetch: boolean,
  ) {
    const uri = `/api/v${req.version}/insight/main_distance`;
    delete req.version;
    const body = JSON.stringify(req);
    return _request(ctx, uri, { method: 'POST', body }, nofetch);
  }

  GetExpertAnalysis(
    ctx: HttpContext,
    params: { [key: string]: any },
    nofetch: boolean,
  ) {
    const uri = `/api/v${params.version}/insight/expert_analysis`;
    delete params.version;
    const body = JSON.stringify(params);
    return _request(ctx, uri, { method: 'GET', body }, nofetch);
  }
}
