import { Service } from '@gulu/application-http';

import queryString from 'query-string';
import J<PERSON><PERSON>rush from '../JSONCrush';

export default class UrlService extends Service {
  zipUrlQuery(obj: Record<string, any>) {
    const zipQueryStr = JSONCrush.crush(JSON.stringify(obj));
    const search = queryString.stringify(
      { q: zipQueryStr },
      {
        arrayFormat: 'comma',
        skipNull: true,
        skipEmptyString: true,
      },
    );
    return search;
  }
}
