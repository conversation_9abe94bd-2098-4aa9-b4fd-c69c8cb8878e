import { Service } from '@gulu/application-http';
import { MetricMetaInfo } from '@quality/common-apis/src/apis/api/insight_report/report_model';
import {
  RequestTypeDevmindQuery,
  RequestTypeExecuteQuery,
  getTimeGranularityId,
  TimeFilterValue,
} from '../utils';

export default class InsightService extends Service {
  /**
   * 构造宽表表头指标卡信息，以及时间粒度和时间范围
   */
  genHeaderReq(
    metricList: MetricMetaInfo[],
    timeFilterValue: TimeFilterValue,
    params: any,
  ) {
    const { range } = timeFilterValue;

    const time_granularity_id =
      getTimeGranularityId(timeFilterValue)?.toString();

    const metric_list = metricList.map(item => ({
      alias_id: item.metric_id,
      type: item.metric_type,
      query_key: item.metric_id, // 用于标识是哪个指标，y 轴坐标
    }));

    return {
      request_key: '', // 第一次发送表头不需要 request_key
      request_type: RequestTypeDevmindQuery, // 固定值
      request: {
        ...params,
        filters: {
          time_range: range,
        },
        with_chart_data: true,
        metric_list,
        std_granularity: {
          time_granularity_id,
        },
        // 生成分析结论需要设置该参数
        with_rule_conclusion: true,
      },
    };
  }

  /**
   * 发送需要请求行信息，
   */
  genExecuteReq(injectFilterKey: string, injectFilterValue: string) {
    console.log(`[${injectFilterKey}]: [${injectFilterValue}]`);

    return {
      request_key: injectFilterValue, // 行标识，默认为 部门/业务线/汇报线/人员 名称，也是 x 轴坐标
      request_type: RequestTypeExecuteQuery, // 固定值
      request: {
        filters: {
          [injectFilterKey]: [injectFilterValue],
        },
      },
    };
  }
}
