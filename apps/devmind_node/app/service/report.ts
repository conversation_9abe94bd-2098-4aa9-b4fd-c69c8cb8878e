import { Service, HttpContext } from '@gulu/application-http';

import { _request } from '../request';
import { queryStringify } from '../utils';

class ReportService extends Service {
  public async queryReport(ctx: HttpContext, params: { [key: string]: any }) {
    const body = await this.GetTempAndInstance(ctx, params);
    const data = body?.data || [];
    return data;
  }

  GetTempAndInstance(
    ctx: HttpContext,
    params: { [key: string]: any },
    nofetch?: boolean,
  ) {
    const query = queryStringify(params, false);
    const uri = `/api/v${params.version}/insight/report_and_instance${query}`;
    ctx.logger.info(
      'devmind_node: report GetTempAndInstance = %v',
      JSON.stringify(params),
      uri,
    );
    return _request(
      ctx,
      uri,
      {
        method: 'get',
      },
      nofetch,
    );
  }

  GetLastTwoXTime(
    ctx: HttpContext,
    params: { [key: string]: any },
    nofetch?: boolean,
  ) {
    const uri = `/api/v${params.version}/insight/report/last_x_time`;
    const body = JSON.stringify(params);
    return _request(
      ctx,
      uri,
      {
        method: 'post',
        body,
      },
      nofetch,
    );
  }
}

export default ReportService;
