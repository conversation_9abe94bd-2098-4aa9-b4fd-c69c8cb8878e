import { Service, HttpContext } from '@gulu/application-http';

import { _request } from '../request';
import { queryStringify } from '../utils';

export default class InsightService extends Service {
  static GetComplexMetricById(
    ctx: HttpContext,
    params: { [key: string]: any },
  ) {
    const query = queryStringify(params);
    const uri = `/api/v${params.version}/insight/complex_metric/${params.complex_metric_id}${query}`;
    ctx.logger.info(
      'devmind_node: insight GetComplexMetricById = %v',
      JSON.stringify(params),
    );
    return _request(ctx, uri, {
      method: 'get',
    });
  }

  static GetMetaMetricById(ctx: HttpContext, params: { [key: string]: any }) {
    const query = queryStringify(params);
    const uri = `/api/v${params.version}/insight/meta-metric/${params.metric_id}${query}`;
    ctx.logger.info(
      'devmind_node: insight GetMetaMetricById = %v',
      JSON.stringify(params),
    );
    return _request(ctx, uri, {
      method: 'get',
    });
  }
}
