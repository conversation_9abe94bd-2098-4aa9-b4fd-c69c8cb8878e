import { Service, HttpContext } from '@gulu/application-http';
import { omitBy, isUndefined, merge } from 'lodash';

import {
  isMetaMetricData,
  queryStringify,
  MetricData,
  MetricMetaData,
  getMetaMetricQueryParams,
  getComplexMetaMetricQueryParams,
  tweakTrendAnalysisHack,
} from '../utils';

import { _request } from '../request';
import InsightService from './insight';

class MetricService extends Service {
  public async queryParams(
    ctx: HttpContext,
    metricData: MetricData,
    queryInfo: { [key: string]: any },
    options?: { hackTrendAnalysis: boolean },
  ) {
    if (!metricData) {
      return;
    }
    const showCompare = true;

    const { hackTrendAnalysis = true } = options ?? {};

    const {
      chartType,
      timeFilterInfo,
      nodeName,
      measureDimensionId,
      anchorStartTime,
      customParams,
      domainMeasureObjFilter,
      spaceType,
      tag_id,
    } = queryInfo;

    const filterOptions = omitBy(
      {
        nodeName,
        spaceType,
        timeFilterInfo,
        measureDimensionId,
        anchorStartTime,
        domainMeasureObjFilter,
      },
      isUndefined,
    );

    const isMetaMetric = isMetaMetricData(metricData);

    const commonParams = isMetaMetric
      ? getMetaMetricQueryParams(
          metricData,
          {
            filterOptions: filterOptions as any,
            showCompare,
            chartType,
          },
          tag_id,
        )
      : await getComplexMetaMetricQueryParams(
          metricData,
          {
            filterOptions: filterOptions as any,
            showCompare,
            chartType,
          },
          params =>
            InsightService.GetComplexMetricById.bind(InsightService)(
              ctx,
              params,
            ),
          tag_id,
        );

    if (!commonParams) {
      return;
    }
    // eslint-disable-next-line consistent-return
    // return customParams ? merge(commonParams, customParams) : commonParams;

    const mergeParams = customParams
      ? merge(commonParams, customParams)
      : commonParams;

    const params = {
      ...(hackTrendAnalysis
        ? tweakTrendAnalysisHack(
            isMetaMetric,
            mergeParams,
            metricData as MetricMetaData,
          )
        : mergeParams),
    };
    return params;
  }

  public async queryDetail(
    ctx: HttpContext,
    params: { [key: string]: any },
  ): Promise<any> {
    const body = await this.GetMetricInfoAndExpertAnalysis(ctx, params);

    return body;
  }

  public async queryData(ctx: HttpContext, params: { [key: string]: any }) {
    // eslint-disable-next-line consistent-return
    return ctx.service.query.Query(ctx, params);
  }

  GetMetricInfoAndExpertAnalysis(
    ctx: HttpContext,
    params: { [key: string]: any },
    nofetch?: boolean,
  ) {
    const query = queryStringify(params, false);
    const uri = `/api/v${params.version}/insight/metric_detail${query}`;
    ctx.logger.info(
      'devmind_node: metric GetMetricInfoAndExpertAnalysis = %v, url = %v',
      JSON.stringify(params),
      uri,
    );

    return _request(
      ctx,
      uri,
      {
        method: 'GET',
      },
      nofetch,
    );
  }

  GetMetricCombineGroup(
    ctx: HttpContext,
    params: { [key: string]: any },
    nofetch?: boolean,
  ) {
    const query = queryStringify(params, false);
    const uri = `/api/v${params.version}/insight/metrics/combine_dim${query}`;
    ctx.logger.info(
      'devmind_node: metric GetMetricCombineGroup = %v, url = %v',
      JSON.stringify(params),
      uri,
    );
    return _request(
      ctx,
      uri,
      {
        method: 'GET',
      },
      nofetch,
    );
  }
}

export default MetricService;
