import { Service } from '@gulu/application-http';
import dayjs from 'dayjs';

export default class ViewService extends Service {
  getDashboardUrl(
    nodeId: string,
    startTime: string,
    granularity: 'myp' | 'ap',
  ) {
    const marMidYearPerformanceStartDate = dayjs('03-01 00:00:00');
    const septMidYearPerformanceStartDate = dayjs('09-01 00:00:00');
    const annualPerformanceStartDate = dayjs('03-01 00:00:00');

    const formatStartTime = dayjs(startTime).format('MM-DD HH:mm:ss');

    const year =
      dayjs(startTime).year() === 1970
        ? dayjs().year()
        : dayjs(startTime).year();

    let devmindStartTime = '';
    let devmindEndTime = '';

    switch (granularity) {
      case 'myp':
        if (
          marMidYearPerformanceStartDate.isSame(formatStartTime) ||
          (marMidYearPerformanceStartDate.isBefore(formatStartTime) &&
            septMidYearPerformanceStartDate.isAfter(formatStartTime))
        ) {
          devmindStartTime = `${year}-${marMidYearPerformanceStartDate.format(
            'MM-DD HH:mm:ss',
          )}`;
        }

        if (
          septMidYearPerformanceStartDate.isSame(formatStartTime) ||
          septMidYearPerformanceStartDate.isBefore(formatStartTime)
        ) {
          devmindStartTime = `${year}-${septMidYearPerformanceStartDate.format(
            'MM-DD HH:mm:ss',
          )}`;
        }

        if (marMidYearPerformanceStartDate.isAfter(formatStartTime)) {
          devmindStartTime = `${
            year - 1
          }-${septMidYearPerformanceStartDate.format('MM-DD HH:mm:ss')}`;
        }
        devmindEndTime = `${dayjs(devmindStartTime)
          .add(6, 'M')
          .subtract(1, 'd')
          .format('YYYY-MM-DD 23:59:59')}`;
        break;
      case 'ap':
      default:
        if (
          annualPerformanceStartDate.isSame(formatStartTime) ||
          annualPerformanceStartDate.isBefore(formatStartTime)
        ) {
          devmindStartTime = `${year}-${annualPerformanceStartDate.format(
            'MM-DD HH:mm:ss',
          )}`;
        }
        if (annualPerformanceStartDate.isAfter(formatStartTime)) {
          devmindStartTime = `${year - 1}-${annualPerformanceStartDate.format(
            'MM-DD HH:mm:ss',
          )}`;
        }
        devmindEndTime = `${dayjs(devmindStartTime)
          .add(12, 'M')
          .subtract(1, 'd')
          .format('YYYY-MM-DD 23:59:59')}`;
    }

    const zipQueryStr = this.ctx.service.url.zipUrlQuery({
      nodeId,
      granularity,
      range: [devmindStartTime, devmindEndTime],
      mode: 'read',
      cycle: 'standard',
      from: 'employee',
      _listGranularity: granularity,
      _listCycle: 'standard',
      _listRange: [devmindStartTime, devmindEndTime],
      freezeVersion: '',
      domainMeasureObjId: '',
      domainMeasureObjFilter: [],
    });

    const redirectUrl = `https://${this.ctx.app.config.devmind.redirectOnesite}.bytedance.net/datamind/employee/dashboard/7202151323540801576?${zipQueryStr}&readAuth=${nodeId},`;

    return redirectUrl;
  }
}
