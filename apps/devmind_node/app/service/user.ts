import { Service, HttpContext } from '@gulu/application-http';
import { queryStringify } from '../utils';
import { _request } from '../request';

class UserService extends Service {
  query() {
    return {
      username: 'gulu',
      email: '<EMAIL>',
    };
  }

  GetBusinessTree(ctx: HttpContext, req: any) {
    const query = queryStringify(req, false);

    const uri = `/api/v${req.version}/insight/business-tree${query}`;
    return _request(ctx, uri, { method: 'get' });
  }

  GetNodeInfo(ctx: HttpContext, req: any) {
    const query = queryStringify(req, false);

    const uri = `/api/v${req.version}/insight/node_info${query}`;
    return _request(ctx, uri, { method: 'get' });
  }

  GetNodeIdByUsername(username: string) {
    const uri = `/api/v2/insight/space_user_node`;
    return _request(this.ctx, uri, {
      method: 'post',
      body: JSON.stringify({ username }),
    });
  }
}

export default UserService;
