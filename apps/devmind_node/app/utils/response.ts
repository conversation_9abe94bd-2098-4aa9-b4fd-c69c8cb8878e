interface IParams {
  data?: any;
  msg?: string;
}

export const success = (p?: IParams) => {
  const params = p || {};
  return {
    data: params.data || {},
    msg: params.msg || 'success',
    code: 200,
  };
};

export const badRequest = (p?: IParams) => {
  const params = p || {};
  return {
    data: params.data || {},
    msg: params.msg || '请求体不合法',
    code: 400,
  };
};

export const unauthorized = (p?: IParams) => {
  const params = p || {};
  return {
    data: params.data || {},
    msg: params.msg || '没有权限',
    code: 403,
  };
};

export const notFound = (p?: IParams) => {
  const params = p || {};
  return {
    data: params.data || {},
    msg: params.msg || '路径或参数错误',
    code: 404,
  };
};

export const error = (p?: IParams) => {
  const params = p || {};
  return {
    data: params.data || {},
    msg: params.msg || '参数错误',
    code: 500,
  };
};
