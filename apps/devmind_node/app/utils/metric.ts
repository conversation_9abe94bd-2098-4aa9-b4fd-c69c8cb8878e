import type { CommonDimension } from '@quality/common-apis/src/apis/api/base/base_data';
import type {
  DisplayConfig,
  DoubleAxies,
} from '@quality/common-apis/src/apis/api/base/display_config';

// import { insightServiceClient } from '@quality/common-apis/src/apis/api/insight/homepage';
import type {
  PublicFilter,
  GroupCondition,
  CompareYearPeriod,
  SelectCondition,
  QueryRequest,
  QueryInfo,
} from '@quality/common-apis/src/apis/api/query/query';
import type { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric.d';
import type { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict.d';
import type { DimMetMetaData } from '@quality/common-apis/src/apis/engine/engine';

import {
  StrOpEq,
  StrOpIn,
  StrOpLike,
  DimensionTypeTimestamp,
  DimensionTypeUnixTimestamp,
  DimensionTypedate,
  DimensionTypeDATE,
  DimensionTypeString,
  CHART_TYPE,
  DefaultValueZero,
  MetricUseStatistics,
  BusinessMeasureObjID,
  DepartmentMeasureObjID,
  ReportMeasureObjID,
  PSMMeasureObjID,
  API_V1,
  ComplexMetricFrom,
  PeopleMeasureObjID,
} from '../constants';
import {
  getDefaultTimeRange,
  getTimeFilterParams,
  TimeFilterValue,
} from './timeFilter';

const timeType = [
  DimensionTypeTimestamp,
  DimensionTypeDATE,
  DimensionTypedate,
  DimensionTypeUnixTimestamp,
]; // 时间类型

export enum SpaceType {
  Department = 'department', // SpaceTypeDept 对应后端idl常量
  Business = 'business', // SpaceTypeBussiness
  Report = 'report', // SpaceTypeReport
  Expert = 'expert',
  Measure = 'measure',
  Employee = 'employee',
  FrontBackEnd = 'onesite_backend_frontend',
  Client = 'onesite_client',
  ManageUnit = 'manage_unit',
}

const DisplayTypeDoubleAxis = 'double_axis';

const DisplayTypeBarRow = 'bar_row';
const DisplayTypeStory = 'story';
const DisplayTypeSheet = 'sheet';
const DisplayTypeBar = 'bar';
const DisplayTypePie = 'pie';

export interface DomainMeasureObjFilterValue {
  measureObjId: string | undefined;
  filter: string[];
}

const baseParams: Pick<
  QueryRequest,
  | 'version'
  | 'project_id'
  | 'drill'
  | 'attribution_info'
  | 'public_group_info'
  | 'mark_config_list'
> = {
  version: API_V1,
  project_id: '',
  drill: {
    layer_list: [],
    detail_config: [],
  },
  attribution_info: [],
  public_group_info: [],
  mark_config_list: [],
};

export type MetricData = MetricMetaData | ComplexMetricMetaData;

export function isMetaMetricData(data: MetricData): data is MetricMetaData {
  if (data) {
    return 'caliber_sql' in data;
  }
  return false;
}

export const getSpaceFilterParams = (options: {
  nodeName: string;
  dimensionId: string;
  dimensionDisplayName: string;
  modelId?: string;
  selectAliasId: string;
  op?: string;
}) => {
  const {
    nodeName,
    dimensionId,
    dimensionDisplayName,
    modelId = '',
    selectAliasId,
    op = StrOpEq,
  } = options;

  const publicFilterListItem: PublicFilter = {
    where_condition: {
      op,
      val: [nodeName],
    },
    where_dimension_list: [
      {
        model_id: modelId,
        model_display_name: '',
        dimension_id: dimensionId,
        data_type: DimensionTypeString,
      },
    ],
  };

  const commonDimensionItem: CommonDimension = {
    id: dimensionId,
    display_name: dimensionDisplayName,
    dimension_list: [
      {
        label: selectAliasId,
        dimension_id: dimensionId,
        from: ComplexMetricFrom.MetaMetric,
      },
    ],
  };

  return {
    publicFilterListItem,
    commonDimensionItem,
  };
};

export const getDomainMeasureObjFilterParams = (options: {
  domainMeasureObjId: string;
  dimensionId: string;
  dimensionDisplayName: string;
  filter: string[];
  selectAliasId: string;
  modelId?: string;
}) => {
  const {
    domainMeasureObjId,
    dimensionId,
    dimensionDisplayName,
    filter,
    selectAliasId,
    modelId = '',
  } = options;
  if (!domainMeasureObjId || !dimensionId || !filter.length) {
    return { publicFilterListItem: undefined, commonDimensionItem: undefined };
  }
  const publicFilterListItem: PublicFilter = {
    where_condition: {
      op: domainMeasureObjId === PSMMeasureObjID ? StrOpLike : StrOpIn, // 服务领域度量对象的查询用LIKE, 其他是IN
      val:
        domainMeasureObjId === PSMMeasureObjID
          ? filter.map(s => `${s}%`)
          : filter,
    },
    where_dimension_list: [
      {
        model_id: modelId,
        model_display_name: '',
        dimension_id: dimensionId,
        data_type: DimensionTypeString,
      },
    ],
  };
  const commonDimensionItem: CommonDimension = {
    id: dimensionId,
    display_name: dimensionDisplayName,
    dimension_list: [
      {
        label: selectAliasId,
        dimension_id: dimensionId,
        from: ComplexMetricFrom.MetaMetric,
      },
    ],
  };

  return {
    publicFilterListItem,
    commonDimensionItem,
  };
};
export const getDisplayConfig = (
  chartType: CHART_TYPE,
  options: {
    metricIdList?: string[];
    defaultValue?: string;
  } = {} as any,
): DisplayConfig => {
  const { metricIdList, defaultValue } = options;
  switch (chartType) {
    case CHART_TYPE.SHEET:
      return {
        display_type: CHART_TYPE.SHEET,
      };
    case CHART_TYPE.BAR:
      return {
        display_type: CHART_TYPE.BAR,
      };
    case CHART_TYPE.BAR_ROW:
      return {
        display_type: CHART_TYPE.BAR_ROW,
      };
    case CHART_TYPE.DOUBLE_AXIS:
      return {
        display_type: CHART_TYPE.DOUBLE_AXIS,
        double_axies: metricIdList?.reduce<DoubleAxies>(
          (prev, next, index) => {
            if (index === 0) {
              prev.left.metric_alias_id_list.push(next);
            } else {
              prev.right.metric_alias_id_list.push(next);
            }
            return prev;
          },
          {
            left: {
              metric_alias_id_list: [],
              display_type: CHART_TYPE.LINE,
              max: 0,
              min: 0,
            },
            right: {
              metric_alias_id_list: [],
              display_type: CHART_TYPE.BAR,
              max: 0,
              min: 0,
            },
          },
        ),
        display_detail: {
          invalid_data_display_type: defaultValue ?? DefaultValueZero,
          allow_zoom: false,
          display_num: true,
          show_sum: false,
          smooth_transition: false,
        },
        x_axis_config: {
          rotation_angle: 0,
          db_no_data_add: true,
        },
      };
    case CHART_TYPE.CARD:
      return {
        display_type: CHART_TYPE.CARD,
      };
    default:
      return {
        display_type: chartType,
      };
  }
};

export const getMetaMetricQueryParams = (
  data: MetricMetaData,
  options: {
    chartType: CHART_TYPE;
    showCompare?: boolean; // 是否展示环比
    filterOptions: {
      spaceType: string;
      nodeName: string;
      timeFilterInfo: TimeFilterValue;
      domainMeasureObjFilter?: DomainMeasureObjFilterValue; // 领域度量对象筛选条件
      measureDimensionId?: string; // 对比分析的度量对象的维度ID
      anchorStartTime?: string; // 趋势分析 实际时间锚点开始值, 不传默认使用时间筛选器的起始时间
    };
  },
  virtualSpaceKey?: string,
) => {
  const { chartType, filterOptions, showCompare = true } = options;
  const {
    nodeName,
    timeFilterInfo,
    measureDimensionId,
    anchorStartTime,
    domainMeasureObjFilter,
    spaceType,
  } = filterOptions;

  const {
    calc_logic_v1,
    measure_objs = [],
    domain_measure_objs = [],
    id: metricId,
    model_id: modelId,
  } = data;
  const timeDimensionId = calc_logic_v1?.where_list?.find(w =>
    timeType.includes(w.where_condition.data_type),
  )?.where_condition?.alias_id;
  const spaceDimension = [...measure_objs, ...domain_measure_objs]?.find(
    ({ id }) => id === virtualSpaceKey,
  );
  const {
    dimension_id: spaceDimensionId,
    display_name: spaceDimensionDisplayName,
  } = spaceDimension ?? {};

  const domainMeasureObjDimension = data.domain_measure_objs?.find(
    ({ id }) => id === domainMeasureObjFilter?.measureObjId,
  );

  const {
    dimension_id: domainMeasureObjDimensionId = '',
    display_name: domainMeasureObjDimensionDisplayName,
  } = domainMeasureObjDimension ?? {};

  if (
    !timeDimensionId ||
    !spaceDimensionId ||
    (domainMeasureObjFilter?.measureObjId && !domainMeasureObjDimensionId) // 配置了领域度量对象筛选，但指标没有配置领域度量对象
  ) {
    return;
  }

  const displayConfig = getDisplayConfig(chartType, {
    defaultValue: data.metric_attribute?.default_value ?? DefaultValueZero,
    metricIdList: [data.id],
  });

  const {
    publicFilterListItem: timePublicFilterListItem,
    commonDimensionItem: timeCommonDimensionItem,
    dimMetListItem: timeDimMetListItem,
    groupAliasIdListItem: timeGroupAliasIdListItem,
  } = getTimeFilterParams({
    timeFilterInfo,
    dimensionId: timeDimensionId,
    selectAliasId: metricId,
    anchorStartTime,
  });

  const {
    publicFilterListItem: spacePublicFilterListItem,
    commonDimensionItem: spaceCommonDimensionItem,
  } = getSpaceFilterParams({
    nodeName,
    dimensionId: spaceDimensionId,
    dimensionDisplayName: spaceDimensionDisplayName!,
    selectAliasId: metricId,
    op: StrOpEq,
  });

  const {
    publicFilterListItem: domainMeasureObjPublicFilterListItem,
    commonDimensionItem: domainMeasureObjCommonDimensionItem,
  } = getDomainMeasureObjFilterParams({
    domainMeasureObjId: domainMeasureObjFilter?.measureObjId ?? '',
    dimensionId: domainMeasureObjDimensionId,
    dimensionDisplayName: domainMeasureObjDimensionDisplayName!,
    filter: domainMeasureObjFilter?.filter ?? [],
    selectAliasId: metricId,
  });

  const groupAliasIdList: GroupCondition[] = [];
  const dimMetList: DimMetMetaData[] = [];

  if (
    [
      CHART_TYPE.DOUBLE_AXIS,
      CHART_TYPE.SHEET,
      CHART_TYPE.BAR,
      CHART_TYPE.BAR_ROW,
      CHART_TYPE.PIE,
    ].includes(chartType)
  ) {
    if (measureDimensionId) {
      groupAliasIdList.push({
        alias_id: measureDimensionId,
        data_type: DimensionTypeString,
      });
    } else {
      groupAliasIdList.push(timeGroupAliasIdListItem);
      dimMetList.push(timeDimMetListItem);
    }
  }

  const compareYearPeriodList: CompareYearPeriod[] = [];
  if ([CHART_TYPE.CARD, CHART_TYPE.SHEET].includes(chartType)) {
    compareYearPeriodList.push({
      time_alias_id: timeDimensionId,
      mode: ['period'],
      reverse_default_color:
        data.metric_attribute?.analysis_direction !== 'Negative', // 负向指标红涨绿跌,正向绿涨红跌
      show_diff_value: true,
      show_diff_ratio: true,
      metric_alias_id: metricId,
    });
  }

  const params = {
    ...baseParams,
    display_config: displayConfig,
    query_info: {
      model_id: modelId,
      from: 'meta_metric',
      proportion_ratio:
        data.metric_attribute?.metric_use === MetricUseStatistics, // 统计类型指标展示占比
      where_list: [],
      having_list: [],
      select_alias_id_list: [
        { alias_id: metricId, from: 'meta_metric', label: metricId },
      ],
      group_alias_id_list: groupAliasIdList,
      dim_met_list: dimMetList,
      common_dimension_list: [
        timeCommonDimensionItem,
        spaceCommonDimensionItem,
        domainMeasureObjCommonDimensionItem,
      ].filter(Boolean),
      compare_year_period_list: showCompare ? compareYearPeriodList : [],
      std_granularity: timeDimMetListItem.granularity,
    } as QueryInfo,
    public_filter_list: [
      timePublicFilterListItem,
      spacePublicFilterListItem,
      domainMeasureObjPublicFilterListItem,
    ].filter(Boolean) as PublicFilter[],
  };

  if (displayConfig?.display_type === CHART_TYPE.BAR_ROW) {
    params.query_info.top_n = {
      type: 'by_result',
      measure_alias_id: metricId,
      dim_alias_id_list: [],
      limit: 100,
      order: 'asc',
      no_display_other: true,
    };
  }

  // eslint-disable-next-line consistent-return
  return params;
};

export const getComplexMetaMetricQueryParams = async (
  data: ComplexMetricMetaData,
  options: {
    chartType: CHART_TYPE;
    showCompare?: boolean; // 是否展示环比
    filterOptions: {
      spaceType: string;
      nodeName: string;
      timeFilterInfo: TimeFilterValue;
      domainMeasureObjFilter?: DomainMeasureObjFilterValue;
      measureDimensionId?: string; // 对比分析的度量对象ID
      anchorStartTime?: string; // 趋势分析 实际时间锚点开始值, 不传默认使用时间筛选器的起始时间
    };
  },
  GetComplexMetricById: (params: {
    version: number;
    complex_metric_id: string;
  }) => any,
  virtualSpaceKey?: string,
) => {
  const { filterOptions, chartType, showCompare = true } = options;
  const {
    nodeName,
    timeFilterInfo,
    measureDimensionId,
    anchorStartTime,
    domainMeasureObjFilter,
    spaceType,
  } = filterOptions;

  const {
    common_dimension_list,
    atom_complex_metric_list,
    atom_metric_caliber_list,
    measure_objs = [],
    domain_measure_objs = [],
    id: metricId,
  } = data;
  const timeDimensionId = common_dimension_list?.find(
    ({ display_name }) => display_name === '时间',
  )?.id;
  const spaceDimension = [...measure_objs, ...domain_measure_objs]?.find(
    ({ id }) => id === virtualSpaceKey,
  );

  const {
    dimension_id: spaceDimensionId,
    display_name: spaceDimensionDisplayName,
  } = spaceDimension ?? {};

  if (!timeDimensionId || !spaceDimensionId) {
    return;
  }

  const {
    publicFilterListItem: timePublicFilterListItem,
    dimMetListItem: timeDimMetListItem,
    groupAliasIdListItem: timeGroupAliasIdListItem,
  } = getTimeFilterParams({
    timeFilterInfo,
    dimensionId: timeDimensionId,
    selectAliasId: metricId,
    modelId: metricId,
    anchorStartTime,
  });

  const { publicFilterListItem: spacePublicFilterListItem } =
    getSpaceFilterParams({
      nodeName,
      dimensionId: spaceDimensionId,
      dimensionDisplayName: spaceDimensionDisplayName!,
      selectAliasId: metricId,
      modelId: metricId,
      op: StrOpEq,
    });

  const dimension = data.domain_measure_objs?.find(
    ({ id }) =>
      id ===
      (domainMeasureObjFilter as DomainMeasureObjFilterValue)?.measureObjId,
  );
  // 配置了领域度量对象筛选，但指标没有配置领域度量对象
  if (
    (domainMeasureObjFilter as DomainMeasureObjFilterValue)?.measureObjId &&
    !dimension?.dimension_id
  ) {
    return;
  }
  const { publicFilterListItem: domainMeasureObjPublicFilterListItem } =
    getDomainMeasureObjFilterParams({
      domainMeasureObjId:
        (domainMeasureObjFilter as DomainMeasureObjFilterValue)?.measureObjId ??
        '',
      dimensionId: dimension?.dimension_id ?? '',
      dimensionDisplayName: dimension?.dimension_name ?? '',
      filter:
        (domainMeasureObjFilter as DomainMeasureObjFilterValue)?.filter ?? [],
      modelId: metricId,
      selectAliasId: metricId,
    });

  // TODO:后端返回的评分指标数据有问题，需要前端再次调评分指标详情接口更新数据,后续需要优化对齐
  const transformAtomMetricCaliberList = await Promise.all(
    atom_metric_caliber_list.map(async item => {
      const { complex_meta_data } = item;
      if (complex_meta_data) {
        const res = await GetComplexMetricById({
          version: API_V1,
          complex_metric_id: complex_meta_data?.id || '',
        }).catch((err: any) => console.log(err));
        return {
          ...item,
          complex_meta_data: {
            ...complex_meta_data,
            id: res?.data?.complex_metric_list?.[0]?.id,
          },
        };
      }
      return item;
    }) || [],
  );

  const displayConfig = getDisplayConfig(chartType, {
    metricIdList: [
      ...(atom_complex_metric_list?.map(({ id }) => id) ?? []),
      ...(transformAtomMetricCaliberList?.map(
        ({ metric_data, complex_meta_data }) =>
          (complex_meta_data || metric_data)?.id ?? '',
      ) ?? []),
    ],
    defaultValue: data.metric_attribute?.default_value ?? DefaultValueZero,
  });

  const compareYearPeriodList: CompareYearPeriod[] = [];
  if (
    [
      CHART_TYPE.CARD,
      CHART_TYPE.SHEET,
      CHART_TYPE.BAR,
      CHART_TYPE.BAR_ROW,
    ].includes(chartType)
  ) {
    compareYearPeriodList.push({
      time_alias_id: timeDimensionId,
      mode: ['period'],
      reverse_default_color:
        data.metric_attribute?.analysis_direction !== 'Negative', // 负向指标红涨绿跌,正向绿涨红跌
      show_diff_value: true,
      show_diff_ratio: true,
      metric_alias_id: atom_complex_metric_list?.[0].id ?? '',
    });
  }

  const selectAliasIdList: SelectCondition[] = [
    ...(atom_complex_metric_list
      ?.slice(0, 1) // 评分指标可能是评分指标嵌套评分指标，会存在多个，实际展示取第一个就可以
      ?.map(({ complex_type, data_unit, id }) => ({
        alias_id: id,
        common_data_unit: data_unit,
        from: complex_type!,
        label: id,
      })) ?? []),
  ];
  if ([CHART_TYPE.DOUBLE_AXIS, CHART_TYPE.SHEET].includes(chartType)) {
    selectAliasIdList.push(
      ...(transformAtomMetricCaliberList?.map(
        ({ metric_data, complex_meta_data, label }) => ({
          label,
          from: metric_data ? 'meta_metric' : 'formula_meta',
          alias_id: (metric_data || complex_meta_data)?.id ?? '',
        }),
      ) ?? []),
    );
  }

  const groupAliasIdList: GroupCondition[] = [];
  const dimMetList: DimMetMetaData[] = [];

  if (
    [
      CHART_TYPE.DOUBLE_AXIS,
      CHART_TYPE.SHEET,
      CHART_TYPE.BAR,
      CHART_TYPE.BAR_ROW,
      CHART_TYPE.PIE,
    ].includes(chartType)
  ) {
    if (measureDimensionId) {
      groupAliasIdList.push({
        alias_id: measureDimensionId,
        data_type: DimensionTypeString,
      });
    } else {
      groupAliasIdList.push(timeGroupAliasIdListItem);
      dimMetList.push(timeDimMetListItem);
    }
  }

  const params = {
    ...baseParams,
    display_config: displayConfig,
    query_info: {
      model_id: metricId,
      from: 'score_metric',
      proportion_ratio:
        data.metric_attribute?.metric_use === MetricUseStatistics,
      where_list: [],
      having_list: [],
      select_alias_id_list: selectAliasIdList,
      group_alias_id_list: groupAliasIdList,
      dim_met_list: dimMetList,
      common_dimension_list: (common_dimension_list as any) || [],
      compare_year_period_list: showCompare ? compareYearPeriodList : [],
      std_granularity: timeDimMetListItem.granularity,
    } as QueryInfo,
    public_filter_list: [
      timePublicFilterListItem,
      spacePublicFilterListItem,
      domainMeasureObjPublicFilterListItem,
    ].filter(Boolean) as PublicFilter[],
  };

  if (displayConfig?.display_type === CHART_TYPE.BAR_ROW) {
    params.query_info.top_n = {
      type: 'by_result',
      measure_alias_id: selectAliasIdList[0].alias_id,
      dim_alias_id_list: [],
      limit: 100,
      order: 'asc',
      no_display_other: true,
    };
  }

  // eslint-disable-next-line consistent-return
  return params;
};

export const tweakTrendAnalysisHack = (
  isMetaMetric: boolean,
  query: QueryRequest,
  metricDetail: MetricMetaData,
): QueryRequest => {
  const result = { ...query };

  // - public_filter_list 内容同步到 queryInfo 中的 where_list 中
  result.query_info.where_list = result.public_filter_list?.map((item: any) => {
    const result = {
      where_condition: {
        ...item.where_condition,
        alias_id: item.where_dimension_list?.[0]?.dimension_id,
      },
    };
    if (!isMetaMetric) {
      // 复合元指标添加固定值
      (result.where_condition as any).from = 'common_dimension';
    }

    return result;
  }) as any;

  // 如果是元指标
  // 补充common_dimension_list，使用指标详情接口里的analysis_info中的analysis_dim_ids进行补充
  if (isMetaMetric) {
    result.query_info.common_dimension_list = result.query_info
      .common_dimension_list?.length
      ? [
          ...result.query_info.common_dimension_list,
          ...(metricDetail.analysis_info?.analysis_dim_ids ?? []).map(item => {
            return {
              // analysis_dim_ids 中的 dim_id
              id: item.dim_id,
              // analysis_dim_ids 中的display_name
              display_name: item.display_name,
              // 只有一项，太他妈 hack 了
              dimension_list: [
                {
                  // 元指标 id
                  label: metricDetail.id,
                  // analysis_dim_ids 中的 dim_id
                  dimension_id: item.dim_id,
                  // 元指标：3
                  from: 3,
                },
              ],
            } as CommonDimension;
          }),
        ]
      : result.query_info.common_dimension_list;
  }

  return result;
};

export const needGenRuleText = (type: string | undefined): boolean => {
  return type == null || [DisplayTypeStory].includes(type);
};

export const getMetricDisplayType = (type: string | undefined): CHART_TYPE => {
  switch (type) {
    case DisplayTypeBar:
      return CHART_TYPE.BAR;
    case DisplayTypeBarRow:
      return CHART_TYPE.BAR_ROW;
    case DisplayTypeDoubleAxis:
      return CHART_TYPE.DOUBLE_AXIS;
    case DisplayTypePie:
      return CHART_TYPE.PIE;
    case DisplayTypeSheet:
      return CHART_TYPE.SHEET;
    case DisplayTypeStory:
      return CHART_TYPE.DOUBLE_AXIS;
    default:
      return CHART_TYPE.DOUBLE_AXIS;
  }
};

/* 不同图表类型的指标请求的参数会有细微差异 */
export const getQueryRequestArgsByMetric = (
  metric: any,
  metricData: MetricMetaData | ComplexMetricMetaData,
  timeFilterInfo: TimeFilterValue,
) => {
  /* FIXME: 老逻辑会优先使用 model 中的数据，很混乱，先始终从 url 获取看看后续会不会有 bug */

  switch (metric.display_type) {
    case DisplayTypeBar:
    case DisplayTypeBarRow:
      return {
        queryInfo: {
          timeFilterInfo,
          anchorStartTime: timeFilterInfo.range[0],
          measureDimensionId: metric.default_dim_id,
        },
        options: { showCompare: false },
      };
    case DisplayTypeDoubleAxis:
      /* 趋势图需要往前推算时间范围 */
      timeFilterInfo.range = getDefaultTimeRange(
        timeFilterInfo.range,
        timeFilterInfo.granularity,
      );
      return {
        queryInfo: { timeFilterInfo, anchorStartTime: timeFilterInfo.range[0] },
        options: { hackTrendAnalysis: true },
      };
    case DisplayTypePie:
      return {
        queryInfo: {
          timeFilterInfo,
          anchorStartTime: timeFilterInfo.range[0],
          measureDimensionId: metric.default_dim_id,
        },
      };
    case DisplayTypeSheet:
      return {
        queryInfo: {
          timeFilterInfo,
          anchorStartTime: timeFilterInfo.range[0],
          measureDimensionId:
            metricData.analysis_info?.analysis_dim_ids?.[0]?.dim_id,
        },
      };
    case DisplayTypeStory:
    default: {
      /* 趋势图需要往前推算时间范围 */
      timeFilterInfo.range = getDefaultTimeRange(
        timeFilterInfo.range,
        timeFilterInfo.granularity,
      );

      return {
        queryInfo: {
          timeFilterInfo,
          anchorStartTime: timeFilterInfo.range[0],
          customParams: {
            baselines: [],
            /* 是否生成结论 */
            gen_rule_text: needGenRuleText(metric.display_type),
          },
        },
      };
    }
  }
};
