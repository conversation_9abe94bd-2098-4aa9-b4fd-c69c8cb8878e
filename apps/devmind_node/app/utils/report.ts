/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable no-new-func */
/* eslint-disable @typescript-eslint/no-implied-eval */

import type {
  EditorInfo,
  MetricMetaInfo,
} from '@quality/common-apis/src/apis/api/insight_report/report_model';

import type { ProductInfo } from '@quality/common-apis/src/apis/api/product/product_model';
import { castArray } from 'lodash';
import { SpaceType } from './metric';

export const ELEMENT_METRIC_CARD_AREA = 'metric-card';

/**
 * 优先从布局信息中获取指标信息，备选从遗留字段中获取
 */
export const getMetrics = (editorInfo?: EditorInfo) => {
  if (!editorInfo) {
    return null;
  }

  const { editor_content: contentString, metric_metas: legacyMetricList } =
    editorInfo;

  const content = JSON.parse(contentString);
  const metricsFromContent = content
    .filter((c: any) => c.type === ELEMENT_METRIC_CARD_AREA)
    .flatMap((c: any) => c.metrics as MetricMetaInfo)
    .filter(Boolean);
  const metricsFromLegacy = legacyMetricList;

  let metrics = metricsFromContent;
  // 历史问题，有些报告的布局信息中不包含指标卡的 parent_id、metric_type 等信息
  // 如果是这种情况则以历史的 metrics_meta 列表为准
  if (
    !metrics.length ||
    metrics.length < legacyMetricList.length ||
    !metrics?.every((item: { parent_id: any }) => item?.parent_id)
  ) {
    metrics = metricsFromLegacy;
  }

  return metrics;
};

export const transformNodeTree = (tree: ProductInfo[], spaceType?: string) => {
  const nodeIdMap: Record<string, ProductInfo & { spaceType?: string }> = {};
  const flatTreeList: ProductInfo[] = [];
  const loop = (treeData: ProductInfo[]) => {
    treeData.forEach(node => {
      nodeIdMap[node.node_id] = { ...node, spaceType };
      if (node.children?.length) {
        loop(node.children);
      }
      flatTreeList.push(node);
    });
  };
  loop(castArray(tree));
  return { flatTree: flatTreeList, treeIdRecord: nodeIdMap };
};
