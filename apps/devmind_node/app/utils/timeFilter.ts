import dayjs, { Dayjs, ManipulateType, QUnitType } from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import type { DimMetMetaData } from '@quality/common-apis/src/apis/engine/engine';
import type {
  PublicFilter,
  GroupCondition,
} from '@quality/common-apis/src/apis/api/query/query';
import {
  TimeGranularityAnnualPerformance,
  TimeGranularityDay,
  TimeGranularityDoubleMonth,
  TimeGranularityDoubleWeek,
  TimeGranularityMidYearPerformance,
  TimeGranularityMonth,
  TimeGranularityQuarter,
  TimeGranularityWeek,
  TimeGranularityHalfYear,
  TimeGranularityYear,
  StrOpBetween,
  DimensionTypeTimestamp,
  TimeGranularity as API_TimeGranularity,
  DimensionTypeDATE,
  DimensionTypedate,
  DimensionTypeUnixTimestamp,
  ComplexMetricFrom,
} from '../constants';

dayjs.extend(quarterOfYear);
dayjs.extend(isSameOrAfter);

export const format = 'YYYY-MM-DD';
export const startTimeFormat = 'YYYY-MM-DD 00:00:00';
export const endTimeFormat = 'YYYY-MM-DD 23:59:59';

export enum TimeCycle {
  Standard = 'standard',
  Custom = 'custom',
}

export const timeType = [
  DimensionTypeTimestamp,
  DimensionTypeDATE,
  DimensionTypedate,
  DimensionTypeUnixTimestamp,
]; // 时间类型

// 对应到Dayjs里的类型
export enum TimeGranularity {
  Day = 'd',
  Week = 'w',
  DoubleWeek = 'dw',
  Month = 'M',
  DoubleMonth = 'DM',
  Quarter = 'Q',
  HalfYear = 'HY',
  Year = 'y',
  MidYearPerformance = 'myp',
  AnnualPerformance = 'ap',
  All = '',
}

export const TimeGranularityText: Record<TimeGranularity, string> = {
  [TimeGranularity.Day]: '日',
  [TimeGranularity.Week]: '周',
  [TimeGranularity.DoubleWeek]: '双周',
  [TimeGranularity.Month]: '月',
  [TimeGranularity.DoubleMonth]: '双月',
  [TimeGranularity.Quarter]: '季度',
  [TimeGranularity.HalfYear]: '半年',
  [TimeGranularity.Year]: '年',
  [TimeGranularity.MidYearPerformance]: '半年绩效周期',
  [TimeGranularity.AnnualPerformance]: '全年绩效周期',
  [TimeGranularity.All]: '全部统计粒度',
};

export const TimeCycleText: Record<TimeCycle, string> = {
  [TimeCycle.Standard]: '标准周期',
  [TimeCycle.Custom]: '自定义周期',
};

// 转换时间粒度字段
export const getTimeGranularity = (
  granularity_type: string,
  defaultValue: string = TimeGranularity.DoubleMonth,
) =>
  (Object.entries(TimeGranularityText).find(
    ([_, value]) => value === granularity_type,
  )?.[0] as TimeGranularity) ?? defaultValue;

export const getTimeCycle = (
  granularity_cycle: string,
  defaultValue: string = TimeCycle.Standard,
) =>
  (Object.entries(TimeCycleText).find(
    ([_, value]) => value === granularity_cycle,
  )?.[0] as TimeCycle) ?? defaultValue;

export interface TimeFilterValue {
  granularity: TimeGranularity;
  cycle: TimeCycle;
  range: string[];
}

export const isDisabledDateOfDoubleMonth = (day: Dayjs) => {
  const date = day.date();
  return date < 2 || date > 28;
};

export const getDayjsUnit = (
  granularity: TimeGranularity,
): ManipulateType | QUnitType => {
  switch (granularity) {
    case TimeGranularity.Day:
      return 'd';
    case TimeGranularity.Week:
    case TimeGranularity.DoubleWeek:
      return 'w';
    case TimeGranularity.Month:
    case TimeGranularity.DoubleMonth:
    case TimeGranularity.AnnualPerformance:
    case TimeGranularity.MidYearPerformance:
    case TimeGranularity.HalfYear:
      return 'M';
    case TimeGranularity.Quarter:
      return 'Q';
    case TimeGranularity.Year:
      return 'y';
    default:
      return 'd';
  }
};

// 生成对应时间类型所在的时间范围
export const getTimeRange = (
  originDay: Dayjs,
  options: {
    granularity: TimeGranularity;
    standard?: boolean; // 是否按标准周期计算
    duration?: number; // 默认0计算当前周期, 1,2,3...传入日期所在的周期往后加1,2,3...周期，负数同理减
  },
) => {
  const {
    granularity = TimeGranularity.Day,
    standard = false,
    duration = 0,
  } = options;
  const isDoubleMonth = granularity === TimeGranularity.DoubleMonth;
  const isDoubleWeek = granularity === TimeGranularity.DoubleWeek;
  const isHalfYear = granularity === TimeGranularity.HalfYear;
  const isMidPerformance = granularity === TimeGranularity.MidYearPerformance;
  const isAnnualPerformance = granularity === TimeGranularity.AnnualPerformance;

  // TODO: 没找到dayjs全局配置的办法，和arcdesign dayjs会互相影响，暂时独立设置
  dayjs.Ls[dayjs.locale()].weekStart = 1;
  let day = dayjs(originDay);
  // 自定义双月起始时间范围2-28，不在范围内则取最近的有效值
  if (isDoubleMonth && !standard && isDisabledDateOfDoubleMonth(day)) {
    day = day.date() === 1 ? day.date(2) : day.date(28);
  }

  const unit = getDayjsUnit(granularity) as ManipulateType;

  let multiple = 1; // 不同时间粒度换算实际duration的倍数
  // 双月周期乘2
  if (isDoubleMonth || isDoubleWeek) {
    multiple = 2;
  }
  // 半年绩效乘6
  if (isMidPerformance || isHalfYear) {
    multiple = 6;
  }
  // 全年绩效乘12
  if (isAnnualPerformance) {
    multiple = 12;
  }
  const realDuration = multiple * duration; // 实际计算duration

  let startTime = day;
  let endTime = day;

  if (standard) {
    if (isDoubleMonth && day.month() % 2 !== 0) {
      startTime = day.subtract(1, 'M');
    } else if (isMidPerformance) {
      const year = day.year();
      if (day.isBefore(`${year}-03-01`)) {
        startTime = dayjs(`${year - 1}-09-01`);
      } else if (day.isSameOrAfter(`${year}-09-01`)) {
        startTime = dayjs(`${year}-09-01`);
      } else {
        startTime = dayjs(`${year}-03-01`);
      }
    } else if (isHalfYear) {
      const year = day.year();
      if (day.isBefore(`${year}-07-01`)) {
        startTime = dayjs(`${year}-01-01`);
      } else {
        startTime = dayjs(`${year}-07-01`);
      }
    } else if (isAnnualPerformance) {
      const year = day.year();
      if (day.isBefore(`${year}-03-01`)) {
        startTime = dayjs(`${year - 1}-03-01`);
      } else {
        startTime = dayjs(`${year}-03-01`);
      }
    }
    startTime = startTime.add(realDuration, unit).startOf(unit);
  } else {
    startTime = day.add(realDuration, unit);
    // 自定义年精确到月,日默认以1号开始
    if (granularity === TimeGranularity.Year || isHalfYear) {
      startTime = day.add(realDuration, unit).startOf('M');
    }
  }

  endTime = startTime.add(multiple, unit).subtract(1, 'd');

  // 重置dayjs配置，避免影响全局其他地方使用
  dayjs.Ls[dayjs.locale()].weekStart = 0;

  return [startTime.format(startTimeFormat), endTime.format(endTimeFormat)] as
    | [startTime: string, endTime: string]
    | [];
};

// 获取趋势分析图表不同时间粒度的默认筛选时间范围
// 比如 周 需要往前推 7 周
export const getDefaultTimeRange = (
  range: string[],
  granularity: TimeGranularity,
) => {
  if (!range.length) {
    return [];
  }

  const startDay = dayjs(range[0]);
  const endDay = dayjs(range[1]);

  let dayjsTimeRange = [startDay, endDay];

  switch (granularity) {
    case TimeGranularity.Day:
      dayjsTimeRange = [startDay.subtract(7, 'd'), startDay];
      break;
    case TimeGranularity.Week:
      dayjsTimeRange = [startDay.subtract(7, 'w'), endDay];
      break;
    case TimeGranularity.DoubleWeek:
      dayjsTimeRange = [startDay.subtract(14, 'w'), endDay];
      break;
    case TimeGranularity.Month:
    case TimeGranularity.DoubleMonth:
      dayjsTimeRange = [startDay.subtract(12, 'M'), endDay];
      break;
    case TimeGranularity.Quarter:
      dayjsTimeRange = [startDay.subtract(3, 'Q'), endDay];
      break;
    case TimeGranularity.Year:
      dayjsTimeRange = [startDay.subtract(3, 'y'), endDay];
      break;
    case TimeGranularity.MidYearPerformance:
    case TimeGranularity.HalfYear:
      dayjsTimeRange = [startDay.subtract(18, 'M'), endDay];
      break;
    case TimeGranularity.AnnualPerformance:
      dayjsTimeRange = [startDay.subtract(3, 'y'), endDay];
      break;
    default:
      break;
  }

  return dayjsTimeRange.map(item => item.format('YYYY-MM-DD'));
};

export const getTimeGranularityId = (value: TimeFilterValue) => {
  if (!value) {
    return TimeGranularity.Day;
  }
  const { range, granularity, cycle } = value;
  const startDay = range[0];
  const isStandard = cycle === TimeCycle.Standard;

  switch (granularity) {
    case TimeGranularity.Day:
      return API_TimeGranularity.Day;
    case TimeGranularity.Week:
      if (isStandard) {
        return API_TimeGranularity.Week;
      } else {
        const weekDay = dayjs(startDay).day();
        return (
          [
            API_TimeGranularity.MonToSun,
            API_TimeGranularity.TueToMon,
            API_TimeGranularity.WedToTue,
            API_TimeGranularity.ThurToWed,
            API_TimeGranularity.FriToThur,
            API_TimeGranularity.SatToFri,
            API_TimeGranularity.SunToSat,
          ].find(item => item === (weekDay || 7) + 5) ?? API_TimeGranularity.Day
        ); // 周日是0
      }
    case TimeGranularity.DoubleWeek:
      return API_TimeGranularity.BiWeekly;
    case TimeGranularity.Month:
      return isStandard
        ? API_TimeGranularity.Month
        : API_TimeGranularity.CustomMonth;
    case TimeGranularity.DoubleMonth:
      return isStandard
        ? API_TimeGranularity.BiMonthly
        : API_TimeGranularity.CustomBiMonthly;
    case TimeGranularity.Quarter:
      return API_TimeGranularity.Quarter;
    case TimeGranularity.Year:
      return (
        [
          API_TimeGranularity.Jan,
          API_TimeGranularity.Feb,
          API_TimeGranularity.Mar,
          API_TimeGranularity.Apr,
          API_TimeGranularity.May,
          API_TimeGranularity.Jun,
          API_TimeGranularity.Jul,
          API_TimeGranularity.Aug,
          API_TimeGranularity.Sept,
          API_TimeGranularity.Oct,
          API_TimeGranularity.Nov,
          API_TimeGranularity.Dec,
        ].find(
          item => item === dayjs(startDay).month() + API_TimeGranularity.Jan,
        ) ?? API_TimeGranularity.Day
      );
    case TimeGranularity.MidYearPerformance:
      return API_TimeGranularity.HalfMar;
    case TimeGranularity.HalfYear:
      return (
        [
          API_TimeGranularity.HalfJan,
          API_TimeGranularity.HalfFeb,
          API_TimeGranularity.HalfMar,
          API_TimeGranularity.HalfApr,
          API_TimeGranularity.HalfMay,
          API_TimeGranularity.HalfJun,
        ].find(
          item =>
            item ===
            (dayjs(startDay).month() % 6) + API_TimeGranularity.HalfJan,
        ) ?? API_TimeGranularity.HalfJan
      );
    case TimeGranularity.AnnualPerformance:
      return API_TimeGranularity.Mar;
    default:
      return API_TimeGranularity.Day;
  }
};

export const getTimeGranularityTypeByApiIdInStandard = (
  range: string[],
  timeGranularityId: number,
): TimeGranularity => {
  switch (timeGranularityId) {
    case API_TimeGranularity.Day:
      return TimeGranularity.Day;
    case API_TimeGranularity.Week:
      return TimeGranularity.Week;
    case API_TimeGranularity.BiWeekly:
      return TimeGranularity.DoubleWeek;
    case API_TimeGranularity.Month:
      return TimeGranularity.Month;
    case API_TimeGranularity.BiMonthly:
      return TimeGranularity.DoubleMonth;
    case API_TimeGranularity.Quarter:
      return TimeGranularity.Quarter;
    case API_TimeGranularity.Apr:
    case API_TimeGranularity.May:
    case API_TimeGranularity.Jun:
    case API_TimeGranularity.Jul:
    case API_TimeGranularity.Aug:
    case API_TimeGranularity.Sept:
    case API_TimeGranularity.Oct:
    case API_TimeGranularity.Nov:
    case API_TimeGranularity.Dec:
      return TimeGranularity.Year;
    case API_TimeGranularity.HalfMar:
      return TimeGranularity.MidYearPerformance;
    case API_TimeGranularity.HalfJan:
      return TimeGranularity.HalfYear;
    case API_TimeGranularity.Jan: {
      if (
        dayjs(range[0]).month() + API_TimeGranularity.Jan ===
        timeGranularityId
      ) {
        return TimeGranularity.Year;
      }
      return TimeGranularity.AnnualPerformance;
    }
    case API_TimeGranularity.Feb: {
      if (
        dayjs(range[0]).month() + API_TimeGranularity.Feb ===
        timeGranularityId
      ) {
        return TimeGranularity.Year;
      }
      return TimeGranularity.AnnualPerformance;
    }
    case API_TimeGranularity.Mar: {
      if (
        dayjs(range[0]).month() + API_TimeGranularity.Mar ===
        timeGranularityId
      ) {
        return TimeGranularity.Year;
      }
      return TimeGranularity.AnnualPerformance;
    }
    default:
      return TimeGranularity.Day;
  }
};

export const getTimeFilterParams = (options: {
  timeFilterInfo: TimeFilterValue;
  dimensionId: string;
  modelId?: string;
  selectAliasId: string;
  anchorStartTime?: string;
}) => {
  const {
    timeFilterInfo,
    dimensionId,
    modelId = '',
    selectAliasId,
    anchorStartTime = timeFilterInfo.range?.[0] ?? '',
  } = options;
  const { range } = timeFilterInfo;
  const startDay = range[0];
  const endDay = range[1] || startDay; // 时间类型是天,则数组可能只有一项,开始和结束是同一天

  const timeGranularityId = getTimeGranularityId(timeFilterInfo);

  const publicFilterListItem: PublicFilter = {
    where_condition: {
      op: StrOpBetween,
      val: [
        dayjs(startDay).format(startTimeFormat),
        dayjs(endDay).format(endTimeFormat),
      ],
    },
    where_dimension_list: [
      {
        model_id: modelId, // 评分指标需要传指标id，元指标为空
        model_display_name: '',
        dimension_id: dimensionId,
        data_type: DimensionTypeTimestamp,
      },
    ],
  };

  const groupAliasIdListItem: GroupCondition = {
    alias_id: `time_granularity_${timeGranularityId}_${dimensionId}`,
    data_type: DimensionTypeTimestamp,
  };

  const dimMetListItem: DimMetMetaData = {
    id: dimensionId,
    alias_id: `time_granularity_${timeGranularityId}_${dimensionId}`,
    caliber_sql: '',
    data_type: DimensionTypeTimestamp,
    source_type: 'aggr',
    granularity: {
      time_granularity_id: `${timeGranularityId}`,
      time_granularity_consts: getTimeFilterConsts(timeFilterInfo.granularity),
    },
  };
  if (timeGranularityId === API_TimeGranularity.BiWeekly) {
    dimMetListItem.granularity!.biweekly_begin_time =
      dayjs(anchorStartTime).format(format);
  }
  if (timeGranularityId === API_TimeGranularity.CustomMonth) {
    dimMetListItem.granularity!.custom_month_begin_value =
      dayjs(anchorStartTime).date();
  }
  if (timeGranularityId === API_TimeGranularity.CustomBiMonthly) {
    dimMetListItem.granularity!.custom_bimonthly_begin_time =
      dayjs(anchorStartTime).format(format);
  }

  const commonDimensionItem = {
    id: dimensionId,
    display_name: '时间',
    dimension_list: [
      {
        label: selectAliasId,
        dimension_id: dimensionId,
        from: ComplexMetricFrom.MetaMetric,
      },
    ],
  };

  return {
    publicFilterListItem,
    groupAliasIdListItem,
    dimMetListItem,
    commonDimensionItem,
  };
};

export const getTimeFilterConsts = (granularity: TimeGranularity) => {
  switch (granularity) {
    case TimeGranularity.Day:
      return TimeGranularityDay;
    case TimeGranularity.Week:
      return TimeGranularityWeek;
    case TimeGranularity.DoubleWeek:
      return TimeGranularityDoubleWeek;
    case TimeGranularity.Month:
      return TimeGranularityMonth;
    case TimeGranularity.DoubleMonth:
      return TimeGranularityDoubleMonth;
    case TimeGranularity.Quarter:
      return TimeGranularityQuarter;
    case TimeGranularity.HalfYear:
      return TimeGranularityHalfYear;
    case TimeGranularity.Year:
      return TimeGranularityYear;
    case TimeGranularity.MidYearPerformance:
      return TimeGranularityMidYearPerformance;
    case TimeGranularity.AnnualPerformance:
      return TimeGranularityAnnualPerformance;
    default:
      return '';
  }
};
