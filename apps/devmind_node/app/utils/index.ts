import type { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import type { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';

import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import { URI_PERFIX_ONLINE } from '../constants';

export {
  API_V2,
  API_V1,
  AUTH_CODE,
  URI_PERFIX_BOE,
  MeasureObjectBusinessTree,
  MeasureObjectDepartmentTree,
  MeasureObjectReportTree,
  RequestTypeExecuteQuery,
  RequestTypeDevmindQuery,
  MetricTypeMeta,
  MetricTypeComplexMeta,
  PeopleMeasureObjID,
  CodeRepoMeasureObjID,
  ProjectMeasureObjID,
  PSMMeasureObjID,
  APPMeasureObjID,
  ProductMeasureObjID,
  MeasureTypeBussiness,
  CHART_TYPE,
  URI_PERFIX_ONLINE,
  StoryTypeComplexMeta,
  StoryTypeMeta,
} from '../constants';

export * from './timeFilter';
export * from './report';
export * from './metric';

dayjs.extend(quarterOfYear);
dayjs.extend(isSameOrAfter);

export const headers = { 'Content-Type': 'application/json' };
export const method = 'GET';
export const credentials = 'same-origin';

export type MetricData = MetricMetaData | ComplexMetricMetaData;
export { MetricMetaData };

export function queryStringify(
  params: { [key: string]: any },
  inBody?: boolean,
) {
  const items: string[] = [];

  function itemStringify(obj: any, prefix: string) {
    const type = Object.prototype.toString.call(obj);
    if (type === '[object Array]') {
      obj.forEach((item: any, key: string) => {
        itemStringify(item, `${prefix}[${key}]`);
      });
    } else if (type === '[object Object]') {
      for (const key in obj) {
        itemStringify(obj[key], `${prefix}[${key}]`);
      }
    } else if (type === '[object Date]') {
      items.push(`${prefix}=${obj.toISOString()}`);
    } else if (type === '[object Null]') {
      items.push(`${prefix}=`);
    } else if (type !== '[object Undefined]') {
      items.push(`${prefix}=${encodeURIComponent(obj)}`);
    }
  }

  for (const k in params) {
    itemStringify(params[k], k);
  }

  const str = items.join('&');
  return str && !inBody ? `?${str}` : str;
}

export const getUrlPerfix = () => {
  return URI_PERFIX_ONLINE;
};
