export const API_V2 = 2;

export const API_V1 = 1;

export const AUTH_CODE = '120356';

export const DOUBLE_AXIS = 'double_axis';

export const URI_PERFIX_ONLINE =
  process.env.QUALITY_API_HOST ||
  'http://*************:32080/devinsight/devmind-open';
export const URI_PERFIX_BOE = 'http://devmind-boe.bytedance.net';

export const MetricTypeMeta = 'meta';
export const MetricTypeComplexMeta = 'complexMeta';

export const DefaultValueZero = '0';
export const DefaultValueNA = 'NA';
export const DefaultValue100 = '100%';
export const DefaultValueMap = {
  [DefaultValueZero]: '0',
  [DefaultValueNA]: 'NA',
  [DefaultValue100]: '1',
};
export const MetricUseStatistics = 'Statistics';
export const StrOpBetween = 'BETWEEN';
export const StrOpEq = '=';
export const DimensionTypeTimestamp = 'TIMESTAMP';
export const DimensionTypeUnixTimestamp = 'UNIX_TIMESTAMP';
export const DimensionTypedate = 'date';
export const DimensionTypeDATE = 'DATE';
export const DimensionTypemonth = 'month';
export const DimensionTypeMONTH = 'MONTH';
export const DimensionTypeString = 'STRING';

export const TimeGranularity = {
  Day: 1,
  Week: 2,
  Month: 3,
  BiMonthly: 4,
  CustomWeek: 5,
  MonToSun: 6,
  TueToMon: 7,
  WedToTue: 8,
  ThurToWed: 9,
  FriToThur: 10,
  SatToFri: 11,
  SunToSat: 12,
  CustomWeekEnd: 13,
  BiWeekly: 14,
  UnixSecondsDay: 15,
  Hour: 16,
  Minute: 17,
  UnixSecondsHour: 18,
  UnixSecondsMinute: 19,
  Minute1: 20,
  Minute5: 21,
  Minute10: 22,
  UnixSecondsMinute1: 23,
  UnixSecondsMinute5: 24,
  UnixSecondsMinute10: 25,
  CustomHalfYear: 26,
  HalfJan: 27,
  HalfFeb: 28,
  HalfMar: 29,
  HalfApr: 30,
  HalfMay: 31,
  HalfJun: 32,
  CustomYear: 33,
  Jan: 34,
  Feb: 35,
  Mar: 36,
  Apr: 37,
  May: 38,
  Jun: 39,
  Jul: 40,
  Aug: 41,
  Sept: 42,
  Oct: 43,
  Nov: 44,
  Dec: 45,
  CustomMonth: 46,
  CustomBiMonthly: 47,
  UnixSecondsWeek: 48,
  Quarter: 49,
  CustomQuarter: 50,
};

export enum ComplexMetricFrom {
  METRIC = 1,
  DIMENSION = 2,
  /** 元指标 */
  MetaMetric = 3,
  /** 业务评分 */
  ComplexMeta = 4,
}
export enum CHART_TYPE {
  /** 表格 */
  SHEET = 'sheet',
  /** 指标表 */
  METRICSHEET = 'metric_sheet',
  /** 排行榜 */
  RANKING = 'ranking',
  PIE = 'pie',
  CARD = 'card',
  LINE = 'line',
  BAR = 'bar',
  BAR_STACK = 'bar_stack',
  BAR_ROW = 'bar_row',
  DOUBLE_AXIS = 'double_axis',
  LINE_STACK = 'line_stack',
  PERCENT_BAR_STACK = 'percent_bar_stack',
  RADAR = 'radar',
  /** 人力资源热力图 */
  HUMAN_RESOURCE = 'heatmap',
  /** 漏斗图 */
  FUNNEL = 'funnel',
}

export const MeasureTypeBussiness = '业务线';
export const MeasureTypeDept = '部门';
export const MeasureTypeReport = '汇报人';

export enum SpaceType {
  Department = 'department',
  Business = 'business',
  Report = 'report',
}

export const DepartmentMeasureObjID = '2';
export const ReportMeasureObjID = '4';
export const BusinessMeasureObjID = '3';
export const PSMMeasureObjID = '7';
export const PeopleMeasureObjID = '1';
export const CodeRepoMeasureObjID = '5';
export const ProjectMeasureObjID = '6';
export const APPMeasureObjID = '8';
export const ProductMeasureObjID = '9';

export const StrOpIn = 'IN';
export const StrOpLike = 'LIKE';

export enum TimeGranularityText {
  Day = '天',
  Week = '周',
  DoubleWeek = '双周',
  Month = '月',
  DoubleMonth = '双月',
  Quarter = '季度',
  HalfYear = '半年',
  Year = '年',
  // MidYearPerformance = '半年绩效周期',
  // AnnualPerformance = '全年绩效周期',
}

export const TimeGranularityName: Record<string, string> = {
  [TimeGranularityText.Day]: 'd',
  [TimeGranularityText.Week]: 'w',
  [TimeGranularityText.DoubleWeek]: 'dw',
  [TimeGranularityText.Month]: 'M',
  [TimeGranularityText.DoubleMonth]: 'DM',
  [TimeGranularityText.Quarter]: 'Q',
  [TimeGranularityText.HalfYear]: 'HY',
  [TimeGranularityText.Year]: 'y',
  // [TimeGranularityText.MidYearPerformance]: 'myp',
  // [TimeGranularityText.AnnualPerformance]: 'ap',
};

export enum TimeCycleText {
  Standard = '标准周期',
  Custom = '自定义周期',
}

export const TimeCycleName: Record<string, string> = {
  [TimeCycleText.Standard]: 'standard',
  [TimeCycleText.Custom]: 'custom',
};

export const ResponseTypeProcessInfo = 'process_info';
export const ResponseTypeQueryData = 'query_data';

export const ELEMENT_METRIC_CARD_AREA = 'metric-card';
export const METRIC_CARD_GRAGGING_SIGNAL = 'is-metric-card-dragging';

export const RequestTypeDevmindQuery = 'devmind_query';
export const RequestTypeExecuteQuery = 'execute_query';
export const MeasureObjectBusinessTree = 'business';
export const MeasureObjectDepartmentTree = 'department';
export const MeasureObjectReportTree = 'report';

export const StoryTypeMeta = 'meta';
export const StoryTypeComplexMeta = 'complexMeta';

export const TimeGranularityDay = 'd';
export const TimeGranularityYear = 'y';
export const TimeGranularityHalfYear = 'HY';
export const TimeGranularityMonth = 'M';
export const TimeGranularityDoubleMonth = 'DM';
export const TimeGranularityWeek = 'w';
export const TimeGranularityDoubleWeek = 'dw';
export const TimeGranularityQuarter = 'Q';
export const TimeGranularityAnnualPerformance = 'ap';
export const TimeGranularityMidYearPerformance = 'myp';
export const TimeGranularityAll = 'all';
