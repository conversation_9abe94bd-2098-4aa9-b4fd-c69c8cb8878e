import { HttpContext } from '@gulu/application-http';
import { getUrlPerfix, AUTH_CODE } from './utils';

export const _request = async (
  ctx: HttpContext,
  url: string,
  options: any,
  nofetch = false,
) => {
  const headers = {
    // 'x-tt-env': ctx.request.header['x-tt-env'] || 'ppe_employee_tree_username',
    // 'x-use-ppe': ctx.request.header['x-use-ppe'] || '1',
    accept: 'application/json',
    'content-type': 'application/json',
    'auth-code': ctx.request.header['auth-code'] || AUTH_CODE,
  };
  ctx.logger.info(
    'devmind_node: request url = ',
    url,
    'header = ',
    url,
    headers,
  );

  if (nofetch) {
    return Promise.resolve({
      method: options.method,
      payload: options.body,
      api_path: url,
      headers,
    });
  }
  let res = null;
  res = await ctx.fetch(getUrlPerfix() + url, {
    ...options,
    headers,
  });
  // if (res.status && res.status !== 200) {
  //   ctx.logger.error('devmind_node: request error = %v', res);
  //   return Promise.resolve({});
  // }
  try {
    return res.json().catch((err: any) => {
      ctx.logger.error('devmind_node: request error = %v', err);
    });
  } catch (error) {
    ctx.logger.error('devmind_node: request error = %v', error);
    return Promise.resolve({});
  }
};
