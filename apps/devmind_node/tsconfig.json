{"compilerOptions": {"target": "es2017", "esModuleInterop": true, "module": "CommonJS", "lib": ["es2015", "es2016", "es2017"], "allowJs": true, "jsx": "react", "declaration": false, "skipDefaultLibCheck": true, "skipLibCheck": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noImplicitThis": true, "noFallthroughCasesInSwitch": true, "types": ["node", "jest"], "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "strict": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "baseUrl": "./", "importHelpers": true, "paths": {"@quality/common-apis": ["./packages/common-apis/src"]}}, "exclude": ["node_modules", "output", "static", "test", "bootstrap.js", "index.js", "common-utils"]}