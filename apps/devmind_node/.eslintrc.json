{"extends": ["@jupiter-app"], "root": true, "rules": {"max-lines": "off", "max-statements": "off", "global-require": "off", "import/no-dynamic-require": "off", "filenames/match-exported": "off", "jsdoc/require-example": "off", "filenames/match-regex": "off", "import/no-commonjs": "off", "import/no-anonymous-default-export": "off", "require-atomic-updates": "off", "no-console": "off", "require-await": "off", "no-shadow": "off", "no-unused-disable": "off", "naming-convention": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/require-await": "off", "@typescript-eslint/no-shadow": "off", "eslint-comments/no-unused-disable": "off", "no-nested-ternary": "off", "@typescript-eslint/no-unnecessary-type-assertion": "off"}, "globals": {}}