import { faker } from '@faker-js/faker';
import { ProblemRegionCNs } from '@quality/common-apis/src/apis/api/metric_platform/consts';
import {
  DimensionMetaData,
  ModelMetaData,
  StoryInfo,
} from '@quality/common-apis/src/apis/api/metric_platform/model';
import { GetStoryRequest } from '@quality/common-apis/src/apis/api/metric_platform/story';
import { resWrap } from '../utils';
import {
  attrOpts,
  storyUseOpts,
  levelOpts,
} from '@/pages/metric-story-manage/form/components/FormDescription/helper';
import { analysisDirectionOpts } from '@/pages/metric-story-manage/form/components/FormAnalysis/helper';

export const GetStory = async (_req: GetStoryRequest) => {
  function createStoryInfo(): StoryInfo {
    const measure_objs = faker.helpers.uniqueArray(
      () =>
        faker.helpers.arrayElement([
          { id: '2', display_name: '部门' },
          { id: '3', display_name: '业务线' },
          { id: '4', display_name: '汇报人' },
        ]),
      2,
    );

    return {
      node_id: faker.datatype.uuid(),
      space_type: faker.helpers.arrayElement(['measure', 'expert']),
      story_desc: {
        problem_region: faker.helpers.arrayElement(ProblemRegionCNs),
        story_use: faker.helpers.arrayElement(
          storyUseOpts.map(opt => opt.value),
        ),
        attr: faker.helpers.arrayElement(attrOpts.map(opt => opt.value)),
        level: faker.helpers.arrayElement(levelOpts.map(opt => opt.value)),
        description: faker.lorem.sentence(),
        drag_direction: faker.lorem.paragraph(),
        calc_caliber: faker.lorem.paragraph(),
      },
      story_analysis: {
        analysis_direction: faker.helpers.arrayElement(
          analysisDirectionOpts.map(opt => opt.value),
        ),
        drill_config: [],
        measure_objs,
        base_line: measure_objs.map(item => ({
          measure_obj: {
            id: item.id,
            display_name: item.display_name,
          },
          baselines: faker.helpers.uniqueArray(
            () => ({
              id: faker.helpers.mustache('mock id {{id}}', {
                id: () => `${faker.datatype.uuid()}`,
              }),
              display_name: faker.helpers.mustache('mock display_name {{id}}', {
                id: () => `${faker.datatype.number()}`,
              }),
              create_time: faker.date.recent().toLocaleDateString(),
              creator_info: {
                name: faker.name.firstName(),
                username: faker.name.fullName(),
                avatar_url: faker.image.avatar(),
              },
              is_generate_result: false,
              is_key: false,
            }),
            2,
          ),
        })),
        improve_case: [],
        improve_plan: [],
      },
      story_base: {} as any,
    };
  }

  return resWrap(createStoryInfo());
};

export const ListDimensionListByCondition = async _params => {
  function createModelMetaData(): DimensionMetaData {
    return {
      id: faker.datatype.uuid(),
      display_name: faker.helpers.mustache('测试维度 {{id}}', {
        id: () => `${faker.datatype.number()}`,
      }),
      description: faker.lorem.sentence(),
      owners: faker.helpers.uniqueArray(faker.name.fullName, 2),
      en_name: faker.helpers.mustache('test dimension {{id}}', {
        id: () => `${faker.datatype.number()}`,
      }),
    } as DimensionMetaData;
  }

  const result = {
    data_list: Array.from({ length: 10 }, () => createModelMetaData()),
  };

  return resWrap(result);
};
