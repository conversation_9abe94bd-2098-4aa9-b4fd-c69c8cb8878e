<!DOCTYPE html>
<html lang="zh-cn">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
  <title>度量平台</title>

  <!-- Nuni<PERSON> Sans 字体 改为import导入 -->
  <!-- <link href="/devinsight/public/nunito.css" rel="stylesheet"> -->
  </link>

  <script language="JavaScript">
    window.__render_start = +new Date();
    window.__render_start_pathname = location.pathname;
  </script>
  <!-- 本地开发/public/xlog.js，线上/devinsight/public/xlog.js -->
  <!-- <script src="/public/xlog.js"></script> -->

  <script>
    (function () {
      const scriptElement = document.currentScript;
      fetch('/devinsight/devmind-open/api/get_env').then(data => data.json()).then(({ data, env }) => {
        if (data?.needLog === true) {
          const script = document.createElement('script');
          script.src = `${window.location.host === 'localhost:8080' ? '' : '/devinsight'}/public/xlog.js`;
          const { product_id, product_name, channel_env, types, from } = data;
          script.onload = function () {
            window.xlog?.init({
              from,
              types,
              myTrackConfig: {
                product_id,
                product_name,
                channel_env,
              },
            });
          };
          document.head.appendChild(script);
        }
        if (env === 'efd') {
          const script = document.createElement('script');
          script.src = 'https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.26.js';
          script.onload = function () {
            if (!window.h5sdk) {
              window.__SSOType = 'cas'
            } else {
              window.__SSOType = 'lark'
            }
          };
          document.head.appendChild(script);
        }
        scriptElement.parentNode.removeChild(scriptElement);
      })
    })();
  </script>
  <!-- iconpark 图标库 改为import导入 -->
  <!-- <script src="/devinsight/public/icons.min.js"></script> -->
</head>

<body>
  <div class="wm-div"></div>
  <noscript> You need to enable JavaScript to run this app. </noscript>
  <div id="root"></div>
</body>

</html>
