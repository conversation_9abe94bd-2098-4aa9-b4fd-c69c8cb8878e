<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M50 100C77.6142 100 100 77.6142 100 50C100 22.3858 77.6142 0 50 0C22.3858 0 0 22.3858 0 50C0 77.6142 22.3858 100 50 100Z" fill="#EBEBEB"/>
<mask id="mask0_18048_48938" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="100" height="100">
<path fill-rule="evenodd" clip-rule="evenodd" d="M50 100C77.6142 100 100 77.6142 100 50C100 22.3858 77.6142 0 50 0C22.3858 0 0 22.3858 0 50C0 77.6142 22.3858 100 50 100Z" fill="white"/>
</mask>
<g mask="url(#mask0_18048_48938)">
<path opacity="0.322545" fill-rule="evenodd" clip-rule="evenodd" d="M35 25H77L111 73.7989V108L35 107.75V25Z" fill="#D3D5D7"/>
<g filter="url(#filter0_d_18048_48938)">
<g filter="url(#filter1_d_18048_48938)">
<rect x="-7" y="22" width="84" height="86" rx="4" fill="#F3F4F5"/>
</g>
<rect opacity="0.64" x="-25" y="34" width="90" height="26" fill="white"/>
<rect opacity="0.8" x="-19" y="39" width="40" height="6" fill="#E1E2E4"/>
<rect opacity="0.8" x="-19" y="51" width="60" height="4" fill="#E1E2E4"/>
<rect opacity="0.64" x="-25" y="65" width="90" height="26" fill="white"/>
<rect opacity="0.8" x="-19" y="70" width="40" height="6" fill="#E1E2E4"/>
<rect opacity="0.8" x="-19" y="82" width="60" height="4" fill="#E1E2E4"/>
</g>
</g>
<defs>
<filter id="filter0_d_18048_48938" x="-35" y="12" width="122" height="106" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_18048_48938"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_18048_48938" result="shape"/>
</filter>
<filter id="filter1_d_18048_48938" x="-17" y="12" width="104" height="106" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_18048_48938"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_18048_48938" result="shape"/>
</filter>
</defs>
</svg>
