@font-face {
    font-family: 'NunitoSans-Regular';
    src: url(./ttf/76b7bf89a4838e7e.ttf) format('truetype');
    font-style: normal;
    font-display: auto;
    font-weight: 500;
    unicode-range: U+1e16-fb02;
}

@font-face {
    font-family: 'NunitoSans-Regular';
    src: url(./ttf/d60e3c8ccbff2035.ttf) format('truetype');
    font-style: normal;
    font-display: auto;
    font-weight: 500;
    unicode-range: U+100-1e15;
}

@font-face {
    font-family: 'NunitoSans-Regular';
    src: url(./ttf/880491603c485ec6.ttf) format('truetype');
    font-style: normal;
    font-display: auto;
    font-weight: 500;
    unicode-range: U+a3-ff,U+2014,U+2018-2019,U+201c-201d;
}

@font-face {
    font-family: 'NunitoSans-Regular';
    src: url(./ttf/b7ddc9f422834db0.ttf) format('truetype');
    font-style: normal;
    font-display: auto;
    font-weight: 500;
    unicode-range: U+0-d,U+20-7e,U+a0-a2;
}
/* DEBUG:
    env.APP_REGION: undefined
    query region: null
    final region: cn
*/
