.arco-menu-group {
  max-height: 460px;
  overflow-y: auto;
}

.popover-font {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  white-space: normal;
}

.tooltip-popover-base {
  max-width: 240px;
  max-height: 160px;
  overflow-x: hidden;
  overflow-y: auto;
}
//公共组件优化，tooltip设置最大宽高，过高时会滚动
.arco-tooltip-content {
  .tooltip-popover-base;
  .popover-font;
}
//公共组件优化，popover设置最大宽高，过高时会滚动
.arco-popover-content {
  .tooltip-popover-base;
  .popover-font;
}

/* 不受宽高约束的气泡框，特殊情况下设置 */
.no-max-popover {
  .arco-popover-content {
    max-height: unset;
    max-width: unset;
  }
}

//设置最小宽度，修复浏览器缩小时造成的卡片重叠
body {
  min-width: 1440px;
}

#chartContainer canvas:hover {
  cursor: default; /* 设置鼠标形状为默认 */
}

// 不展示sort组件，但需要sort外包div包裹
.noSort {
  .arco-table-sorter {
    display: none;
  }
}

.arco-message-wrapper {
  z-index: 1050;
}
