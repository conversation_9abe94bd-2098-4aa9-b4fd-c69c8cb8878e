import {
  getTimeGranularityId,
  getDefaultTimeRange,
  getTimeFilterParams,
  isMetaMetricData,
  getSpaceFilterParams,
  getDomainMeasureObjFilterParams,
  getDisplayConfig,
  getMetaMetricQueryParams,
  getComplexMetaMetricQueryParams,
  MetricTypeMeta,
} from '@devmind/server/app/utils/index';
import type { MetricData } from '@devmind/server/app/utils/index';
import { createModel } from '@jupiter/plugin-runtime/model';

import { CommonDimension } from '@quality/common-apis/src/apis/api/base/base_data';
import { MetricInfoAndExpertAnalysisInfo } from '@quality/common-apis/src/apis/api/insight_report/report';
import type {
  DrillLayer,
  QueryRequest,
} from '@quality/common-apis/src/apis/api/query/query';
import type { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import type { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';

export {
  MetricData,
  isMetaMetricData,
  getSpaceFilterParams,
  getDomainMeasureObjFilterParams,
  getDisplayConfig,
  getMetaMetricQueryParams,
  getComplexMetaMetricQueryParams,
};

export { getTimeGranularityId, getDefaultTimeRange, getTimeFilterParams };

export const getMetricDrillConfig = (
  metricData: MetricMetaData | ComplexMetricMetaData | undefined,
) => {
  const config: { [key: string]: Array<DrillLayer> } = {};
  if (!metricData) {
    return config;
  }
  if (isMetaMetricData(metricData)) {
    if (metricData?.metric_expand_attribute?.drill_config?.length) {
      // 添加元指标下钻配置
      config[metricData.id] = metricData.metric_expand_attribute.drill_config;
    }
  } else {
    if (metricData?.metric_expand_attribute?.drill_config?.length) {
      // 添加评分指标下钻配置
      config[metricData.complex_metric_list[0].id] =
        metricData?.metric_expand_attribute?.drill_config;
    }

    // 添加评分指标下的元指标下钻配置
    metricData.atom_meta_metric_list?.forEach(data => {
      if (data.metric_data?.metric_expand_attribute?.drill_config?.length) {
        config[data.metric_data.id] =
          data.metric_data?.metric_expand_attribute?.drill_config;
      }
    });
  }
  return config;
};

export const getAnalysisMetricIds = (
  metricData: MetricMetaData | ComplexMetricMetaData | undefined,
) => {
  const ids: string[] = [];
  if (!metricData) {
    return ids;
  }
  if (isMetaMetricData(metricData)) {
    ids.push(metricData.id);
  } else {
    ids.push(metricData.complex_metric_list[0].id);
    metricData?.atom_meta_metric_list?.forEach(data => {
      data.metric_data?.id && ids.push(data.metric_data.id);
    });
  }
  return ids;
};

export const tweakTrendAnalysisHack = (
  isMetaMetric: boolean,
  query: QueryRequest,
  metricDetail: MetricMetaData,
): QueryRequest => {
  const result = { ...query };

  // - public_filter_list 内容同步到 queryInfo 中的 where_list 中
  result.query_info.where_list = result.public_filter_list?.map(item => {
    const result = {
      where_condition: {
        ...item.where_condition,
        alias_id: item.where_dimension_list?.[0]?.dimension_id,
      },
    };
    if (!isMetaMetric) {
      // 复合元指标添加固定值
      (result.where_condition as any).from = 'common_dimension';
    }

    return result;
  }) as any;

  // 如果是元指标
  // 补充common_dimension_list，使用指标详情接口里的analysis_info中的analysis_dim_ids进行补充
  if (isMetaMetric) {
    result.query_info.common_dimension_list = result.query_info
      .common_dimension_list?.length
      ? [
          ...result.query_info.common_dimension_list,
          ...(metricDetail.analysis_info?.analysis_dim_ids ?? []).map(item => {
            return {
              // analysis_dim_ids 中的 dim_id
              id: item.dim_id,
              // analysis_dim_ids 中的display_name
              display_name: item.display_name,
              // 只有一项，太他妈 hack 了
              dimension_list: [
                {
                  // 元指标 id
                  label: metricDetail.id,
                  // analysis_dim_ids 中的 dim_id
                  dimension_id: item.dim_id,
                  // 元指标：3
                  from: 3,
                },
              ],
            } as CommonDimension;
          }),
        ]
      : result.query_info.common_dimension_list;
  }

  return result;
};

// key 为某个请求, value是否loading
export type EventBusLoadingState = { [key: string]: boolean };

export const eventBusLoadingModel = createModel<EventBusLoadingState>(
  'eventBusLoading-cache',
).define({
  state: {},
  actions: {
    setLoading: (state, payload: EventBusLoadingState) => {
      return { ...state, ...payload };
    },
    restore: () => {
      return {};
    },
  },
});

export const getMetricDataFormRes = (
  data: MetricInfoAndExpertAnalysisInfo,
  metricType,
) => {
  if (metricType === MetricTypeMeta) {
    return data.meta_metric_data;
  } else {
    return data.complex_metric_data;
  }
};
