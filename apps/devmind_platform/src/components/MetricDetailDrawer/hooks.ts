import { useEffect } from 'react';

import { NiceModalHandler } from '@ebay/nice-modal-react';
import { useModel } from '@jupiter/plugin-runtime/model';
import { useRequest } from 'ahooks';
import { match } from 'path-to-regexp';
import { useRouteMatch } from 'react-router-dom';

import type { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import type { DisplayConfig } from '@quality/common-apis/src/apis/api/base/display_config';
import {
  queryServiceClient,
  QueryRequest,
  QueryInfo,
} from '@quality/common-apis/src/apis/api/query/query';
import { CHART_TYPE } from '@quality/common-components';
import {
  QUERYCACHE,
  QUERYCACHE_ENABLE,
  QUERYCACHE_DISABLE,
} from '@quality/common-utils';

import type { TimeFilterValue } from '@/components/TimeFilter';
import { useReportConfigUrlQuery } from '@/hooks/url';
import useMetricDrawerUrlQuery from '@/hooks/url/useMetricDrawerUrlQuery';
import globalModel from '@/model/global';
import { genQueryReqKey } from '@/model/report/utils';
import { CacheModule } from '@/modules/cache';
import { DevmindUrls, pathPrefix } from '@/routers/path';
import { generateQueryRequest } from '@/utils/metrics';
import { isCacheExpire } from '@/utils/utils';

import { DomainMeasureObjFilterValue } from '../DomainMeasureObjFilter';

import type { MetricData } from './interface';
import { isMetaMetricData } from './utils';

export const useDetect = (handler: NiceModalHandler) => {
  const { urlQuery } = useMetricDrawerUrlQuery();

  useEffect(() => {
    if (urlQuery._metricId) {
      handler.show();
    }
  }, [urlQuery._metricId]);
};

type DeepPartial<T> = T extends () => void
  ? T
  : // eslint-disable-next-line @typescript-eslint/ban-types
  T extends object
  ? { [P in keyof T]?: DeepPartial<T[P]> }
  : T;

export type QueryOptions<T = CHART_TYPE> = {
  chartType: CHART_TYPE;
  timeFilterInfo: TimeFilterValue;
  nodeId: string;
  templateId?: string;
  parentId?: string;
  customParams?: DeepPartial<QueryRequest> & Record<string, any>; // 不同类型图表特有的参数
  anchorStartTime?: string; // 周期锚点开始时间，不传默认使用时间筛选器的开始时间,趋势分析会用到
  domainMeasureObjFilter?: DomainMeasureObjFilterValue; // 领域度量对象筛选条件
  spaceId: string; // 对应虚拟类型的 NameEN
} & (T extends CHART_TYPE.SHEET
  ? { measureDimensionId: string } // 度量对象维度id 对比分析查询使用
  : { measureDimensionId?: string });

interface Options {
  showCompare?: boolean;
  onSuccess?: (data: QueryData | undefined) => void;
  freezeVersion?: string;
  hackTrendAnalysis?: boolean;
}
export const useQueryRequest = <T extends CHART_TYPE>(
  metricData: MetricData | undefined,
  options?: Options,
) => {
  let match = useRouteMatch(DevmindUrls.VirtualSpaceMatch);

  const { onSuccess, freezeVersion } = options ?? {};

  const [{ globalMetricItem, nodeIdRecord }] = useModel([globalModel]);

  const fetch = async (
    queryInfo: QueryOptions<T>,
    cacheable = true, // 请求是否走缓存
  ): Promise<
    | undefined
    | {
        dataSource: QueryData | undefined;
        msg: string | undefined;
        queryInfo: QueryInfo;
        displayConfig: DisplayConfig;
        params: QueryRequest;
      }
  > => {
    if (!queryInfo) {
      return;
    }

    const { nodeId, spaceId: virtualSpaceId } = queryInfo;

    if (!metricData || !nodeId || !virtualSpaceId) {
      return;
    }
    const nodeName = nodeIdRecord[nodeId]?.inject_filter_value ?? '';

    const virtualSpaceKey =
      (match?.params as { spaceType: string })?.spaceType ?? '';

    const queryRequest = await generateQueryRequest({
      metricData,
      queryInfo: { ...queryInfo, nodeName, spaceType: virtualSpaceKey },
      options,
    });

    if (!queryRequest?.params) {
      return;
    }

    const { params } = queryRequest;
    const cacheData = CacheModule.get.metricDataMap().get(
      genQueryReqKey({
        ...queryInfo,
        metricId: metricData.id,
        freezeVersion,
      }),
    );
    const cacheRes = cacheData?.data;

    if (!cacheRes || isCacheExpire(cacheData.time) || !cacheable) {
      const res = await queryServiceClient.Query(params, {
        [QUERYCACHE]: cacheable ? QUERYCACHE_ENABLE : QUERYCACHE_DISABLE,
      });
      if (res.code === 200) {
        CacheModule.set.setMetricDataMap({
          queryInfo: { ...queryInfo, metricId: metricData.id, freezeVersion },
          data: { ...res },
        });
      }

      return {
        dataSource: res?.data,
        msg: res?.msg,
        queryInfo: params.query_info,
        displayConfig: params.display_config,
        params,
      };
    }

    return {
      dataSource: cacheRes?.data,
      msg: cacheRes?.msg,
      queryInfo: params.query_info,
      displayConfig: params.display_config,
      params,
    };
  };

  const queryRequest = useRequest(fetch, {
    refreshDeps: [metricData, globalMetricItem.virtualSpaceKey],
    onSuccess: data => onSuccess?.(data!.dataSource),
  });

  return {
    ...queryRequest,
    ...(queryRequest.data ?? {
      dataSource: undefined,
      queryInfo: undefined,
      params: undefined,
      displayConfig: { display_type: CHART_TYPE.CARD },
    }),
    run: queryRequest.runAsync,
  };
};

export const getWarmUpExtra = (metricData, freezeVersion) => {
  if (isMetaMetricData(metricData)) {
    return {
      metric_id: metricData.id,
      metric_type: 'meta_metric',
      seal_version: freezeVersion,
    };
  } else {
    const dillConfig: any = {};
    metricData?.atom_meta_metric_list?.forEach(({ metric_data }: any) => {
      dillConfig[metric_data?.id] = Boolean(
        metric_data?.metric_expand_attribute?.drill_config?.length,
      );
    });
    return {
      metric_id: metricData.id,
      metric_type: 'score_metric',
      seal_version: freezeVersion,
    };
  }
};

export type MatchType = {
  params: { templateId?: string };
};

export const useQueryExtraParams = () => {
  const { urlQuery: _urlQuery } = useReportConfigUrlQuery();

  const currentSpacePath = `${pathPrefix}${DevmindUrls.VirtualSpaceReportDetail}`;
  const urlMatch = match(currentSpacePath)(location.pathname);
  const templateId = (urlMatch as MatchType)?.params?.templateId;

  const { reportId = '' } = _urlQuery;
  const inReportDetail = Boolean(templateId);
  return {
    inReportDetail,
    params: {
      template_id: templateId,
      ...(reportId ? { report_id: reportId } : {}),
    },
  };
};
