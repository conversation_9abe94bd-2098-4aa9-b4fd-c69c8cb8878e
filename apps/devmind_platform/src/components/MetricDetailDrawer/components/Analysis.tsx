import { FC, useMemo } from 'react';

import { Divider } from '@arco-design/web-react';
import { useModel } from '@jupiter/plugin-runtime/model';
import _ from 'lodash';
import { MacScrollbar } from 'mac-scrollbar';

import type { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import {
  QueryInfo,
  QueryRequest,
} from '@quality/common-apis/src/apis/api/query/query';
import { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';

import {
  AnalysisConclusion,
  AnalysisReason,
  Improvement,
} from '@/components/Analysis';
import { AbnormalRule } from '@/components/Analysis/AbnormalRule';
import { DomainMeasureObjFilterValue } from '@/components/DomainMeasureObjFilter';
import { getReportName, TimeFilterValue } from '@/components/TimeFilter';
import { Authority } from '@/constants/auth';
import { reportModel } from '@/model/report';
import { NodeManager } from '@/modules/node';

import type { GetSpaceJumpWarp, MetricData } from '../interface';

interface Props {
  nodeId: string;
  spaceId: string;
  domainMeasureObjFilter?: DomainMeasureObjFilterValue;
  timeFilterInfo: TimeFilterValue;
  metricData: MetricData | undefined;
  // 外层控制专家分析是否可编辑
  analysisEditable?: boolean;
  queryParams: QueryRequest;
  dataSource?: QueryData;
  queryResult?:
    | {
        dataSource: QueryData | undefined;
        msg: string | undefined;
        queryInfo: QueryInfo;
        params: QueryRequest;
      }
    | undefined;
  loading: boolean;
  freezeVersion?: string;
  getSpaceJumpWarp?: GetSpaceJumpWarp;
}

export const REASON_CACHE_EVENT = Symbol('analysis-reason');

const Analysis: FC<Props> = props => {
  const {
    metricData,
    nodeId,
    queryParams,
    domainMeasureObjFilter = { measureObjId: '', filter: [] },
    timeFilterInfo,
    analysisEditable = true,
    queryResult,
    loading,
    spaceId,
    getSpaceJumpWarp,
    freezeVersion = '',
  } = props;

  // 控制专家分析及改进任务是否可编辑
  const editable =
    NodeManager.getModule()?.validAuth(nodeId, Authority.write) &&
    analysisEditable &&
    !freezeVersion;

  // // 分析结论
  // const {
  //   loading: trendQueryLoading,
  //   run: trendQuery,
  //   data: trendQueryResult,
  //   params: trendQueryParams,
  //   dataSource: trendQueryDataSource,
  // } = useQueryRequest<CHART_TYPE.DOUBLE_AXIS>(metricData, {
  //   freezeVersion,
  // });
  // 数据单位;
  const dataUnit = useMemo(() => {
    if (!queryResult) {
      return;
    }
    const { queryInfo, dataSource } = queryResult;
    return dataSource?.data_unit?.find(
      ({ aliasId }) => aliasId === queryInfo.select_alias_id_list[0].alias_id,
    )?.data_unit;
  }, [queryResult]);

  // const handleFetch = useCallback(
  //   async (cacheable = true) => {
  //     if (!metricData) {
  //       return;
  //     }

  //     await trendQuery(
  //       {
  //         chartType: CHART_TYPE.DOUBLE_AXIS,
  //         nodeId,
  //         timeFilterInfo: {
  //           ...timeFilterInfo,
  //           range: getDefaultTimeRange(
  //             timeFilterInfo.range,
  //             timeFilterInfo.granularity,
  //           ),
  //         },
  //         domainMeasureObjFilter,
  //         customParams: {
  //           baselines: [],
  //           gen_rule_text: true,
  //         },
  //       },
  //       cacheable,
  //     );
  //     !cacheable && reasonRefreshTrigger();
  //   },
  //   // 依赖不需要加入nodeName, metricData变化是发生在节点改变之后，一定能获取到最新的名称
  //   [
  //     metricData?.id,
  //     ...timeFilterInfo?.range,
  //     domainMeasureObjFilter.measureObjId,
  //     domainMeasureObjFilter.filter?.sort()?.join(''),
  //     trendQuery,
  //   ],
  // );
  // 获取分析结论
  // useEffect(() => {
  //   handleFetch();
  // }, [handleFetch]);

  // 触发无缓存请求
  // useCacheEvent({
  //   eventName: cacheEvent,
  //   callback: () => handleFetch(false),
  // });

  // const { trigger: reasonRefreshTrigger } = useCacheEvent({
  //   eventName: REASON_CACHE_EVENT,
  // });

  const [{ templateInfo }] = useModel([
    reportModel,
    state => ({
      instanceInfo: state.$instanceInfo,
      templateInfo: state.$templateInfo,
    }),
  ]);

  const reportFullName = useMemo(() => {
    if (!templateInfo || !timeFilterInfo) {
      return '';
    }
    return getReportName({ ...templateInfo, ...timeFilterInfo });
  }, [templateInfo, timeFilterInfo]);

  const ququeryParamsMemo = useMemo(() => {
    if (spaceId && queryResult && !queryResult.params.query_info.tag_id) {
      let deepParams = _.cloneDeep(queryResult?.params);
      deepParams.query_info.tag_id = spaceId;
      deepParams.query_info.node_id = nodeId;
      return deepParams;
    }
    return queryResult?.params;
  }, [queryResult?.params, spaceId]);

  return (
    <MacScrollbar
      suppressScrollX={true}
      className="h-[calc(100%-72px)] px-6 py-5">
      <AnalysisConclusion
        loading={loading}
        conlusion={queryResult?.dataSource?.chart_data?.rule_conclusion}
        dataUnit={dataUnit}
      />
      <Divider className="!mt-3 !mb-5" />
      <AnalysisReason
        metricData={metricData}
        queryParams={ququeryParamsMemo}
        dataSource={queryResult?.dataSource}
        domainMeasureObjFilter={domainMeasureObjFilter}
        timeFilterInfo={timeFilterInfo}
        getSpaceJumpWarp={getSpaceJumpWarp}
        nodeId={nodeId}
        expertProps={{
          editable,
          metricData,
          metric_expand_attribute: metricData?.metric_expand_attribute,
          business_id: nodeId,
          metric_id: metricData?.id || '',
          timeFilterInfo,
          start_time: timeFilterInfo?.range?.[0],
          end_time: timeFilterInfo?.range?.[1],
          metric_name:
            (metricData as MetricMetaData)?.display_name ||
            (metricData as ComplexMetricMetaData)?.name,
          metric_url: location.href,
          report_name: reportFullName,
          report_url: location.href,
          freezeVersion,
        }}
        loading={loading}
        cacheEvent={REASON_CACHE_EVENT}
        listMaxHeight="50vh"
      />
      {/*  xxx IFTRUE_prodEnv */}
      <Divider className="!my-2" />
      <AbnormalRule
        metricData={metricData}
        queryParams={queryParams}
        timeFilterInfo={timeFilterInfo}
      />
      <Divider className="!mt-3 !mb-5" />
      <Improvement
        analysisProps={{
          editable,
          metricData,
          metric_expand_attribute: metricData?.metric_expand_attribute,
          business_id: nodeId,
          metric_id: metricData?.id || '',
          timeFilterInfo,
          start_time: timeFilterInfo?.range?.[0],
          end_time: timeFilterInfo?.range?.[1],
          metric_name:
            (metricData as MetricMetaData)?.display_name ||
            (metricData as ComplexMetricMetaData)?.name,
          metric_url: location.href,
          report_name: reportFullName,
          report_url: location.href,
        }}
        freezeVersion={freezeVersion}
      />
      {/* xxx FITRUE_prodEnv */}
    </MacScrollbar>
  );
};

export default Analysis;
