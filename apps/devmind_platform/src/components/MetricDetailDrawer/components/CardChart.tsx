import { FC, useEffect, useMemo, useCallback, useState, useRef } from 'react';

import { useModal } from '@ebay/nice-modal-react';
import { useModel } from '@jupiter/plugin-runtime/model';
import cs from 'classnames';
import { noop, toNumber } from 'lodash';

import { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { DrillWhere } from '@quality/common-apis/src/apis/api/query/query';
import { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import {
  MetricTypeMeta,
  MetricTypeComplexMeta,
} from '@quality/common-apis/src/apis/api/zepto/consts';
import { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import { VisualPanel, CHART_TYPE } from '@quality/common-components';

import type { DomainMeasureObjFilterValue } from '@/components/DomainMeasureObjFilter';
import type { MetricInfo } from '@/components/MetricDetailDrawer/interface';
import type { TimeFilterValue } from '@/components/TimeFilter';
import { LogEventMap, LogSceneMap } from '@/constants/log';
import { useCacheEvent } from '@/hooks';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { XLog } from '@/utils/utils';

import { useQueryExtraParams, useQueryRequest } from '../hooks';
import {
  eventBusLoadingModel,
  getMetricDrillConfig,
  isMetaMetricData,
} from '../utils';

import DrillModal from './DrillModal';

interface Props {
  nodeId: string;
  timeFilterInfo: TimeFilterValue;
  domainMeasureObjFilter?: DomainMeasureObjFilterValue;
  metricData: MetricMetaData | ComplexMetricMetaData | undefined;
  showMetricAnalysis?: boolean; // 是否展示指标详情分析菜单选项, 评分指标下的元指标需要展示
  cacheEvent?: symbol;
  onAnalysis?: (options: { metricInfo: MetricInfo }) => void;
  hideMenus?: string[];
  cacheDataSource?: QueryData;
  showFormula?: boolean; // 是否展示公式
  index: number;
  freezeVersion?: string;
}

const CardChart: FC<Props> = props => {
  const {
    domainMeasureObjFilter = { measureObjId: '', filter: [] },
    timeFilterInfo,
    nodeId,
    index,
    metricData,
    onAnalysis,
    showMetricAnalysis,
    hideMenus,
    cacheEvent,
    showFormula = false,
    freezeVersion,
    cacheDataSource,
  } = props;

  const [, action] = useModel(eventBusLoadingModel);

  const { inReportDetail, params } = useQueryExtraParams();
  const cacheFlag = useRef<boolean>(true);
  const fetchCount = useRef<number>(0);
  const [cardLoading, setCardLoading] = useState(false);
  const { virtualItem } = useVirtualSpaceType();
  const spaceId = virtualItem?.SpaceId ?? '';

  const showDrillMenuList = useMemo<string[]>(() => {
    if (showMetricAnalysis) {
      return ['指标详情分析'];
    }
    return [];
  }, [showMetricAnalysis, metricData]);

  const {
    loading,
    run,
    dataSource,
    queryInfo,
    displayConfig,
    params: queryParams,
  } = useQueryRequest<CHART_TYPE.CARD>(metricData, {
    showCompare: true,
    freezeVersion,
  });
  useEffect(() => {
    if (cacheDataSource && !dataSource) {
      setCardLoading(false);
    } else {
      setCardLoading(loading);
    }
  }, [loading, dataSource, cacheDataSource]);
  useEffect(() => {
    action.setLoading({ [`card${index}Loading`]: loading });
  }, [loading]);

  const handleMetricAnalysis = () => {
    if (metricData) {
      if (isMetaMetricData(metricData)) {
        const { id, display_name } = metricData;
        onAnalysis?.({
          metricInfo: {
            id,
            name: display_name,
            type: MetricTypeMeta,
          },
        });
      } else {
        const { id, name } = metricData;
        onAnalysis?.({
          metricInfo: {
            id,
            name,
            type: MetricTypeComplexMeta,
          },
        });
      }
    }
  };

  const drillModal = useModal(DrillModal);

  // 查看明细下钻
  const handleDrill = (drillWhere: DrillWhere) => {
    XLog({
      id: 'webclick',
      page_id: LogSceneMap.metric.value,
      page_title: LogSceneMap.metric.type,
      btn_title: {
        metric: isMetaMetricData(metricData!)
          ? metricData.display_name
          : metricData?.name,
        event: LogEventMap.metric.drillModal,
      },
    });
    drillModal.show({
      nodeId,
      queryParams,
      drillWhere,
      drillConfig: getMetricDrillConfig(metricData),
    });
  };

  const handleFetch = useCallback(
    async (cacheable = true) => {
      fetchCount.current += 1;
      // 二次请求,默认不使用cache
      if (fetchCount.current > 1) {
        cacheFlag.current = false;
      }
      await run(
        {
          chartType: CHART_TYPE.CARD,
          timeFilterInfo,
          nodeId,
          domainMeasureObjFilter,
          spaceId,
          ...(inReportDetail ? { customParams: params } : {}),
        },
        cacheable,
      );
      // 请求完毕,不使用cache
      cacheFlag.current = false;
    },
    // 依赖不需要加入nodeId, metricData变化是发生在节点改变之后，一定能获取到最新的名称
    [
      run,
      metricData?.id,
      ...timeFilterInfo?.range,
      domainMeasureObjFilter.measureObjId,
      domainMeasureObjFilter.filter?.sort()?.join(''),
      spaceId,
    ],
  );

  useEffect(() => {
    handleFetch();
  }, [handleFetch]);

  // 触发无缓存请求
  useCacheEvent({
    eventName: cacheEvent,
    callback: () => handleFetch(false),
  });

  const formula_desc =
    metricData && !isMetaMetricData(metricData)
      ? metricData?.formula_description
      : '';

  const cacheCardSource = useMemo(() => {
    if (cacheDataSource && metricData) {
      const metric_id = isMetaMetricData(metricData)
        ? metricData?.id
        : metricData?.atom_complex_metric_list?.[0].id;
      const series = cacheDataSource.chart_data?.series.find(
        ({ metric_alias_id }) => metric_id === metric_alias_id,
      );
      const latest_data = cacheDataSource.chart_data?.latest_datas?.find(
        ({ metric_alias_id }) => metric_id === metric_alias_id,
      );

      return {
        card_data: [
          {
            name: series?.name || '',
            alias_id: series?.metric_alias_id || '',
            value: toNumber(latest_data?.value),
            interactive_name: '',
            default_value: latest_data?.default_value,
            is_no_data: latest_data?.is_no_data,
          },
        ],
        data_unit: cacheDataSource.data_unit?.filter(
          item => item.aliasId === metric_id,
        ),
        query_id: '',
      };
    }
    return undefined;
  }, [cacheDataSource]);
  return (
    <div className={cs(['relative', showFormula && formula_desc && 'mt-7'])}>
      {showFormula && formula_desc && (
        <div className="text-sm whitespace-nowrap w-full absolute top-[-32px] left-0">
          {formula_desc}
        </div>
      )}
      <div className="min-h-[104px] flex items-center justify-center">
        <VisualPanel
          loading={cacheFlag.current ? cardLoading : loading}
          style={{
            width: 'fit-content',
          }}
          chartClassName="rounded-[4px]  p-[16px] border border-solid border-[#E5E6EB] min-w-[200px]"
          displayConfig={{ display_subtype: 'compact', ...displayConfig }}
          query_info={queryInfo}
          dataSource={
            cacheFlag.current ? dataSource || cacheCardSource : dataSource
          }
          drillWhere={[]}
          onDrillBack={noop}
          hasMenuMetrics={[]}
          timeGranularitys={[]}
          dimensionConf={[]}
          showDrillMenuList={showDrillMenuList}
          chart_detail_info={
            { meta_drill_config: getMetricDrillConfig(metricData) } as any
          }
          onAnalysis={handleMetricAnalysis}
          onDrill={handleDrill}
          hideMenus={hideMenus}
        />
      </div>
    </div>
  );
};

export default CardChart;
