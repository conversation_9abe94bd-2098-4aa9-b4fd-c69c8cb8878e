import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Message,
  Notification,
  Tabs,
} from '@arco-design/web-react';
import { IconDownload } from '@arco-iconbox/react-bits';
import { useRequest } from '@byted/hooks';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { noop } from 'lodash';

import { MetricSheetData } from '@quality/common-apis/src/apis/api/base/base_data';
import { DisplayConfig } from '@quality/common-apis/src/apis/api/base/display_config';
import { queryBaseFeatureServiceClient } from '@quality/common-apis/src/apis/api/query/base_feature';
import {
  queryServiceClient,
  QueryInfo,
  WarmupExtra,
} from '@quality/common-apis/src/apis/api/query/query';
import { VisualPanel } from '@quality/common-components';

import { API_V1 } from '@/constants';

import { SCModal, SCTabs } from '../styles';

import DrillDetailTable, { Props as ModalProps } from './DrillTable';

const getModelParams = ({
  modelId,
  aliasList,
  filterOptions,
  recently_key,
  warmupExtra,
}) => {
  return {
    query_info: {
      model_id: modelId,
      select_alias_id_list: aliasList.map(dimAliasId => ({
        alias_id: dimAliasId,
        from: 'dimension',
      })),
      where_list: Object.entries(filterOptions)
        .map(([key, value = '']) => ({
          where_condition: {
            alias_id: key,
            op: 'IN',
            val: [value],
            data_type: 'STRING',
            display_name: 'BUG ID(区分来源平台)',
            time_last_type: '',
            contain_cur_period: false,
          },
          is_all: false,
          is_select_filter: false,
          filter_display_type: 2,
        }))
        .concat(
          recently_key
            ? [
                {
                  confidentiality: 'ConfidentialityPartition',
                  is_all: false,
                  where_condition: {
                    alias_id: String(recently_key),
                    contain_cur_period: false,
                    data_type: 'DATE',
                    display_name: 'date',
                    op: 'RECENTLYHAS',
                    time_last_type: 'day',
                    val: ['1'],
                  },
                } as any,
              ]
            : [],
        ),
      group_alias_id_list: [],
      having_list: [],
      dim_met_list: [],
    },
    display_config: {
      display_type: 'sheet',
    },
    project_id: '',
    drill: {
      layer_list: [[]],
      detail_config: [],
    },
    attribution_info: [],
    mark_config_list: [],
    warmup_extra: warmupExtra,
  };
};

interface Props extends ModalProps {
  nodeId: string;
  drillModels?: any;
  drillData?: MetricSheetData;
  filterOption?: any;
  hasOutputAuth?: boolean;
  warmupExtra?: WarmupExtra;
}

const DrillConfigTable = ({ params, drillConfig, showOutputBtn }) => {
  const { loading, data } = useRequest(
    async () => {
      const res = await queryServiceClient.Query(
        {
          ...params,
          version: API_V1,
        },
        {
          hideNotice: true,
        },
      );
      if (res.code !== 200) {
        Notification.error({
          title: '请求失败',
          content: res.msg,
        });
      }
      return res;
    },
    {
      auto: true,
      refreshDeps: [params],
    },
  );

  const handleDownload = async () => {
    Message.info('正在导出中，预计需要1~2分钟');
    const dlres = await queryBaseFeatureServiceClient.GetDownloadInfo({
      ...(params as any),
      version: 1,
    });
    if (dlres?.data?.url) {
      window.open(dlres?.data?.url);
    }
    if (dlres.code !== 200) {
      Message.error(dlres.msg);
    }
  };

  return (
    <div className="h-[500px]  flex flex-col">
      {params?.warmup_extra?.seal_version &&
        data?.data &&
        !data?.msg.includes('seal') && (
          <Alert
            type="info"
            showIcon={true}
            content="当前数据为实时数据，可能与快照版本存在差异"
            closable={true}
            style={{
              position: 'absolute',
              marginLeft: 10,
              padding: '4px 12px',
              width: 'calc(100% - 60px)',
              height: 32,
            }}
          />
        )}
      {/* IFTRUE_prodEnv */}
      {showOutputBtn && (
        <div style={{ textAlign: 'right', paddingRight: 12 }}>
          <Button
            onClick={handleDownload}
            icon={<IconDownload />}
            // type="text"
            style={{ color: '#222' }}
            iconOnly
          />
        </div>
      )}
      {/* FITRUE_prodEnv */}

      <VisualPanel
        loading={loading}
        displayConfig={params?.display_config as DisplayConfig}
        pagination={{
          defaultPageSize: 10,
        }}
        chart_detail_info={{
          chart_id: '',
          query_info: params?.query_info as QueryInfo,
          drill: {
            layer_list: [],
            detail_config: [],
          },
          attribution_info: [],
          display_config: params?.display_config as DisplayConfig,
          chart_name: '',
          description: '',
          owners: [],
          page_list: [],
          version: 0,
          link_list: [],
          chart_type: '',
          meta_drill_config: drillConfig,
        }}
        drillWhere={[]}
        queryDrillWhere={[]}
        onDrill={noop}
        onDrillBack={noop}
        hasMenuMetrics={[]}
        timeGranularitys={[]}
        dimensionConf={[]}
        dataSource={data?.data}
      />
    </div>
  );
};

const DrillModal = NiceModal.create<Props>(props => {
  const {
    nodeId,
    queryParams,
    drillWhere,
    drillModels,
    drillData,
    drillConfig,
    warmupExtra,
    filterOption,
  } = props;

  const modal = useModal();

  const hasOutputAuth = true;

  return (
    <SCModal
      className="!w-2/3"
      visible={modal.visible}
      footer={null}
      unmountOnExit={true}
      title="明细下钻"
      wrapStyle={{ zIndex: 1010 }}
      maskStyle={{ zIndex: 1005 }}
      onCancel={modal.hide}>
      {drillModels?.length ? (
        <SCTabs defaultActiveTab={drillModels?.[0]?.model_id}>
          {drillModels.map(item => {
            let valueIndex =
              drillData?.titles?.findIndex(
                i => i.alias_id === item.value_key,
              ) || 0;
            valueIndex = valueIndex > 0 ? valueIndex : 0;
            const filterValue = filterOption?.[valueIndex]?.value;

            if (!filterValue) {
              return null;
            }

            const params = getModelParams({
              modelId: item.model_id,
              aliasList: item.related_dims.split(','),
              filterOptions: {
                [item.related_key]: filterValue,
              },
              recently_key: item.recently_key,
              warmupExtra,
            });
            return (
              <Tabs.TabPane key={item.model_id} title={item.related_name}>
                <DrillConfigTable
                  params={params}
                  drillConfig={drillConfig}
                  showOutputBtn={hasOutputAuth}
                />
              </Tabs.TabPane>
            );
          })}
        </SCTabs>
      ) : (
        <DrillDetailTable
          nodeId={nodeId}
          queryParams={queryParams}
          drillWhere={drillWhere}
          drillConfig={drillConfig}
          showOutputBtn={hasOutputAuth}
        />
      )}
    </SCModal>
  );
});

export default DrillModal;
