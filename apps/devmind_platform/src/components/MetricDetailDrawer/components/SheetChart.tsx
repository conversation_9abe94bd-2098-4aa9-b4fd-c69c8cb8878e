import {
  FC,
  useState,
  useEffect,
  useMemo,
  useCallback,
  forwardRef,
  Ref,
  MutableRefObject,
} from 'react';

import { Radio, Message } from '@arco-design/web-react';
import { useModal } from '@ebay/nice-modal-react';
import { useModel } from '@jupiter/plugin-runtime/model';
import cs from 'classnames';
import { noop } from 'lodash';

import { insightServiceClient } from '@quality/common-apis/src/apis/api/insight/homepage';
import { DrillWhere } from '@quality/common-apis/src/apis/api/query/query';
import { MetricTypeMeta } from '@quality/common-apis/src/apis/api/zepto/consts';
import { QueryInfo } from '@quality/common-apis/src/apis/engine/engine';
import { VisualPanel, CHART_TYPE } from '@quality/common-components';

import type { DomainMeasureObjFilterValue } from '@/components/DomainMeasureObjFilter';
import type {
  MetricInfo,
  MetricData,
  GetSpaceJumpWarp,
} from '@/components/MetricDetailDrawer/interface';
import type { TimeFilterValue } from '@/components/TimeFilter';
import { API_V1 } from '@/constants';
import { LogEventMap, LogSceneMap } from '@/constants/log';
import { useCacheEvent } from '@/hooks';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { NodeManager } from '@/modules/node';
import { XLog } from '@/utils/utils';

import { useQueryRequest } from '../hooks';
import {
  eventBusLoadingModel,
  getMetricDrillConfig,
  isMetaMetricData,
} from '../utils';

import DrillModal from './DrillModal';

interface Props {
  ref: Ref<QueryInfo | any>;
  nodeId: string;
  timeFilterInfo: TimeFilterValue;
  domainMeasureObjFilter: DomainMeasureObjFilterValue;
  metricData: MetricData | undefined;
  cacheEvent?: symbol;
  onAnalysis: (options: { nodeId: string; metricInfo?: MetricInfo }) => void;
  hideMenus?: string[];
  getSpaceJumpWarp?: GetSpaceJumpWarp;
  freezeVersion?: string;
  selectedArray?: string[];
}

const SheetChart: FC<Props> = forwardRef((props, queryInfoRef) => {
  const {
    timeFilterInfo,
    nodeId,
    domainMeasureObjFilter = { measureObjId: '', filter: [] },
    metricData,
    cacheEvent,
    onAnalysis,
    hideMenus,
    getSpaceJumpWarp,
    freezeVersion,
    selectedArray,
  } = props;
  const [, action] = useModel(eventBusLoadingModel);
  const { virtualItem } = useVirtualSpaceType();
  const spaceId = virtualItem?.SpaceId ?? '';

  const analysisDimensionList =
    metricData?.analysis_info?.analysis_dim_ids ?? [];
  const spaceType = NodeManager.getModule()?.getSpaceType() ?? '';

  const [dimensionId, setDimensionId] = useState<string>('');
  useEffect(() => {
    if (analysisDimensionList.length) {
      setDimensionId(analysisDimensionList[0].dim_id);
    } else {
      setDimensionId('');
    }
  }, [metricData]);
  const showDrillMenuList = [];
  const isEmployeeDim = useMemo<boolean>(() => {
    return (
      analysisDimensionList.find(({ dim_id }) => dim_id === dimensionId)
        ?.is_employee_dim ?? false
    );
  }, [analysisDimensionList, dimensionId]);

  const {
    loading,
    run,
    dataSource,
    queryInfo,
    displayConfig,
    params: queryParams,
  } = useQueryRequest<CHART_TYPE.SHEET>(metricData, {
    freezeVersion,
  });

  useEffect(() => {
    action.setLoading({ sheetloading: loading });
    (queryInfoRef as MutableRefObject<any>).current.loading = loading;
  }, [loading]);

  useEffect(() => {
    if (dimensionId && analysisDimensionList.length) {
      (queryInfoRef as MutableRefObject<any>).current.dimensionName =
        analysisDimensionList.find(
          ({ dim_id }) => dim_id === dimensionId,
        )?.display_name;
    }
  }, [dimensionId, analysisDimensionList]);

  (queryInfoRef as MutableRefObject<any>).current.queryParams = queryParams;

  const queryParamsList = metricData?.analysis_info?.analysis_dim_ids?.map(
    item => {
      const dim_id = item.dim_id;
      const _query = {
        ...queryParams,
        query_info: {
          ...queryParams?.query_info,
          group_alias_id_list: [
            {
              alias_id: dim_id,
              data_type:
                queryParams?.query_info.group_alias_id_list[0].data_type,
              analysis_type:
                queryParams?.query_info.group_alias_id_list[0].analysis_type,
            },
          ],
        },
      };
      return {
        query_info: _query,
        sheet_name: item.display_name,
      };
    },
  );
  (queryInfoRef as MutableRefObject<any>).current.queryParamsList =
    queryParamsList;

  const handleMetricAnalysis = async (drillWhere: DrillWhere) => {
    const { metric_alias_id } = drillWhere;
    const res = await insightServiceClient.GetBusinessID({
      version: API_V1,
      curr_val: drillWhere.interactive_name,
      space_type: spaceType,
    });

    const nodeId = res?.data?.node_id;
    if (!nodeId) {
      Message.error(`${drillWhere.interactive_name}未接入`);
      return;
    }

    if (isMetaMetricData(metricData!)) {
      onAnalysis({
        nodeId,
      });
    } else {
      const metricCaliber = metricData?.atom_metric_caliber_list?.find(
        ({ metric_data, complex_meta_data }) =>
          (complex_meta_data || metric_data)?.id === metric_alias_id,
      );
      const metricName =
        metricCaliber?.complex_meta_data?.name ||
        metricCaliber?.metric_data?.display_name;
      if (metricName) {
        // 匹配到元指标，则下钻查看指标详情
        onAnalysis({
          nodeId,
          metricInfo: {
            id: metric_alias_id,
            name: metricName,
            type: MetricTypeMeta,
          },
        });
      } else {
        onAnalysis({
          nodeId,
        });
      }
    }
  };

  const drillModal = useModal(DrillModal);

  // 查看明细下钻
  const handleDrill = (drillWhere: DrillWhere) => {
    XLog({
      id: 'webclick',
      page_id: LogSceneMap.metric.value,
      page_title: LogSceneMap.metric.type,
      btn_title: {
        metric: isMetaMetricData(metricData!)
          ? metricData.display_name
          : metricData?.name,
        event: LogEventMap.metric.drillModal,
      },
    });
    drillModal.show({
      nodeId,
      queryParams,
      drillWhere,
      drillConfig: getMetricDrillConfig(metricData),
    });
  };

  const handleFetch = useCallback(
    (cacheable = true) => {
      if (!dimensionId || !analysisDimensionList?.length || !metricData) {
        return;
      }
      const params = {
        chartType: CHART_TYPE.SHEET,
        timeFilterInfo,
        nodeId,
        domainMeasureObjFilter,
        measureDimensionId: dimensionId,
        customParams: {
          query_info: {
            order_list: [
              {
                alias_id: isMetaMetricData(metricData)
                  ? metricData.id
                  : metricData.complex_metric_list[0].id,
                order:
                  metricData.metric_attribute?.analysis_direction === 'Negative'
                    ? 'desc'
                    : 'asc', // 正向指标小到大，负向指标大到小
              },
            ],
          },
        },
        spaceId,
      };
      run(params, cacheable);
    },
    [
      ...timeFilterInfo?.range, // 避免仅有时间类型改变造成重复请求
      // TODO: 等后端接口字段优化调整，目前依赖里不需要加metricData，存在度量对象id则必然存在metricData,不然导致useeffect多次执行
      dimensionId,
      domainMeasureObjFilter.measureObjId,
      domainMeasureObjFilter.filter?.sort()?.join(''),
      run,
      spaceId,
    ],
  );

  useEffect(() => {
    handleFetch();
  }, [handleFetch]);

  // 触发无缓存请求
  useCacheEvent({
    eventName: cacheEvent,
    callback: () => handleFetch(false),
  });

  return (
    <>
      <div className="mx-3 mb-4">
        <Radio.Group
          type="button"
          value={dimensionId}
          style={{ width: 'calc(100% - 200px)' }}
          onChange={setDimensionId}>
          {analysisDimensionList.map(({ dim_id, display_name }) => (
            <Radio key={dim_id} value={dim_id}>
              {display_name}
            </Radio>
          ))}
        </Radio.Group>
      </div>
      <div
        className={cs({
          'h-[550px]':
            (dataSource?.metric_sheet_data?.series?.length ?? 0) >= 10,
          'h-[300px]':
            (dataSource?.metric_sheet_data?.series?.length ?? 0) < 10,
        })}>
        <VisualPanel
          chartClassName="!pb-0"
          getSpaceJumpWarp={getSpaceJumpWarp}
          loading={loading}
          drillWhere={[]}
          onDrill={handleDrill}
          onDrillBack={noop}
          hideMenus={hideMenus}
          selectedArray={selectedArray}
          onAnalysis={handleMetricAnalysis}
          hasMenuMetrics={[]}
          timeGranularitys={[]}
          dimensionConf={{}}
          displayConfig={displayConfig}
          dataSource={analysisDimensionList?.length ? dataSource : undefined}
          query_info={queryInfo}
          // analysisMetricIds={getAnalysisMetricIds(metricData)}
          chart_detail_info={
            { meta_drill_config: getMetricDrillConfig(metricData) } as any
          }
          showDrillMenuList={showDrillMenuList}
          isEmployeeDim={isEmployeeDim}
          dimensionId={dimensionId}
        />
      </div>
    </>
  );
});

export default SheetChart;
