import { useEffect } from 'react';

import { Checkbox } from '@arco-design/web-react';

const selectOptions = [
  {
    label: '占比',
    value: 'proportion_ratio',
  },
  {
    label: '环比',
    value: 'period_diff_ratio',
  },
];

export enum DataType {
  ProportionRatio = 'proportion_ratio', // 占比
  PeriodDiffRatio = 'period_diff_ratio', // 环比
}
const SelectStatisticsButton = ({
  selectedArray,
  isStatistics,
}: {
  selectedArray: (selectedArray: string[]) => void;
  isStatistics: boolean;
}) => {
  const CheckboxGroup = Checkbox.Group;
  const useCheckbox = Checkbox.useCheckbox;
  const defaultValues = isStatistics
    ? [DataType.ProportionRatio, DataType.PeriodDiffRatio]
    : [DataType.PeriodDiffRatio];
  const defaultOptions = isStatistics
    ? selectOptions
    : selectOptions.filter(item => item.value !== DataType.ProportionRatio);
  useEffect(() => {
    selectedArray(defaultValues);
  }, []);

  const { selected, setSelected } = useCheckbox(
    selectOptions.map(x => x.value),
    defaultValues,
  );

  return (
    <CheckboxGroup
      style={{ position: 'relative', top: 70 }}
      value={selected}
      options={defaultOptions}
      onChange={value => {
        setSelected(value);
        selectedArray(value);
      }}
    />
  );
};

export default SelectStatisticsButton;
