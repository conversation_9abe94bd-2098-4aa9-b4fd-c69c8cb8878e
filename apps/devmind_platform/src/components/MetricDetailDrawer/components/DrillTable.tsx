import { FC, useMemo, useRef, useState } from 'react';

import {
  <PERSON><PERSON>,
  But<PERSON>,
  Message,
  Notification,
  Popover,
} from '@arco-design/web-react';
import type { ModalProps } from '@arco-design/web-react';
import { IconDownload } from '@arco-iconbox/react-bits';
import { useRequest } from '@byted/hooks';
import { useModal } from '@ebay/nice-modal-react';
import { useModel } from '@jupiter/plugin-runtime/model';
import { noop } from 'lodash';

import { DisplayConfig } from '@quality/common-apis/src/apis/api/base/display_config';
import { queryBaseFeatureServiceClient } from '@quality/common-apis/src/apis/api/query/base_feature';
import {
  queryServiceClient,
  QueryRequest,
  DrillWhere,
  QueryInfo,
  DrillLayer,
} from '@quality/common-apis/src/apis/api/query/query';
import { VisualPanel } from '@quality/common-components';
import { QUERYCACHE, QUERYCACHE_DISABLE } from '@quality/common-utils';

import { API_V1 } from '@/constants';
import globalModel from '@/model/global';
import { download, s2ab } from '@/utils/downloadFile';

import DrillModal from './DrillModal';

export interface Props extends ModalProps {
  nodeId: string;
  queryParams?: QueryRequest | undefined;
  drillWhere?: DrillWhere | undefined;
  showOutputBtn?: boolean;
  drillConfig?: { [key: string]: Array<DrillLayer> };
}

const DrillTable: FC<Props> = props => {
  const {
    queryParams,
    nodeId,
    drillWhere,
    drillConfig,
    showOutputBtn: _showOutputBtn,
  } = props;
  const [{ nodeIdRecord }] = useModel([globalModel]);
  const currentOrgName = nodeIdRecord[nodeId]?.node_name;
  const drillIdRef = useRef({});
  const drillWhereList = useMemo<DrillWhere[]>(
    () =>
      // 卡片图表需要自己构造drillWhere
      drillWhere
        ? [drillWhere]
        : queryParams
        ? [
            {
              metric_alias_id:
                queryParams.query_info.select_alias_id_list[0].alias_id,
              drill_type: 'detail',
              interactive_name: '',
              x_index: '',
            } as DrillWhere,
          ]
        : [],
    [queryParams, drillWhere],
  );

  const { loading, data } = useRequest(
    async () => {
      if (!queryParams || !drillWhereList.length) {
        return;
      }

      const res = await queryServiceClient.Query(
        {
          ...queryParams,
          query_info: {
            ...(queryParams.query_info as any),
            current_org_name: currentOrgName,
          } as any,
          drill_where_list: drillWhereList,
          version: API_V1,
        },
        { [QUERYCACHE]: QUERYCACHE_DISABLE, hideNotice: true },
      );
      if (res.code !== 200) {
        Notification.error({
          title: '请求失败',
          content: res.msg,
        });
      }
      return res;
    },
    {
      auto: true,
      refreshDeps: [drillWhereList, queryParams, currentOrgName],
    },
  );

  const drillModal = useModal(DrillModal);
  const handleDrill = (record?: any, data?: any) => {
    drillIdRef.current = record;

    drillModal.show({
      nodeId,
      queryParams,
      drillWhere,
      drillConfig,
      filterOption: record,
      drillData: data,
      warmupExtra: queryParams?.warmup_extra,
      drillModels: relatedModels,
    });
  };

  let relatedModels: any[] = [];
  try {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const models = JSON.parse(data?.data?.related_models || '') || [];
    relatedModels = Array.isArray(models) ? models : [models];
  } catch (error) {}

  return (
    <div className="h-[500px] flex flex-col">
      {queryParams?.warmup_extra?.seal_version &&
        data?.data &&
        !data?.msg.includes('seal') && (
          <Alert
            type="info"
            showIcon={true}
            content="当前数据为实时数据，可能与快照版本存在差异"
            closable={true}
            style={{
              position: 'absolute',
              marginLeft: 10,
              padding: '4px 12px',
              width: 'calc(100% - 60px)',
              height: 32,
            }}
          />
        )}
      <DownloadButton
        loading={loading}
        drillWhereList={drillWhereList}
        queryParams={queryParams}
        nodeId={nodeId}
        nodeIdRecord={nodeIdRecord}
      />
      <VisualPanel
        loading={loading}
        displayConfig={queryParams?.display_config as DisplayConfig}
        chart_detail_info={{
          chart_id: '',
          query_info: queryParams?.query_info as QueryInfo,
          drill: {
            layer_list: [],
            detail_config: [],
          },
          attribution_info: [],
          display_config: queryParams?.display_config as DisplayConfig,
          chart_name: '',
          description: '',
          owners: [],
          page_list: [],
          version: 0,
          link_list: [],
          chart_type: '',
          meta_drill_config: drillConfig,
        }}
        drillWhere={[]}
        pagination={{
          pageSize: 20,
          sizeOptions: [20, 50, 100],
        }}
        queryDrillWhere={drillWhereList}
        onDrill={noop}
        onDrillBack={noop}
        hasMenuMetrics={[]}
        timeGranularitys={[]}
        dimensionConf={[]}
        dataSource={data?.data}
        operateCols={
          relatedModels.length
            ? [
                {
                  fixed: 'right',
                  title: '操作',
                  dataIndex: 'operation',
                  render: (_, record) => {
                    return (
                      <a
                        onClick={() =>
                          handleDrill(record, data?.data?.metric_sheet_data)
                        }>
                        关联数据
                      </a>
                    );
                  },
                  width: 100,
                },
              ]
            : []
        }
      />
    </div>
  );
};

const DownloadButton = ({
  loading,
  queryParams,
  drillWhereList,
  nodeId,
  nodeIdRecord,
}) => {
  const [isDownload, setIsDownload] = useState(false);
  const currentOrgName = nodeIdRecord[nodeId]?.node_name;

  const handleExportData = async () => {
    if (!queryParams) {
      return;
    }
    Message.success({
      id: '明细下钻',
      content: '开始下载,请稍后',
      duration: 2000,
    });
    setIsDownload(true);
    try {
      const param = {
        request_list: [
          {
            query_info: {
              ...(queryParams?.query_info as any),
              current_org_name: currentOrgName,
              drill_where_list: drillWhereList,
            } as any,
            sheet_name: '明细下钻',
          },
        ],
        version: 1,
        position: '明细下钻',
      };
      const res = await queryBaseFeatureServiceClient.GetDownloadCsv(param, {
        [QUERYCACHE]: QUERYCACHE_DISABLE,
      });
      setIsDownload(false);
      if (res.code !== 200) {
        Message.error({ id: '明细下钻', content: res.msg });
      }
      const content = res?.data?.content;
      if (content) {
        download(s2ab(atob(content)), `${res?.data.file_name}`, {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;',
        });
        Message.success({ id: '明细下钻', content: '下载完成' });
      } else {
        Message.error({ id: '明细下钻', content: '暂无数据下载' });
      }
    } catch (error) {
      setIsDownload(false);
      Message.error({ id: '明细下钻', content: '下载请求超时' });
    }
  };

  return (
    <div style={{ textAlign: 'right', paddingRight: 12 }}>
      <Popover content="最大支持50000条" position="tr">
        <Button
          onClick={handleExportData}
          icon={<IconDownload />}
          disabled={loading || isDownload}
          loading={isDownload}
          // type="text"
          style={{ color: '#222' }}
          iconOnly
        />
      </Popover>
    </div>
  );
};

export default DrillTable;
