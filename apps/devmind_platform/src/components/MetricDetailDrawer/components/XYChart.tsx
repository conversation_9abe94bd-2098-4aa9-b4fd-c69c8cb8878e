import { FC } from 'react';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@arco-design/web-react';
import { IconShareInternal } from '@arco-iconbox/react-bits';
import { useModal } from '@ebay/nice-modal-react';
import dayjs from 'dayjs';
import { noop } from 'lodash';

import type { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { DisplayConfig } from '@quality/common-apis/src/apis/api/base/display_config';
import type {
  DrillWhere,
  QueryInfo,
  QueryRequest,
} from '@quality/common-apis/src/apis/api/query/query';
import { MetricTypeMeta } from '@quality/common-apis/src/apis/api/zepto/consts';
import { VisualPanel } from '@quality/common-components';

import {
  TimeFilterValue,
  TimeGranularity,
  TimeCycle,
} from '@/components/TimeFilter';
import { getTimeRange } from '@/components/TimeFilter/utils';
import { LogEventMap, LogSceneMap } from '@/constants/log';
import { getTrendAnalysisMeasureHref } from '@/utils/url';
import { XLog } from '@/utils/utils';

import { MetricData, MetricInfo } from '../interface';
import {
  getMetricDrillConfig,
  getAnalysisMetricIds,
  isMetaMetricData,
} from '../utils';

import DrillModal from './DrillModal';

const { RangePicker } = DatePicker;

interface Props {
  nodeId: string;
  timeFilterInfo: TimeFilterValue;
  metricData: MetricData | undefined;
  onAnalysis: (options: {
    timeFilterInfo: TimeFilterValue;
    metricInfo?: MetricInfo;
  }) => void;
  hideMenus?: string[];
  dataSource?: QueryData;
  displayConfig: DisplayConfig;
  queryParams?: QueryRequest;
  queryInfo?: QueryInfo;
  loading: boolean;
  timeRange: string[];
  onChangeTime?: (dateString: string[], date: dayjs.Dayjs[]) => void;
  freezeVersion: string | undefined;
}

const XYChart: FC<Props> = props => {
  const {
    timeFilterInfo,
    nodeId,
    metricData,
    onAnalysis,
    dataSource,
    displayConfig,
    queryParams,
    queryInfo,
    loading,
    hideMenus,
    timeRange,
    onChangeTime,
    freezeVersion,
  } = props;

  const analysisMetricIds = getAnalysisMetricIds(metricData);

  // const [{ freezeVersion }] = useModel([
  //   metricDetailDrawerModel,
  //   ({ config: { freezeVersion } }) => ({ freezeVersion }),
  // ]);

  const handleMetricAnalysis = (drillWhere: DrillWhere) => {
    const { x_index, metric_alias_id } = drillWhere;
    let startTime = x_index;
    const timeGranularity = timeFilterInfo.granularity;

    // 双周返回的x轴数据格式为 xxxx-xx-xx ~ xxxx-xx-xx 需要特殊匹配处理
    if (timeGranularity === TimeGranularity.DoubleWeek) {
      startTime = x_index.match(/[0-9]+-[0-9]+-[0-9]+/)?.[0] ?? startTime;
    }
    // 双月和自定义周 返回的x轴数据格式为 xxxx-xx ~ xxxx-xx 需要特殊匹配处理
    if (
      timeGranularity === TimeGranularity.DoubleMonth ||
      (timeGranularity === TimeGranularity.Week &&
        timeFilterInfo.cycle === TimeCycle.Custom)
    ) {
      startTime = x_index.match(/[0-9]+-[0-9]+/)?.[0] ?? startTime;
    }
    if (timeGranularity === TimeGranularity.Quarter) {
      startTime = `${x_index.replace(/Q\d/, '')}${
        (Number(x_index[x_index.length - 1]) - 1) * 3 + 1
      }-01`;
    }

    let day = dayjs(startTime);
    let filterDay = dayjs(timeFilterInfo.range[0]);

    // 自定义锚点时，后端暂时无法返回具体的自定义时间日期，需要前端处理和顶部时间筛选器时间日期比对对齐
    if (
      [
        TimeGranularity.Month,
        TimeGranularity.DoubleMonth,
        TimeGranularity.Year,
        // TimeGranularity.MidYearPerformance,
        // TimeGranularity.AnnualPerformance,
      ].includes(timeGranularity)
    ) {
      if (day.date() !== filterDay.date()) {
        day = day.date(filterDay.date());
      }
    }
    if (
      [
        TimeGranularity.Year,
        // TimeGranularity.MidYearPerformance,
        // TimeGranularity.AnnualPerformance,
      ].includes(timeGranularity) &&
      day.month() !== filterDay.month()
    ) {
      day = day.month(filterDay.month());
    }

    const timeRange = getTimeRange(day, {
      granularity: timeGranularity,
      standard: timeFilterInfo.cycle === TimeCycle.Standard,
    });

    if (isMetaMetricData(metricData!)) {
      onAnalysis({
        timeFilterInfo: {
          ...timeFilterInfo,
          range: timeRange,
        },
      });
    } else {
      const metricCaliber = metricData?.atom_metric_caliber_list?.find(
        ({ metric_data, complex_meta_data }) =>
          (complex_meta_data || metric_data)?.id === metric_alias_id,
      );
      const metricName =
        metricCaliber?.complex_meta_data?.name ||
        metricCaliber?.metric_data?.display_name;
      if (metricName) {
        // 匹配到元指标，则下钻查看指标详情
        onAnalysis({
          timeFilterInfo: {
            ...timeFilterInfo,
            range: timeRange,
          },
          metricInfo: {
            id: metric_alias_id,
            name: metricName,
            type: MetricTypeMeta,
          },
        });
      } else {
        onAnalysis({
          timeFilterInfo: {
            ...timeFilterInfo,
            range: timeRange,
          },
        });
      }
    }
  };

  const drillModal = useModal(DrillModal);

  // 查看明细下钻
  const handleDrill = (drillWhere: DrillWhere) => {
    XLog({
      id: 'webclick',
      page_id: LogSceneMap.metric.value,
      page_title: LogSceneMap.metric.type,
      btn_title: {
        metric: isMetaMetricData(metricData!)
          ? metricData.display_name
          : metricData?.name,
        event: LogEventMap.metric.drillModal,
      },
    });
    drillModal.show({
      nodeId,
      queryParams,
      drillWhere,
      drillConfig: getMetricDrillConfig(metricData),
    });
  };

  const jumpToMeasure = () => {
    const href = getTrendAnalysisMeasureHref(
      dataSource!.query_id,
      metricData ? isMetaMetricData(metricData) : false,
    );

    href && window.open(href);
  };

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <Tooltip
          content={freezeVersion ? '当前报告已封板，无法执行此操作' : ''}>
          <span>
            <RangePicker
              value={timeRange}
              disabled={Boolean(freezeVersion)}
              className="w-[270px]"
              allowClear={false}
              onChange={onChangeTime}
            />
          </span>
        </Tooltip>
        {/* IFTRUE_prodEnv */}
        {!freezeVersion && dataSource && (
          <Button type="text" onClick={jumpToMeasure} disabled={loading}>
            高级分析
            <IconShareInternal />
          </Button>
        )}
        {/* FITRUE_prodEnv */}
      </div>
      <div className="h-[260px]">
        <VisualPanel
          loading={loading}
          drillWhere={[]}
          onDrillBack={noop}
          hasMenuMetrics={[]}
          timeGranularitys={[]}
          hideMenus={hideMenus}
          dimensionConf={{}}
          displayConfig={displayConfig}
          dataSource={dataSource}
          query_info={queryInfo}
          analysisMetricIds={analysisMetricIds}
          chart_detail_info={
            { meta_drill_config: getMetricDrillConfig(metricData) } as any
          }
          onAnalysis={handleMetricAnalysis}
          onDrill={handleDrill}
        />
      </div>
    </>
  );
};

export default XYChart;
