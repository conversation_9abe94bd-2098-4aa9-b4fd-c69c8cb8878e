import { useRef } from 'react';

import { Modal, Form, Checkbox } from '@arco-design/web-react';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import {
  SortableContainer as sortableContainer,
  SortableElement as sortableElement,
} from 'react-sortable-hoc';

const SortableContainer = sortableContainer(({ children }) => (
  <div className="sort-container">{children}</div>
));
const SortableItem = sortableElement(({ children }) => (
  <div className="item-line">{children}</div>
));

const ConfigModal = NiceModal.create(() => {
  const { form } = Form.useFormContext();
  const containerRef = useRef<any>();
  const modal = useModal();

  const onOk = () => {
    form.validate().then(data => {
      console.log({ data });
    });
    // modal.remove();
  };

  return (
    <Modal
      title="对比分析维度配置"
      visible={modal.visible}
      onOk={onOk}
      onCancel={modal.remove}>
      <Form
        layout="vertical"
        initialValues={{
          configsField: [
            { id: 'xxxx', status: true },
            { id: 'xxx2x', status: true },
            { id: 'xxx3x', status: true },
            { id: 'xxx3x', status: false },
          ],
        }}>
        <Form.List field={'configsField'}>
          {(fields, { move }) => {
            const configs: Array<{ id: string; status: boolean }> =
              form?.getFieldValue('configsField') || [];

            return (
              <>
                <SortableContainer
                  helperContainer={() => containerRef.current}
                  transitionDuration={0}
                  axis="y"
                  lockAxis="y"
                  disableAutoscroll={true}
                  onSortEnd={({ oldIndex, newIndex }) =>
                    move(oldIndex, newIndex)
                  }
                  useDragHandle>
                  {fields.map((item, index) => {
                    const config = configs[index] as {
                      id: string;
                      status: boolean;
                    };

                    return (
                      <SortableItem key={config.id} index={index}>
                        <Checkbox
                          onChange={v => form.setFieldValue(item.field, v)}
                          value={config.status}>
                          {config.id}
                        </Checkbox>
                      </SortableItem>
                    );
                  })}
                </SortableContainer>
              </>
            );
          }}
        </Form.List>
      </Form>
    </Modal>
  );
});

export default ConfigModal;
