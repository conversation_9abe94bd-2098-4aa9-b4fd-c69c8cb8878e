import { CSSProperties, FC, useMemo, useState } from 'react';

import { Cascader } from '@arco-design/web-react';
import { useRequest } from 'ahooks';
import { last } from 'lodash';

import { insightServiceClient } from '@quality/common-apis/src/apis/api/insight/homepage';
import { queryServiceClient } from '@quality/common-apis/src/apis/api/query/query';
import type { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import type { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import { CHART_TYPE } from '@quality/common-components';
import BaselineTip from '@quality/common-components/src/VisualPanel/BaselineTip';

import { DomainMeasureObjFilterValue } from '@/components/DomainMeasureObjFilter';
import { TimeFilterValue } from '@/components/TimeFilter';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { NodeManager } from '@/modules/node';

import { getWarmUpExtra } from '../hooks';
import {
  getMetaMetricQueryParams,
  getComplexMetaMetricQueryParams,
  isMetaMetricData,
  getDefaultTimeRange,
} from '../utils';

interface Props {
  metricData: MetricMetaData | ComplexMetricMetaData | undefined;
  nodeId: string;
  timeFilterInfo: TimeFilterValue;
  domainMeasureObjFilter: DomainMeasureObjFilterValue;
  onChange: (id: string) => void;
  freezeVersion?: string;
  style?: CSSProperties;
}

const BaselineSelect: FC<Props> = props => {
  const {
    metricData,
    nodeId,
    timeFilterInfo,
    domainMeasureObjFilter,
    onChange,
    freezeVersion,
    style,
  } = props;

  // const { urlQuery: { spaceId: virtualSpaceId } } = useSpaceUrlQuery()
  const { virtualItem, virtualSpaceKey } = useVirtualSpaceType();
  const virtualSpaceId = virtualItem?.SpaceId;
  const [value, setValue] = useState<string[]>([]);

  const { data } = useRequest(
    async () => {
      if (!metricData || !virtualSpaceId) {
        return;
      }
      const range = getDefaultTimeRange(
        timeFilterInfo.range,
        timeFilterInfo.granularity,
      );
      const params = isMetaMetricData(metricData)
        ? getMetaMetricQueryParams(
            metricData,
            {
              chartType: CHART_TYPE.DOUBLE_AXIS,
              filterOptions: {
                spaceType: virtualSpaceKey,
                nodeName:
                  NodeManager.getModule()?.getNodeInfo(nodeId)
                    .inject_filter_value ?? '',
                timeFilterInfo: {
                  ...timeFilterInfo,
                  range,
                },
                domainMeasureObjFilter,
              },
            },
            virtualSpaceId,
          )
        : await getComplexMetaMetricQueryParams(
            metricData,
            {
              chartType: CHART_TYPE.DOUBLE_AXIS,
              filterOptions: {
                spaceType: virtualSpaceKey,
                nodeName:
                  NodeManager.getModule()?.getNodeInfo(nodeId)
                    .inject_filter_value ?? '',
                timeFilterInfo: {
                  ...timeFilterInfo,
                  range,
                },
                domainMeasureObjFilter,
              },
            },
            insightServiceClient.GetComplexMetricById.bind(
              insightServiceClient,
            ),
            virtualSpaceId,
          );
      if (!params) {
        return;
      }
      const res = await queryServiceClient.GetValidBaseLine({
        ...params,
        warmup_extra: getWarmUpExtra(metricData, freezeVersion),
      });

      const measureObjId = res.data?.[0]?.measure_obj?.id;
      const validBaselineId = res.data?.[0]?.baselines?.[0]?.id;

      if (measureObjId && validBaselineId) {
        setValue([measureObjId, validBaselineId]);
      }
      return res.data;
    },
    {
      ready:
        Boolean(metricData) &&
        Boolean(
          NodeManager.getModule()?.getNodeInfo(nodeId).inject_filter_value,
        ),
      refreshDeps: [
        virtualSpaceId,
        metricData,
        NodeManager.getModule()?.getNodeInfo(nodeId).inject_filter_value,
        domainMeasureObjFilter.measureObjId,
        domainMeasureObjFilter.filter?.sort()?.join(''),
        ...timeFilterInfo.range,
      ],
    },
  );

  const options = useMemo(
    () =>
      data?.reduce<
        {
          label: string;
          value: string;
          children: { label: string; value: string }[];
        }[]
      >((prev, { measure_obj, baselines = [] }) => {
        const item = {
          label: measure_obj.display_name,
          value: measure_obj.id,
          children: baselines.map(({ id, rules }) => ({
            label: rules?.[0].rule_hint?.display_name ?? '',
            value: id,
          })),
        };
        prev.push(item);
        return prev;
      }, []) ?? [],
    [data],
  );

  const baselineData =
    data
      ?.find(({ measure_obj }) => measure_obj.id === value[0])
      ?.baselines?.find(({ id }) => id === value[1]) ?? [];

  const handleChange = (v: (string | string[])[]) => {
    setValue(v as string[]);
    const baselineId = last(v) as string;
    baselineId && onChange(baselineId);
  };

  return options.length > 0 ? (
    <div className="flex items-center gap-2">
      <Cascader
        style={style}
        className="w-[360px]"
        prefix="基线:"
        value={value}
        options={options}
        onChange={handleChange}
      />
      {/* IFTRUE_prodEnv */}
      <BaselineTip baselineData={baselineData} />
      {/* FITRUE_prodEnv */}
    </div>
  ) : null;
};

export default BaselineSelect;
