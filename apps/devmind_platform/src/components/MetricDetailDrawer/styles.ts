import { Drawer, Modal, Tabs } from '@arco-design/web-react';
import styled from '@jupiter/plugin-runtime/styled';

export const SCDrawer = styled(Drawer)`
  .arco-drawer-wrapper {
    z-index: 900;
  }

  .arco-drawer-header {
    width: 100%;
    height: 90px;
    padding: 0 0 0 32px;
    .arco-drawer-header-title {
      width: calc(100% - 44px);
    }
  }
  .arco-drawer-close-icon {
    right: 32px !important;
    top: 39px !important;
  }
  .arco-drawer-content {
    padding: 20px 24px;
    border-top: 4px;
    background: ${({ theme }) => theme.colors.fill2};
  }
`;

export const SCModal = styled(Modal)`
  .arco-modal-content {
    padding-left: 0;
    padding-right: 0;
    padding: 0;
  }
`;
export const SCBlock = styled.div`
  background: #fff;
  border: ${({ theme }) => `1px solid ${theme.borderColor.line2}`};
  border-radius: 4px;
  overflow-y: auto;
`;
export const SCBlockHeader = styled.div`
  padding: 0 24px;
  height: 64px;
  border-bottom: ${({ theme }) => `1px solid ${theme.borderColor.line2}`};
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const SCTabs = styled(Tabs)`
  padding: 12px 20px 0 20px;
`;
