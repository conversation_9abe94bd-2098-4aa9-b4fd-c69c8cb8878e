import { FC, useEffect, useRef } from 'react';

import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { useModel } from '@jupiter/plugin-runtime/model';

import { uuid } from '@quality/common-utils/src/uuid';

import { LogSceneMap, LogEventMap } from '@/constants/log';
import { XLog } from '@/utils/utils';

import Drawer from './Drawer';
import { useDetect } from './hooks';
import { MetricDetailDrawerConfig } from './interface';
import metricDetailDrawerModel from './model';

let METRIC_DETAIL_DRAWER_FLAG = '';

const MetricDetailInner = NiceModal.create(() => {
  const modal = useModal();

  const [{ config }] = useModel([
    metricDetailDrawerModel,
    ({ config }) => ({ config }),
  ]);

  const flagRef = useRef<string>(uuid());

  // 卸载的时候清空全局变量
  useEffect(
    () => () => {
      METRIC_DETAIL_DRAWER_FLAG = '';
    },
    [],
  );

  if (!METRIC_DETAIL_DRAWER_FLAG) {
    METRIC_DETAIL_DRAWER_FLAG = flagRef.current;
  }

  if (METRIC_DETAIL_DRAWER_FLAG !== flagRef.current) {
    return null;
  }
  console.log('reloadParent,displayconfig');
  if (modal.visible) {
    return (
      <Drawer
        {...config}
        visible={modal.visible}
        onCancel={modal.hide}
        afterClose={modal.remove}
      />
    );
  }
  return null;
});

const MetricDetailDrawer: FC & {
  useToggle: () => {
    open: (config: MetricDetailDrawerConfig) => void;
    close: () => void;
  };
} = () => {
  const modal = useModal(MetricDetailInner);

  useDetect(modal);

  return null;
};

MetricDetailDrawer.useToggle = () => {
  const modal = useModal(MetricDetailInner);
  const [_, { setState }] = useModel([metricDetailDrawerModel, () => ({})]);

  return {
    open: (config: MetricDetailDrawerConfig) => {
      XLog({
        id: 'webclick',
        page_id: LogSceneMap.metric.value,
        page_title: LogSceneMap.metric.type,
        btn_title: {
          metric: config.initialMetric.name,
          event: LogEventMap.metric.openDrawer,
        },
      });
      setState({
        config,
      });
      modal.show();
    },
    close: () => {
      modal.remove();
    },
  };
};

export default MetricDetailDrawer;
