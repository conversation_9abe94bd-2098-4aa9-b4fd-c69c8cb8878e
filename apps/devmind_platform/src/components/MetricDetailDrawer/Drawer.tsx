import { FC, useEffect, useState, useMemo, useRef, useCallback } from 'react';

import {
  Button,
  Divider,
  Radio,
  Spin,
  Popover,
  Message,
  Space,
  Dropdown,
  Menu,
  Link,
} from '@arco-design/web-react';
import {
  IconLink,
  IconList,
  IconSync,
  IconSettings,
} from '@arco-design/web-react/icon';
import { IconMore, IconDownload } from '@arco-iconbox/react-bits';
import { MetricUseStatistics } from '@devmind/server/app/constants';
import { useModel } from '@reduck/core';
import { useBoolean, useRequest } from 'ahooks';
import cs from 'classnames';
import copy from 'copy-to-clipboard';
import { cloneDeep, initial, findKey } from 'lodash';
import { MacScrollbar } from 'mac-scrollbar';
import { useRouteMatch } from 'react-router-dom';

import { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { insightServiceClient } from '@quality/common-apis/src/apis/api/insight/homepage';
import { reportTemplateServiceClient } from '@quality/common-apis/src/apis/api/insight_report/report';
import {
  DownloadRequest,
  queryBaseFeatureServiceClient,
} from '@quality/common-apis/src/apis/api/query/base_feature';
import {
  QueryRequest,
  QueryInfo,
} from '@quality/common-apis/src/apis/api/query/query';
import type { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import {
  MetricTypeMeta,
  MetricTypeComplexMeta,
} from '@quality/common-apis/src/apis/api/zepto/consts';
import type { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import { CHART_TYPE } from '@quality/common-components';
import {
  cancelAllRequest,
  QUERYCACHE,
  QUERYCACHE_ENABLE,
} from '@quality/common-utils';

import FreezeList from '@/components/FreezeList';
import MetricName from '@/components/MetricName';
import { SCSpinWrap } from '@/components/style';
import TimeFilter, {
  getTimeCycle,
  getTimeGranularity,
  TimeFilterValue,
} from '@/components/TimeFilter';
import { API_V1, EDITOR_MODE } from '@/constants';
import { isMiniScreen } from '@/constants/adapt';
import { Authority } from '@/constants/auth';
import { LogEventMap, LogSceneMap } from '@/constants/log';
import { useCacheEvent } from '@/hooks';
import { useReportConfigUrlQuery } from '@/hooks/url';
import useMetricDrawerUrlQuery from '@/hooks/url/useMetricDrawerUrlQuery';
import { DEFAULT_CACHE_EVENT } from '@/hooks/useCacheEvent';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { SpaceType } from '@/interface';
import globalModel from '@/model/global';
import { reportModel } from '@/model/report';
import { NodeManager } from '@/modules/node';
import { DevmindUrls, isSubModule } from '@/routers/path';
import { download, s2ab } from '@/utils/downloadFile';
import { getShortLink } from '@/utils/login';
import { isMetricList, metricManageUrlBy } from '@/utils/url';
import { openURL, XLog } from '@/utils/utils';

import DomainMeasureObjFilter, {
  DomainMeasureObjFilterValue,
} from '../DomainMeasureObjFilter';
import VirtualArea from '../Layout/BaseLayout/VirtualArea';

import Analysis, { REASON_CACHE_EVENT } from './components/Analysis';
import BaselineSelect from './components/BaselineSelect';
import CardChart from './components/CardChart';
import SelectStatisticsButton from './components/SelectStatisticsButton';
import SheetChart from './components/SheetChart';
import XYChart from './components/XYChart';
import { useQueryExtraParams, useQueryRequest } from './hooks';
import {
  MetricInfo,
  AnalysisRadioValue,
  MetricData,
  MetricDetailDrawerProps,
  GetSpaceJumpWarp,
} from './interface';
import { SCDrawer, SCBlock, SCBlockHeader } from './styles';
import {
  getDefaultTimeRange,
  isMetaMetricData,
  eventBusLoadingModel,
  getMetricDataFormRes,
} from './utils';

const analysisRadioOptions = [
  AnalysisRadioValue.Conclusion,
  AnalysisRadioValue.About,
];

// eslint-disable-next-line complexity
const MetricDrawer: FC<MetricDetailDrawerProps> = props => {
  const {
    initialMetric,
    initialNodeId,
    initialTime = defaultTimeValue,
    initialDomainMeasureObjFilter = { measureObjId: '', filter: [] },
    className,
    ...rest
  } = props;
  const [state] = useModel(eventBusLoadingModel);
  const [{ metrics, viewMode, freezeVersion: reportFreezeVersion }] =
    useModel(reportModel);
  const { inReportDetail, params } = useQueryExtraParams();

  const { virtualItem } = useVirtualSpaceType();
  const spaceId = virtualItem?.SpaceId ?? '';

  const [{ globalMetricItem, authData }] = useModel(globalModel);

  // 如果获取不到，说明是控制台点进去的，需要去global拿 spaceType
  const spaceType =
    NodeManager.getModule()?.getSpaceType() ?? globalMetricItem.virtualSpaceKey;

  const [freezeVersion, setFreezeVersion] = useState<string | undefined>(
    props.freezeVersion,
  );
  const [freezed, setFreezed] = useState<boolean>(Boolean(props.freezeVersion));
  const [firstDataSource, setFirstDataSource] = useState<QueryData | undefined>(
    undefined,
  );

  const { urlQuery, setUrlQuery } = useMetricDrawerUrlQuery({
    _metricId: initialMetric.id,
    _metricType: initialMetric.type,
    _metricName: initialMetric.name,
    _metricNodeId: initialNodeId,
    _metricTimeGranularity: initialTime.granularity,
    _metricTimeCycle: initialTime.cycle,
    _metricStartTime: initialTime.range[0],
    _metricEndTime: initialTime.range[1],
    _metricDomainMeasureObjId: initialDomainMeasureObjFilter.measureObjId,
    _metricDomainMeasureObjFilter: initialDomainMeasureObjFilter.filter ?? [],
    freezeVersion,
  });
  const {
    urlQuery: { nodeId: reportNodeIdId = '', mode = EDITOR_MODE.Write },
  } = useReportConfigUrlQuery();
  const {
    _metricId: metricId,
    _metricType: metricType,
    _metricName: metricName = '',
    _metricNodeId: nodeId,
    _metricTimeGranularity = initialTime.granularity,
    _metricTimeCycle = initialTime.cycle,
    _metricStartTime = initialTime.range?.[0],
    _metricEndTime = initialTime.range?.[1],
    _metricDomainMeasureObjId,
    _metricDomainMeasureObjFilter = [],
    freezeVersion: urlFreezeVersion,
  } = urlQuery;
  const [
    showSheetCard,
    { setTrue: setShowSheetCardTrue, setFalse: setShowSheetCardFalse },
  ] = useBoolean(false);

  useEffect(() => {
    setFreezeVersion(props.freezeVersion || urlFreezeVersion);
    setFreezed(Boolean(props.freezeVersion || urlFreezeVersion));
  }, [urlFreezeVersion, props.freezeVersion]);
  const isMatchMetricList = useRouteMatch(DevmindUrls.VirtualSpaceMetricList);

  const visible = rest.visible || Boolean(metricId); // url参数用于判断自动开启的情况
  // const hasAdminAuth = NodeManager.getModule()?.validAuth(
  //   '-1',
  //   Authority.admin,
  // );
  // const hasWriteAuth = NodeManager.getModule()?.validAuth(
  //   '-1',
  //   Authority.write,
  // );

  // 针对中文字符需要做转化
  const cycle = getTimeCycle(_metricTimeCycle, _metricTimeCycle);
  const granularity = getTimeGranularity(
    _metricTimeGranularity,
    _metricTimeGranularity,
  );

  const timeFilterValue: TimeFilterValue = {
    granularity,
    cycle,
    range: [_metricStartTime, _metricEndTime],
  };

  const domainMeasureObjFilter = {
    measureObjId: _metricDomainMeasureObjId,
    filter: _metricDomainMeasureObjFilter,
  };

  const hasOutputAuth = true;

  // 初始化url query参数
  useEffect(() => {
    if (visible) {
      setUrlQuery({
        _metricId: metricId || initialMetric.id,
        _metricName: metricName || initialMetric.name,
        _metricType: metricType || initialMetric.type,
        _metricNodeId: nodeId || initialNodeId,
        _metricTimeGranularity:
          timeFilterValue.granularity || initialTime.granularity,
        _metricTimeCycle: timeFilterValue.cycle || initialTime.cycle,
        _metricStartTime: timeFilterValue.range[0] || initialTime.range[0],
        _metricEndTime: timeFilterValue.range[1] || initialTime.range[1],
        _metricDomainMeasureObjId:
          domainMeasureObjFilter.measureObjId ||
          initialDomainMeasureObjFilter.measureObjId,
        _metricDomainMeasureObjFilter: domainMeasureObjFilter.filter?.length
          ? domainMeasureObjFilter.filter
          : initialDomainMeasureObjFilter.filter ?? [],
      });
      setOpenedMetricStack([
        {
          id: metricId || initialMetric.id,
          name: metricName || initialMetric.name,
          type: metricType || initialMetric.type,
        },
      ]);
    }
  }, [visible]);

  const [openedMetricStack, setOpenedMetricStack] = useState<MetricInfo[]>([]);

  // 指标分析 卡片图表列表
  const [cardChartList, setCardChartList] = useState<
    Array<MetricData | undefined>
  >([]);

  const {
    data: _metricData,
    loading: _metricLoading,
    run: _getMetricData,
    mutate: _changeMetricData,
  } = useRequest<MetricMetaData | ComplexMetricMetaData | undefined, string[]>(
    async (id: string, projectId: string) => {
      // 只有报告里需要走缓存, 宽表不走
      if (!isMetricList() && viewMode === 'default') {
        const _reportFreezeVersion = reportFreezeVersion?.freeze_key || '';
        const cacheData = metrics?.get.metricModelMap().get(id)?.data;
        if (
          cacheData &&
          // 抽屉里nodeId、封板版本，与报告页面一致。则用缓存数据
          projectId === reportNodeIdId &&
          freezeVersion === _reportFreezeVersion
        ) {
          return getMetricDataFormRes(cacheData, metricType);
        }
      }
      const res =
        await reportTemplateServiceClient.GetMetricInfoAndExpertAnalysis({
          metric_id: id,
          metric_type: metricType,
          version: API_V1,
          space_type: spaceType,
          project_id: projectId,
          seal_version: urlQuery.freezeVersion,
        });
      return getMetricDataFormRes(res.data, metricType);
    },
    { manual: true },
  );

  // 指标详情数据
  const metricData = useMemo(() => {
    if (_metricData?.id === metricId) {
      return _metricData;
    }
    return undefined;
  }, [metricId, _metricData]);

  // 获取当前指标详情信息
  useEffect(() => {
    if (metricId && visible) {
      _getMetricData(metricId, nodeId);
    }
  }, [metricId, visible, nodeId, freezeVersion]);
  // 处理评分指标嵌套评分指标的特殊case
  useEffect(() => {
    (async () => {
      const list: Array<MetricData | undefined> = [];
      if (!metricData) {
        return;
      }
      if (isMetaMetricData(metricData)) {
        list.push(metricData);
      } else {
        list.push(metricData);

        // TODO:后端返回的评分指标数据有问题，需要前端再次调评分指标详情接口更新数据,后续需要优化对齐
        const transformAtomMetricCaliberList = await Promise.all(
          metricData.atom_metric_caliber_list.map(async item => {
            const { complex_meta_data } = item;
            if (complex_meta_data) {
              const res = await insightServiceClient.GetComplexMetricById({
                version: API_V1,
                complex_metric_id: complex_meta_data.id,
                seal_version: freezeVersion,
              });
              return {
                ...item,
                complex_meta_data: res.data,
              };
            }
            return item;
          }),
        );
        metricData.atom_metric_caliber_list?.length &&
          list.push(
            ...transformAtomMetricCaliberList.map(
              ({ metric_data, complex_meta_data }) =>
                metric_data || complex_meta_data,
            ),
          );
      }
      setCardChartList(list);
    })();
  }, [metricData?.id, freezeVersion]);

  let {
    loading,
    run,
    dataSource,
    queryInfo,
    data,
    displayConfig,
    params: queryParams,
  } = useQueryRequest<CHART_TYPE.DOUBLE_AXIS>(metricData, {
    freezeVersion,
    hackTrendAnalysis: true,
  });

  useEffect(() => {
    if (visible) {
      !firstDataSource && setFirstDataSource(dataSource);
    }
  }, [dataSource]);

  const [timeRange, setTimeRange] = useState<string[]>([]); // 用户选的时间范围
  const [baselineId, setBaselineId] = useState<string>(''); // 基线id 趋势分析使用

  const [queryResult, setQueryResult] = useState<
    | {
        dataSource: QueryData | undefined;
        msg: string | undefined;
        queryInfo: QueryInfo;
        params: QueryRequest;
      }
    | undefined
  >();

  const [analysisLoading, setAnalysisLoading] = useState<boolean>(false);

  useEffect(() => {
    if (!baselineId) {
      setQueryResult(data);
      setAnalysisLoading(loading);
    }
  }, [baselineId, data, loading]);

  const refetchReady =
    !findKey(state, item => item) && !_metricLoading && !analysisLoading;
  useEffect(() => {
    let qResult = cloneDeep(data);
    if (qResult?.params?.query_info?.where_list && queryInfo) {
      qResult.params.query_info.where_list = queryInfo.where_list;
    }
    setQueryResult(qResult);
  }, [queryInfo?.where_list, nodeId, data]);

  const [selectedArray, setSelectedArray] = useState<string[]>([]);

  useEffect(() => {
    const range = getDefaultTimeRange(
      timeFilterValue.range,
      timeFilterValue.granularity,
    );
    setTimeRange(range);
  }, [...timeFilterValue.range]);

  const handleFetch = useCallback(
    (cacheable = true) => {
      if (!timeRange.length || !metricData) {
        return;
      }

      run(
        {
          chartType: CHART_TYPE.DOUBLE_AXIS,
          timeFilterInfo: {
            ...timeFilterValue,
            range: timeRange,
          },
          domainMeasureObjFilter,
          anchorStartTime: timeFilterValue.range[0],
          nodeId,
          customParams: {
            baselines: baselineId ? [baselineId] : [],
            gen_rule_text: true,
            ...(inReportDetail ? params : {}),
          },
          spaceId,
        },
        cacheable,
      );
      !cacheable && reasonRefreshTrigger();
    },
    // 依赖不需要加入nodeId, metricData变化是发生在节点改变之后，一定能获取到最新的名称
    [
      nodeId,
      spaceId,
      metricData?.id,
      timeRange,
      baselineId,
      run,
      domainMeasureObjFilter.measureObjId,
      domainMeasureObjFilter.filter?.sort()?.join(''),
      freezeVersion,
    ],
  );
  useEffect(() => {
    handleFetch();
  }, [handleFetch]);

  useEffect(() => {
    setBaselineId('');
  }, [
    metricData?.id,
    timeRange,
    run,
    domainMeasureObjFilter.measureObjId,
    domainMeasureObjFilter.filter?.sort()?.join(''),
  ]);

  // 触发无缓存请求
  useCacheEvent({
    eventName: DEFAULT_CACHE_EVENT,
    callback: () => handleFetch(false),
  });

  const { trigger: reasonRefreshTrigger } = useCacheEvent({
    eventName: REASON_CACHE_EVENT,
  });

  // 节点切换
  const handleNodeChange = (id: string) => {
    if (!NodeManager.getModule()?.validAuth(id, Authority.read)) {
      Message.error({
        content: (
          <>没有 {NodeManager.getModule()?.getNodeInfo(id)?.node_name} 权限</>
        ),
      });
      return;
    }
    // 切换节点把基线置空
    setBaselineId('');
    setUrlQuery({ _metricNodeId: id });
  };
  // 指标切换
  const handleMetricChange = (metric: MetricInfo) => {
    setOpenedMetricStack(prevState => {
      const index = prevState.findIndex(({ id }) => id === metric.id);
      if (~index) {
        return prevState.slice(0, index + 1);
      }
      return prevState.concat(metric);
    });
    setUrlQuery({
      _metricId: metric.id,
      _metricName: metric.name,
      _metricType: metric.type,
    });
    setCardChartList([]);
    _changeMetricData(undefined);
  };
  // 时间筛选器切换
  const handleTimeFilterChange = ({
    granularity,
    cycle,
    range,
  }: TimeFilterValue) => {
    setUrlQuery({
      _metricTimeGranularity: granularity,
      _metricTimeCycle: cycle,
      _metricStartTime: range[0],
      _metricEndTime: range[1],
    });
  };

  const handleDomainMeasureObjChange = (v: DomainMeasureObjFilterValue) => {
    setUrlQuery({
      _metricDomainMeasureObjId: v.measureObjId,
      _metricDomainMeasureObjFilter: v.filter,
    });
  };

  // 查看指标详情分析
  const handleMetricAnalysis = (options: {
    timeFilterInfo?: TimeFilterValue;
    metricInfo?: MetricInfo; // 指标信息 指标卡片+ 评分指标下的元指标需要
    nodeId?: string;
  }) => {
    const { timeFilterInfo, metricInfo, nodeId } = options;
    let queryParams = {};
    if (metricInfo) {
      setCardChartList([]);
      setOpenedMetricStack(v => [...v, metricInfo]);
      queryParams = {
        ...queryParams,
        _metricId: metricInfo.id,
        _metricName: metricInfo.name,
        _metricType: metricInfo.type,
      };
    }
    if (timeFilterInfo) {
      setUrlQuery({});
      queryParams = {
        ...queryParams,
        _metricTimeGranularity: timeFilterInfo.granularity,
        _metricTimeCycle: timeFilterInfo.cycle,
        _metricStartTime: timeFilterInfo.range[0],
        _metricEndTime: timeFilterInfo.range[1],
      };
    }
    if (nodeId) {
      setUrlQuery({ _metricNodeId: nodeId });
      queryParams = {
        ...queryParams,
        _metricNodeId: nodeId,
      };
    }
    setUrlQuery(queryParams);
  };

  // 短链分享
  const handleShare = async () => {
    const res: any = await getShortLink(location.href).catch(_ => null);
    if (res?.code === 0 && res?.data?.[0]?.short_url) {
      copy(res?.data?.[0]?.short_url || '');
      Message.success('成功复制到剪切板');
    } else {
      Message.error('复制失败');
    }
  };

  // 用户无当前节点权限 跳转 kani 申请
  // useEffect(() => {
  //   if (visible && nodeId) {
  //     if (!NodeManager.getModule()?.validAuth(nodeId, Authority.read)) {
  //       window.location.href = getNodeAuthApplyUrl(nodeId);
  //     }
  //   }
  // }, [nodeId, visible]);

  const handleCancel = e => {
    rest.onCancel?.(e);
    setUrlQuery({
      _metricId: undefined,
      _metricType: undefined,
      _metricName: undefined,
      _metricNodeId: undefined,
      _metricTimeGranularity: undefined,
      _metricTimeCycle: undefined,
      _metricStartTime: undefined,
      _metricEndTime: undefined,
      _metricDomainMeasureObjId: undefined,
      _metricDomainMeasureObjFilter: undefined,
    });
    if (isMatchMetricList) {
      cancelAllRequest();
    }
  };

  const handleAfterClose = () => {
    setShowSheetCardFalse();
    setOpenedMetricStack([]);
    setBaselineId('');
    setCardChartList([]);
    _changeMetricData(undefined);
    rest.afterClose?.();
  };

  // 无缓存请求触发器
  const { trigger: refreshTrigger } = useCacheEvent();

  const queryInfoRef = useRef<{
    queryParams: QueryRequest | undefined;
    queryParamsList: DownloadRequest[] | undefined;
    loading: boolean | undefined;
    dimensionName: string | undefined;
  }>({
    queryParams: undefined,
    queryParamsList: undefined,
    loading: undefined,
    dimensionName: undefined,
  });

  const handleDownload = async () => {
    Message.info('正在导出中，预计需要1~2分钟');
    const dlres = await queryBaseFeatureServiceClient.GetDownloadInfo({
      ...(queryInfoRef.current.queryParams as any),
      version: 1,
    });
    if (dlres?.data?.url) {
      window.open(dlres?.data?.url);
    }
    if (dlres.code !== 200) {
      Message.error(dlres.msg);
    }
  };

  const owners = metricData
    ? isMetaMetricData(metricData)
      ? metricData.owners
      : [metricData?.common_data?.owner ?? '']
    : [];

  const getSpaceJumpWarp: GetSpaceJumpWarp = (spaceValue, _) => {
    // const jumpSpaceType =
    //   metricData?.analysis_info?.analysis_dim_ids?.find(
    //     ({ dim_id }) => String(dim_id) === String(dimensionId),
    //   )?.space_type ?? '';

    // const jumpNodeId = NodeManager.getModule()?.getNodeId(
    //   jumpSpaceType,
    //   spaceValue,
    // )?.node_id;

    // return dom => {
    //   if (jumpNodeId) {
    //     return (
    //       <a
    //         className="text-blue-600"
    //         onClick={() =>
    //           setUrlQuery({
    //             _metricNodeId: jumpNodeId,
    //           })
    //         }>
    //         {dom || spaceValue}
    //       </a>
    //     );
    //   } else {
    //     return <span>{dom || spaceValue}</span>;
    //   }
    // };
    return dom => <span>{dom || spaceValue}</span>;
  };

  const noFreeze = !data?.msg?.includes('seal');

  return (
    <>
      <SCDrawer
        className={cs('rounded-t-xl', className)}
        height="96%"
        focusLock={false}
        unmountOnExit
        style={{ zIndex: 1000 }}
        placement="bottom"
        maskStyle={{ zIndex: 900 }}
        {...(isMiniScreen
          ? {
              getPopupContainer: () =>
                document.getElementById('root') as HTMLElement,
            }
          : {})}
        footer={null}
        title={
          <div className="flex flex-1 justify-between text-lg">
            <div className="flex gap-3 items-center">
              {openedMetricStack.length > 1 && (
                <Popover
                  position="bl"
                  triggerProps={{ showArrow: false }}
                  content={initial(openedMetricStack).map((item, index) => (
                    <div
                      key={item.id}
                      className={cs('cursor-pointer hover:text-brand5', {
                        'mt-3': index > 0,
                      })}
                      onClick={() => handleMetricChange(item)}>
                      {item.name}
                    </div>
                  ))}>
                  <IconList className="text-lg text-text3" />
                </Popover>
              )}
              <MetricName
                name={
                  metricName ||
                  (metricData
                    ? isMetaMetricData(metricData)
                      ? metricData.display_name
                      : metricData.name
                    : '')
                }
                owners={owners}
                description={metricData?.description ?? ''}
                metricInfo={metricData?.metric_info}
                metricAttribute={metricData?.metric_attribute}
              />
              <FreezeList
                isDrawer={true}
                drawerVisible={visible}
                noFreeze={noFreeze}
                onChange={version => {
                  setFreezeVersion(version);
                  setFreezed(Boolean(version));
                }}
              />

              {!isSubModule() && <Divider type="vertical" />}
              {!isSubModule() && (
                <div
                  className="flex items-center text-sm font-normal cursor-pointer"
                  onClick={() => {
                    const allowEdit =
                      authData?.isDomainExpert || authData?.isAdmin;
                    openURL(
                      metricManageUrlBy(
                        metricType,
                        metricId,
                        allowEdit,
                        nodeId,
                      ),
                    );
                  }}>
                  <IconSettings className="mr-1" />
                  配置
                </div>
              )}
            </div>
            <div>
              <Button
                type="outline"
                icon={<IconSync />}
                className="mr-4"
                disabled={!refetchReady}
                loading={!refetchReady}
                onClick={(...args) => {
                  XLog({
                    id: 'webclick',
                    page_id: LogSceneMap.metric.value,
                    page_title: LogSceneMap.metric.type,
                    btn_title: {
                      metric: metricName || initialMetric.name,
                      event: LogEventMap.metric.clickRefresh,
                    },
                  });
                  refreshTrigger(args);
                }}>
                刷新
              </Button>
              {/* <Button
                disabled={true}
                type="outline"
                icon={<IconSubscribeAdd />}
                className="mr-4">
                订阅
              </Button> */}
              {/* IFTRUE_prodEnv */}
              <Button type="primary" icon={<IconLink />} onClick={handleShare}>
                分享
              </Button>
              {/* FITRUE_prodEnv */}
              <Divider type="vertical" className="!mx-6 !h-3 !border-text2" />
            </div>
          </div>
        }
        visible={visible}
        onCancel={handleCancel}
        afterClose={handleAfterClose}>
        {visible ? (
          _metricLoading ? (
            <SCSpinWrap>
              <Spin size={30} tip="加载中" />
            </SCSpinWrap>
          ) : (
            <div className="flex justify-between h-full">
              <MacScrollbar className="overflow-y-auto relative flex-1 mr-6 h-full metricDetailDrawer">
                <SCBlock className="flex justify-between items-center px-6 h-20">
                  {/* <SpaceSelector nodeId={nodeId} onChange={handleNodeChange} /> */}
                  <VirtualArea nodeId={nodeId} onChange={handleNodeChange} />
                  <Space>
                    <DomainMeasureObjFilter
                      value={domainMeasureObjFilter}
                      nodeId={nodeId}
                      disabled={freezed || spaceType === SpaceType.Employee}
                      timeRange={timeFilterValue.range}
                      domainMeasureObjList={
                        metricData?.domain_measure_objs?.reduce<
                          { id: string; name: string }[]
                        >((prev, { id, display_name, dimension_id }) => {
                          if (dimension_id) {
                            prev.push({
                              id,
                              name: display_name,
                            });
                          }
                          return prev;
                        }, []) ?? []
                      }
                      metricInfoList={
                        metricData
                          ? [
                              {
                                metricId: metricData.id,
                                metricType: isMetaMetricData(metricData)
                                  ? MetricTypeMeta
                                  : MetricTypeComplexMeta,
                                modelId: isMetaMetricData(metricData)
                                  ? metricData.model_id
                                  : metricData.id,
                                domainMeasureObjs:
                                  metricData.domain_measure_objs ?? [],
                              },
                            ]
                          : []
                      }
                      getPopupContainer={() =>
                        document.getElementsByClassName(
                          'metricDetailDrawer',
                        )[0] ?? document.body
                      }
                      onChange={handleDomainMeasureObjChange}
                    />
                    <TimeFilter
                      needLog={true}
                      logName={metricName}
                      disabledFuture={true}
                      value={timeFilterValue}
                      disabled={freezed}
                      tooltipContent={
                        freezed ? '当前报告已封板，无法执行此操作' : ''
                      }
                      onChange={handleTimeFilterChange}
                    />
                  </Space>
                </SCBlock>
                <SCBlock className="mt-4">
                  <SCBlockHeader>
                    <div className="flex items-center">
                      <iconpark-icon name="metric-analysis" class="mr-2" />
                      指标分析
                    </div>
                  </SCBlockHeader>
                  <div className="overflow-x-auto overflow-y-hidden">
                    <div className="px-6 my-[24px] flex gap-4 justify-start items-baseline min-h-[104px]">
                      {cardChartList.map((metricData, index) => {
                        return (
                          <>
                            <CardChart
                              showFormula={index === 0}
                              index={index}
                              key={index}
                              nodeId={nodeId}
                              timeFilterInfo={timeFilterValue}
                              cacheDataSource={firstDataSource}
                              hideMenus={freezed ? ['指标详情分析'] : []}
                              domainMeasureObjFilter={domainMeasureObjFilter}
                              metricData={metricData}
                              showMetricAnalysis={index > 0} // 存在多个指标说明是评分指标下的元指标
                              onAnalysis={handleMetricAnalysis}
                              freezeVersion={freezeVersion}
                            />
                          </>
                        );
                      })}
                      <div className="pr-1 w-[10px] h-[10px]"></div>
                    </div>
                  </div>
                </SCBlock>
                <SCBlock className="mt-4">
                  <SCBlockHeader className="justify-between">
                    <div className="flex items-center">
                      <iconpark-icon name="line-chart" class="mr-2" />
                      趋势分析
                    </div>
                    <BaselineSelect
                      metricData={metricData}
                      nodeId={nodeId}
                      timeFilterInfo={timeFilterValue}
                      domainMeasureObjFilter={domainMeasureObjFilter}
                      onChange={setBaselineId}
                      freezeVersion={freezeVersion}
                    />
                  </SCBlockHeader>
                  <div className="px-6 pt-6">
                    <XYChart
                      nodeId={nodeId}
                      timeFilterInfo={timeFilterValue}
                      metricData={metricData}
                      hideMenus={freezed ? ['指标详情分析'] : []}
                      dataSource={dataSource}
                      displayConfig={displayConfig}
                      queryParams={queryParams}
                      queryInfo={queryInfo}
                      loading={loading}
                      onAnalysis={handleMetricAnalysis}
                      timeRange={timeRange}
                      onChangeTime={setTimeRange}
                      freezeVersion={freezeVersion}
                    />
                  </div>
                </SCBlock>
                {showSheetCard && (
                  <SCBlock className="mt-4">
                    <SCBlockHeader>
                      <div className="flex items-center">
                        <iconpark-icon name="metric-table-chart" class="mr-2" />
                        对比分析
                      </div>
                      {/* IFTRUE_prodEnv */}
                      {hasOutputAuth && (
                        <Dropdown
                          droplist={
                            <Menu>
                              <Menu.Item key="3" onClick={handleDownload}>
                                数据导出
                              </Menu.Item>
                            </Menu>
                          }>
                          <Button
                            icon={<IconMore />}
                            type="text"
                            style={{ color: '#222' }}
                            iconOnly
                          />
                        </Dropdown>
                      )}
                      {/* FITRUE_prodEnv */}
                      <Space>
                        <SelectStatisticsButton
                          isStatistics={
                            _metricData?.metric_attribute?.metric_use ===
                            MetricUseStatistics
                          }
                          selectedArray={selected => setSelectedArray(selected)}
                        />
                        <DownLoadButton
                          queryInfoRef={queryInfoRef}
                          metricName={metricName || initialMetric.name}
                        />
                      </Space>
                    </SCBlockHeader>
                    <div className="px-3 pt-6">
                      <SheetChart
                        getSpaceJumpWarp={getSpaceJumpWarp}
                        ref={queryInfoRef}
                        nodeId={nodeId}
                        selectedArray={selectedArray}
                        timeFilterInfo={timeFilterValue}
                        domainMeasureObjFilter={domainMeasureObjFilter}
                        hideMenus={freezed ? ['指标详情分析'] : []}
                        metricData={metricData}
                        onAnalysis={handleMetricAnalysis}
                        freezeVersion={freezeVersion}
                      />
                    </div>
                  </SCBlock>
                )}
                {!showSheetCard && (
                  <Divider orientation="center">
                    <div style={{ fontSize: '12px', fontWeight: 400 }}>
                      更多分析，
                      <Link onClick={setShowSheetCardTrue}>点击加载</Link>
                    </div>
                  </Divider>
                )}
              </MacScrollbar>
              <SCBlock className="w-[380px]">
                <div className="h-[72px] p-6 border-b border-solid border-line2">
                  <Radio.Group defaultValue={AnalysisRadioValue.Conclusion}>
                    {analysisRadioOptions.map(item => (
                      <Radio
                        key={item}
                        value={item}
                        className="!p-0 !mr-5"
                        disabled={item === AnalysisRadioValue.About}>
                        {({ checked }) => (
                          <div
                            className={cs(
                              {
                                'font-medium': checked,
                                'text-text2': !checked,
                              },
                              'p-0 text-base',
                            )}>
                            {item}
                          </div>
                        )}
                      </Radio>
                    ))}
                  </Radio.Group>
                </div>
                <Analysis
                  metricData={metricData}
                  queryResult={queryResult}
                  dataSource={dataSource}
                  loading={analysisLoading}
                  queryParams={queryParams!}
                  nodeId={nodeId}
                  spaceId={spaceId}
                  freezeVersion={freezeVersion}
                  timeFilterInfo={timeFilterValue}
                  domainMeasureObjFilter={domainMeasureObjFilter}
                  analysisEditable={mode === EDITOR_MODE.Write}
                  getSpaceJumpWarp={getSpaceJumpWarp}
                />
              </SCBlock>
            </div>
          )
        ) : null}
      </SCDrawer>
    </>
  );
};

const DownLoadButton = ({ queryInfoRef, metricName }) => {
  const [isDownload, setIsDownload] = useState(false);

  const handleExportData = async () => {
    if (!queryInfoRef.current.queryParamsList) {
      return;
    }
    Message.success({
      id: '对比分析',
      content: '开始下载,请稍后',
      duration: 2000,
    });
    XLog({
      id: 'webclick',
      page_id: LogSceneMap.metric.value,
      page_title: LogSceneMap.metric.type,
      btn_title: {
        metric: metricName,
        event: LogEventMap.metric.downloadAnalysis,
      },
    });
    setIsDownload(true);
    try {
      const res = await queryBaseFeatureServiceClient.GetDownloadCsv(
        {
          request_list: queryInfoRef.current.queryParamsList.filter(
            item => item.sheet_name === queryInfoRef.current.dimensionName,
          ),
          version: 1,
          position: '对比分析',
        },
        { [QUERYCACHE]: QUERYCACHE_ENABLE },
      );
      setIsDownload(false);
      if (res.code !== 200) {
        Message.error({ id: '对比分析', content: res.msg });
      }
      const content = res?.data?.content;
      if (content) {
        download(s2ab(atob(content)), `${res?.data.file_name}`, {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;',
        });
        Message.success({ id: '对比分析', content: '下载完成' });
      } else {
        Message.error({ id: '对比分析', content: '暂无数据下载' });
      }
    } catch (error) {
      setIsDownload(false);
      Message.error({ id: '对比分析', content: '下载请求超时' });
    }
  };
  return (
    <Popover content="最大支持50000条">
      <Button
        onClick={handleExportData}
        icon={<IconDownload />}
        // type="text"
        style={{ color: '#222', position: 'relative', top: 70 }}
        disabled={queryInfoRef.current.loading || isDownload}
        loading={isDownload}
        iconOnly
      />
    </Popover>
  );
};

export default MetricDrawer;
