import { createModel, updateState } from '@jupiter/plugin-runtime/model';

import { getDefaultTimeValue } from '../TimeFilter';

import { MetricDetailDrawerConfig } from './interface';

interface MetricDetailDrawerState {
  visible: boolean;
  config: MetricDetailDrawerConfig;
}

const initialState: MetricDetailDrawerState = {
  visible: false,
  config: {
    initialNodeId: '',
    initialTime: getDefaultTimeValue(),
    initialMetric: { id: '', name: '', type: '' },
  },
};

const metricDetailDrawerModel = createModel<MetricDetailDrawerState>(
  'metricDetailDrawer',
).define({
  state: initialState,
  actions: {
    setVisible: updateState.replace<boolean>('visible'),
    setState: updateState.replace(true),
  },
});

export default metricDetailDrawerModel;
