import { ReactNode } from 'react';

import type { DrawerProps } from '@arco-design/web-react';

import type { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import type { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';

import type { TimeFilterValue } from '@/components/TimeFilter';

import type { DomainMeasureObjFilterValue } from '../DomainMeasureObjFilter';

export enum AnalysisRadioValue {
  Conclusion = '分析结论',
  About = '相关指标故事',
}

export type MetricData = MetricMetaData | ComplexMetricMetaData;

export interface MetricInfo {
  id: string; // 指标id
  name: string; // 指标名称
  type: string; // 指标类型
}

export interface MetricDetailDrawerConfig {
  initialMetric: MetricInfo;
  initialNodeId: string;
  initialTime?: TimeFilterValue;
  initialDomainMeasureObjFilter?: DomainMeasureObjFilterValue;
  freezeVersion?: string; // 封板信息 报告详情页打开抽屉使用
  className?: string | string[];
}

export type MetricDetailDrawerProps = MetricDetailDrawerConfig & DrawerProps;

export type GetSpaceJumpWarp = (
  value: string,
  space: string,
) => (dom: ReactNode) => ReactNode;
