import { useEffect, useState } from 'react';

import {
  Button,
  Tag,
  Modal,
  Dropdown,
  Menu,
  Divider,
  Spin,
  Descriptions,
  Message,
  Tooltip,
  Empty,
} from '@arco-design/web-react';
import { IconDown } from '@arco-design/web-react/icon';
import { useRequest } from '@byted/hooks';
import { useModel } from '@jupiter/plugin-runtime/model';
import dayjs from 'dayjs';
import { MacScrollbar } from 'mac-scrollbar';
import styled from 'styled-components';

import { improvementTaskServiceClient } from '@quality/common-apis/src/apis/api/insight/improvement_task';

import UserAvatar from '@/components/UserAvatar';
import { API_V1, Authority, EDITOR_MODE } from '@/constants';
import useMetricDrawerUrlQuery from '@/hooks/url/useMetricDrawerUrlQuery';
import useReportConfigUrlQuery from '@/hooks/url/useReportConfigUrlQuery';
import { useNodeIdAuth } from '@/hooks/useNodeIdAuth';
import globalModel from '@/model/global';

import { taskLevelMap } from './interface';

const TaskListWrap = styled.div`
  border: 1px solid #e5e8ef;
  border-bottom: none;
  .avatar-wrapper {
    margin: 0 4px 0 0;
  }
  .arco-btn-group {
    button {
      box-shadow: none !important;
      background: none;
      border: none !important;
    }
    .arco-btn:first-child {
      padding: 0;
      pointer-events: none;
      :hover {
        background: none;
      }
    }
    .arco-btn-size-small.arco-btn-icon-only {
      width: 20px;
      height: 24px;
      border-radius: 4px;
      margin-left: 1px;
      padding-bottom: 4px;
    }
  }
  .taskName {
    height: 16px;
    // max-width: 200px;
    white-space: nowrap;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;
const FilterWrap = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  button {
    padding: 0 8px;
  }
`;
export const ModalWrap = styled.div`
  .arco-modal-simple {
    top: 300px !important;
    width: 340px;
  }
  .arco-modal-simple .arco-modal-header {
    margin-bottom: 12px;
  }
  .arco-btn-size-default {
    height: 28px;
  }
  .arco-modal-content {
    padding: 0 28px;
  }
`;

export const MiniTextButton = styled(Button)`
  padding: 0 4px;
`;

export const TaskList = ({ data, onModifyClick, freshToken }) => {
  const [detailData, setDetailData] = useState<any>();
  // const [hasReadAuth, hasWriteAuth] = useSpaceAuthoriy();
  const {
    urlQuery: { mode, nodeId },
  } = useReportConfigUrlQuery();

  const {
    urlQuery: { _metricNodeId },
  } = useMetricDrawerUrlQuery();
  // 兼容抽屉node_id传参
  const node_id = _metricNodeId || nodeId;
  const { isAuth: hasWriteAuth } = useNodeIdAuth(node_id);
  const { isAuth: hasReadAuth } = useNodeIdAuth(node_id, Authority.read);
  const [{ userInfo }] = useModel(globalModel);

  const [showFinishedTasks, setShowFinishedTasks] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const {
    run: getTaskList,
    loading: getTaskListLoading,
    data: getTaskListRep,
  } = useRequest(async () => {
    const res = await improvementTaskServiceClient.GetImprovementTasks({
      ...data,
      version: API_V1,
    });

    return res.data;
  });

  const {
    run: finishTask,
    loading: finishTaskLoading,
    // data: finishTaskRep,
  } = useRequest(async taskData => {
    const { report_name, report_url, metric_url } = data;
    const reqData = {
      id: taskData.id,
      is_finished: !taskData.is_finished,
    } as any;
    if (report_name) {
      reqData.report_url = report_url;
      reqData.report_name = report_name;
    } else {
      reqData.metric_url = metric_url;
    }
    const res = await improvementTaskServiceClient.FinishImprovementTask({
      data: reqData,
      version: API_V1,
    });

    if (res) {
      window.postMessage({ event: 'taskChange', data: { ...data } }, '*');
    }
    return res.data;
  });

  const handleRefreshEvent = event => {
    const { data } = event;
    if (data?.event === 'taskChange') {
      getTaskList();
    }
  };

  useEffect(() => {
    window.addEventListener('message', handleRefreshEvent, false);
    return () => {
      window.removeEventListener('message', handleRefreshEvent, false);
    };
  }, []);

  useEffect(() => {
    getTaskList();
  }, [freshToken]);

  const taskListData = showFinishedTasks
    ? getTaskListRep?.finished_tasks
    : getTaskListRep?.unfinished_tasks;

  return (
    <div>
      <FilterWrap className="flex">
        <Button
          key="0"
          type="text"
          onClick={() => {
            setShowFinishedTasks(false);
          }}
          style={{
            color: `${showFinishedTasks ? 'grey' : '#165DFF'}`,
            fontWeight: `${showFinishedTasks ? '400' : '500'}`,
          }}>
          未完成
        </Button>
        <Divider style={{ margin: '0 4px' }} type="vertical" />
        <Button
          key="1"
          onClick={() => {
            setShowFinishedTasks(true);
          }}
          type="text"
          style={{
            color: `${!showFinishedTasks ? 'grey' : '#165DFF'}`,
            fontWeight: `${!showFinishedTasks ? '400' : '500'}`,
          }}>
          已完成
        </Button>
        {getTaskListLoading ||
          (finishTaskLoading && (
            <Spin style={{ marginLeft: '4px' }} loading={true} size={18} />
          ))}
      </FilterWrap>
      {taskListData && taskListData.length > 0 && (
        <TaskListWrap>
          {taskListData?.map(data => {
            return (
              <div
                style={{
                  padding: '8px 12px',
                  fontSize: '12px',
                  fontWeight: 500,
                  color: '#1D2129',
                  borderBottom: '1px solid #E5E8EF',
                }}
                key={data.id}>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}>
                  <div
                    style={{
                      height: 'fit-content',
                      display: 'flex',
                      alignItems: 'center',
                    }}>
                    <Tag
                      color={taskLevelMap[data.task_level].color}
                      size="small"
                      style={{
                        marginRight: '4px',
                        padding: '1px 2px',
                        height: '16px',
                        marginBottom: '1px',
                      }}>
                      {taskLevelMap[data.task_level].label}
                    </Tag>
                    <Tooltip content={data.task_name}>
                      <div className="taskName">{data.task_name}</div>
                    </Tooltip>
                  </div>
                  {data.task_owners.length > 0 && (
                    <UserAvatar
                      size={20}
                      length={3}
                      dataList={data.task_owners.map(o => o.name)}
                    />
                  )}
                </div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: '6px',
                  }}>
                  <Dropdown.Button
                    size="small"
                    icon={<IconDown />}
                    droplist={
                      <Menu
                        className="w-[102px]"
                        selectedKeys={[data.is_finished ? '1' : '0']}
                        onClickMenuItem={key => {
                          const owner = data.task_owners.find(
                            t => t.username === userInfo?.username,
                          );
                          if (!hasWriteAuth && !owner) {
                            Message.warning('无操作权限');
                            return;
                          }

                          if (Boolean(Number(key)) !== data.is_finished) {
                            finishTask(data);
                          }
                        }}>
                        <Menu.Item key="0">未完成</Menu.Item>
                        <Menu.Item key="1">已完成</Menu.Item>
                      </Menu>
                    }>
                    <Tag color={data.is_finished ? 'green' : 'orange'}>
                      {data.is_finished ? '已完成' : '未完成'}
                    </Tag>
                  </Dropdown.Button>
                  <div>
                    {
                      <MiniTextButton
                        size="mini"
                        type="text"
                        onClick={() => {
                          if (data.is_finished || mode === EDITOR_MODE.Read) {
                            if (!hasReadAuth) {
                              Message.warning('无操作权限');
                              return;
                            }

                            setDetailData(data);
                            setDetailVisible(true);
                          } else {
                            onModifyClick(data);
                          }
                        }}>
                        {data.is_finished || mode === EDITOR_MODE.Read
                          ? '查看'
                          : '修改'}
                      </MiniTextButton>
                    }
                    {mode !== EDITOR_MODE.Read && (
                      <MiniTextButton
                        size="mini"
                        type="text"
                        onClick={() => {
                          if (!hasWriteAuth) {
                            Message.warning('无操作权限');
                            return;
                          }
                          Modal.confirm({
                            modalRender: n => {
                              return <ModalWrap>{n}</ModalWrap>;
                            },
                            title: '确定要删除改进任务吗？',
                            content:
                              '删除所选改进任务后，该任务将不可恢复，请谨慎操作。',
                            okText: '删除',
                            onOk: async () => {
                              const res =
                                await improvementTaskServiceClient.DeleteImprovementTask(
                                  {
                                    version: API_V1,
                                    id: data.id,
                                  },
                                );

                              if (res.code === 200) {
                                window.postMessage(
                                  { event: 'taskChange' },
                                  '*',
                                );
                              }
                            },
                            okButtonProps: {
                              status: 'danger',
                            },
                          });
                        }}>
                        删除
                      </MiniTextButton>
                    )}
                  </div>
                </div>
                <Modal
                  title="查看详情"
                  visible={detailVisible}
                  hideCancel={true}
                  key={data.id}
                  onCancel={() => {
                    setDetailVisible(false);
                  }}
                  mask={true}
                  maskStyle={{
                    opacity: 0.2,
                  }}
                  unmountOnExit={true}
                  className="w-[600px]"
                  onOk={() => {
                    setDetailVisible(false);
                  }}>
                  {detailData && (
                    <MacScrollbar suppressScrollY={true} suppressScrollX={true}>
                      <Descriptions
                        column={1}
                        className="no-scrollbar"
                        style={{
                          textOverflow: 'ellipsis',
                          wordWrap: 'break-word',
                          overflowY: 'scroll',
                          overflowX: 'hidden',
                        }}
                        valueStyle={{
                          maxWidth: '420px',
                        }}
                        data={[
                          { label: '任务名称', value: detailData?.task_name },
                          {
                            label: '任务描述',
                            value: <div>{detailData?.task_desc}</div>,
                          },
                          {
                            label: '任务责任人',
                            value: (
                              <UserAvatar
                                size={20}
                                length={10}
                                wrapperStyle={{ transform: 'translateY(6px)' }}
                                dataList={detailData?.task_owners.map(
                                  o => o.name,
                                )}
                              />
                            ),
                          },
                          {
                            label: '优先级',
                            value: (
                              <Tag
                                color={
                                  taskLevelMap[detailData.task_level].color
                                }
                                size="small"
                                style={{
                                  marginRight: '4px',
                                  padding: '1px 2px',
                                  height: '16px',
                                  marginBottom: '1px',
                                }}>
                                {taskLevelMap[detailData.task_level].label}
                              </Tag>
                            ),
                          },
                          {
                            label: '计划时间',
                            value: `${dayjs(detailData?.exp_start_time).format(
                              'YYYY/MM/DD',
                            )} - ${dayjs(detailData?.exp_end_time).format(
                              'YYYY/MM/DD',
                            )}`,
                          },
                        ]}
                        labelStyle={{ paddingRight: 40 }}
                      />
                    </MacScrollbar>
                  )}
                </Modal>
              </div>
            );
          })}
        </TaskListWrap>
      )}
      {(!taskListData || taskListData.length === 0) &&
        mode === EDITOR_MODE.Read && <Empty description="暂无改进任务" />}
    </div>
  );
};
