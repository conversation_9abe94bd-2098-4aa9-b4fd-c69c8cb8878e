/* eslint-disable max-len */
import { useEffect, useState } from 'react';

import {
  DatePicker,
  Form,
  Input,
  Message,
  Modal,
  Select,
} from '@arco-design/web-react';
import { useRequest } from '@byted/hooks';

import { UserData } from '@quality/common-apis/src/apis/api/base/base_data';
import { improvementTaskServiceClient } from '@quality/common-apis/src/apis/api/insight/improvement_task';
import { TaskLevel } from '@quality/common-apis/src/apis/api/insight/model';
import IconTextButton from '@quality/common-components/src/ArcoExtends/IconTextButton';

import UserSelect from '@/components/UserSelect';
import { API_V1, EDITOR_MODE } from '@/constants';
import useMetricDrawerUrlQuery from '@/hooks/url/useMetricDrawerUrlQuery';
import useReportConfigUrlQuery from '@/hooks/url/useReportConfigUrlQuery';
import { useNodeIdAuth } from '@/hooks/useNodeIdAuth';

import { taskLevelMap } from './interface';
import { TaskList } from './TaskList';

const FormItem = Form.Item;

export const ImproveTask = ({ data }) => {
  const {
    metric_id,
    business_id,
    template_id,

    report_url,
    report_name,
    metric_url,
    metricData,
    timeFilterInfo: { granularity, range },
  } = data;

  let extraReqData = {
    metric_id,
    business_id,
    granularity_type: granularity,
    start_time: range[0],
    end_time: range[1],
    template_id,
    metric_type: metricData?.caliber_type !== 'meta' ? 'complexMeta' : 'meta',
  } as any;
  if (report_name) {
    extraReqData = {
      report_url,
      report_name,
      ...extraReqData,
    };
  } else {
    extraReqData = {
      metric_url,
      ...extraReqData,
    };
  }
  const maxLength = 30;
  // const [, hasWriteAuth] = useSpaceAuthoriy();

  const {
    urlQuery: { mode, nodeId },
  } = useReportConfigUrlQuery();

  const {
    urlQuery: { _metricNodeId },
  } = useMetricDrawerUrlQuery();
  // 兼容抽屉node_id传参
  const node_id = _metricNodeId || nodeId;
  const { isAuth: hasWriteAuth } = useNodeIdAuth(node_id);

  const [owners, setOwners] = useState<UserData[]>([]);

  const [initialValues, setInitialValues] = useState<any>();
  const [visible, setVisible] = useState(false);
  // const [modifyData, setModifyData] = useState();
  const [freshToken, setFreshToken] = useState(0);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [form] = Form.useForm();

  const {
    run: createTask,
    loading: createTaskLoading,
    data: createTaskRep,
  } = useRequest(async data => {
    const res = await improvementTaskServiceClient.CreateImprovementTask({
      data: { ...data, ...extraReqData },
      version: API_V1,
    });
    if (res.code === 200) {
      window.postMessage({ event: 'taskChange' }, '*');
      setVisible(false);
    }
    return res;
  });
  const {
    run: updateTask,
    loading: updateTaskLoading,
    data: updateTaskRep,
  } = useRequest(async data => {
    const res = await improvementTaskServiceClient.UpdateImprovementTask({
      data: { ...data, ...extraReqData },
      version: API_V1,
    });
    if (res.code === 200) {
      setFreshToken(freshToken + 1);
      setVisible(false);
    }
    return res;
  });

  function onOk() {
    form.validate().then(({ task_level, task_name, task_desc, range_time }) => {
      const task_owners = owners;
      const data = {
        task_level,
        task_name,
        task_desc,
        exp_start_time: range_time[0],
        exp_end_time: range_time[1],
        task_owners,
        is_finished: false,
      };
      setConfirmLoading(true);
      if (initialValues) {
        updateTask({ id: initialValues?.id, ...extraReqData, ...data });
      } else {
        createTask({ ...extraReqData, ...data });
      }
    });
  }

  const formItemLayout = {
    labelCol: {
      span: 6,
    },
    wrapperCol: {
      span: 18,
    },
  };

  useEffect(() => {
    if (createTaskRep?.code === 200 || updateTaskRep?.code === 200) {
      setVisible(false);
      setConfirmLoading(false);
      setFreshToken(freshToken + 1);
    }
  }, [
    createTaskRep?.code,
    updateTaskRep?.code,
    createTaskLoading,
    updateTaskLoading,
  ]);

  if (!metric_id || !business_id) {
    return null;
  }
  return (
    <div
      style={{
        minHeight: '156px',
      }}>
      <TaskList
        data={{
          metric_id,
          business_id,
          report_name,
          report_url,
          metric_url,
        }}
        freshToken={freshToken}
        onModifyClick={data => {
          if (!hasWriteAuth) {
            Message.warning('无操作权限');
            return;
          }
          const mData = {
            owners_username: data.task_owners.map(o => o.name),
            range_time: [data.exp_start_time, data.exp_end_time],
            ...data,
          };
          setOwners(data.task_owners);
          setInitialValues(mData);
          form.setFieldsValue(mData);
          setVisible(true);
        }}
      />
      {(mode === EDITOR_MODE.Write || mode === undefined) && (
        <IconTextButton
          className="mt-[4px]"
          text="添加改进任务"
          onClick={() => {
            if (!hasWriteAuth) {
              Message.warning('无操作权限');
              return;
            }
            setOwners([]);
            form.clearFields();
            setInitialValues(undefined);
            form.setFieldValue('task_level', TaskLevel.P0);
            setVisible(true);
          }}
        />
      )}
      <Modal
        title={initialValues ? '修改任务' : '添加改进任务'}
        visible={visible}
        unmountOnExit={true}
        style={{
          width: '600px',
        }}
        onOk={onOk}
        confirmLoading={confirmLoading}
        onCancel={() => {
          form.clearFields();
          setVisible(false);
        }}>
        <Form
          initialValues={initialValues}
          {...formItemLayout}
          layout="vertical"
          form={form}
          style={{ padding: '16px 40px' }}>
          <FormItem
            label="任务名称"
            placeholder={`不超过${maxLength}个字任务名称`}
            field="task_name"
            rules={[
              { required: true, message: '任务名称必填' },
              { maxLength, message: `字数不超过${maxLength}个` },
            ]}>
            <Input placeholder={`不超过${maxLength}个字任务名称`} />
          </FormItem>
          <FormItem label="任务描述" field="task_desc" required={false}>
            <Input.TextArea maxLength={100} placeholder="详细描述任务详情" />
          </FormItem>
          <FormItem
            label="改进负责人"
            field="owners_username"
            rules={[
              {
                required: true,
                message: '请选择负责人',
              },
              {
                validator: (value, callback) => {
                  if (value.length > 4) {
                    callback('最多设置4位负责人');
                  }
                },
              },
            ]}>
            <UserSelect
              onChange={(_, users) => {
                setOwners(
                  users &&
                    users.map(item => ({
                      avatar: item.avatar || item?.userinfo?.avatar,
                      name: item.identify_name || item?.userinfo?.en_name,
                      username: item.identify_key || item?.userinfo?.email_pre,
                    })),
                );
              }}
              triggerProps={{ style: { zIndex: 10000 } }}
              placeholder="请输入用户名搜索"
            />
          </FormItem>

          <FormItem
            label="优先级"
            required={true}
            field="task_level"
            // defaultValue={TaskLevel.P0}
            rules={[{ required: true }]}>
            <Select defaultValue={TaskLevel.P0} options={taskLevelMap} />
          </FormItem>
          <FormItem
            label="计划时间"
            field={'range_time'}
            rules={[
              {
                required: true,
                message: '请选择计划时间',
              },
            ]}>
            <DatePicker.RangePicker style={{ width: '100%' }} />
          </FormItem>
        </Form>
      </Modal>
    </div>
  );
};
