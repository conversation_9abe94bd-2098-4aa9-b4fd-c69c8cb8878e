import * as React from 'react';

import { Tabs as ArcoTabs } from '@arco-design/web-react';
import styled from 'styled-components';

import { ExpertAnalysisProps } from './ExpertAnalysis/interface';
import { ImprovementTabType } from './helper';
import { ImproveCase } from './ImproveCase';
import { ImproveTask } from './ImproveTask';

const Header: React.FC = () => {
  return (
    <div className="flex flex-col gap-1">
      <span className="text-xs text-text3">🎯 改进措施</span>
    </div>
  );
};

const Tabs = styled(ArcoTabs)`
  .arco-tabs-header-wrapper {
    justify-content: flex-start;
  }
  .arco-tabs-header-title-active {
    background-color: white;
    border-radius: 2px;
  }
`;

export interface ImprovementProps {
  analysisProps: ExpertAnalysisProps;
  freezeVersion: string;
}
export const Improvement: React.FC<ImprovementProps> = ({
  analysisProps: originProps,
  freezeVersion,
}) => {
  const analysisProps = {
    ...originProps,
    freezeVersion,
  };
  return (
    <div className="flex flex-col gap-5">
      <Header />

      <Tabs type="capsule" defaultActiveTab={ImprovementTabType.ImprovementWay}>
        {/* <Tabs.TabPane
          key={ImprovementTabType.ExpertAnalysis}
          title={ImprovementTabType.ExpertAnalysis}>
          <ExpertAnalysis {...analysisProps} />
        </Tabs.TabPane> */}
        <Tabs.TabPane
          key={ImprovementTabType.ImprovementWay}
          title={ImprovementTabType.ImprovementWay}>
          <ImproveCase data={analysisProps} />
        </Tabs.TabPane>
        <Tabs.TabPane
          key={ImprovementTabType.ImprovementTask}
          title={ImprovementTabType.ImprovementTask}>
          <ImproveTask data={analysisProps} />
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
};
