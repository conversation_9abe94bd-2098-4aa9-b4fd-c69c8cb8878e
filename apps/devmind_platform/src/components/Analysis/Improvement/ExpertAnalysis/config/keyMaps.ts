import { UNDO_COMMAND, REDO_COMMAND } from '@editor-kit/core';
import {
  List,
  Bold,
  Table,
  Align,
  Quote,
  Italic,
  Heading,
  Checkbox,
  Underline,
  HyperLinks,
  Strikethrough,
  HorizontalLine,
  COMMANDS,
} from '@editor-kit/plugins';

export const defaultShortcutsKeyMap = [
  {
    key: Heading.KEY,
    value: 'h1',
    shortcut: {
      windows: {
        altKey: true,
        ctrlKey: true,
        keyCode: 49,
      },
      mac: {
        altKey: true,
        metaKey: true,
        keyCode: 49,
      },
    },
  },
  {
    key: Heading.KEY,
    value: 'h2',
    shortcut: {
      windows: {
        altKey: true,
        ctrlKey: true,
        keyCode: 50,
      },
      mac: {
        altKey: true,
        metaKey: true,
        keyCode: 50,
      },
    },
  },
  {
    key: Heading.KEY,
    value: 'h3',
    shortcut: {
      windows: {
        altKey: true,
        ctrlKey: true,
        keyCode: 51,
      },
      mac: {
        altKey: true,
        metaKey: true,
        keyCode: 51,
      },
    },
  },
  {
    key: Bold.KEY,
    shortcut: {
      windows: {
        keyCode: 66,
        ctrlKey: true,
      },
      mac: {
        metaKey: true,
        keyCode: 66,
      },
    },
  },
  {
    key: Italic.KEY,
    shortcut: {
      windows: {
        keyCode: 73,
        ctrlKey: true,
      },
      mac: {
        keyCode: 73,
        metaKey: true,
      },
    },
  },
  {
    key: Underline.KEY,
    shortcut: {
      windows: {
        keyCode: 85,
        ctrlKey: true,
      },
      mac: {
        keyCode: 85,
        metaKey: true,
      },
    },
  },
  {
    key: Strikethrough.KEY,
    shortcut: {
      windows: {
        keyCode: 88,
        ctrlKey: true,
        shiftKey: true,
      },
      mac: {
        keyCode: 88,
        metaKey: true,
        shiftKey: true,
      },
    },
  },
  {
    key: UNDO_COMMAND,
    shortcut: {
      windows: {
        ctrlKey: true,
        keyCode: 90,
      },
      mac: {
        metaKey: true,
        keyCode: 90,
      },
    },
  },
  {
    key: REDO_COMMAND,
    shortcut: [
      {
        // 有两个快捷键
        windows: {
          ctrlKey: true,
          keyCode: 90,
          shiftKey: true,
        },
        mac: {
          metaKey: true,
          keyCode: 90,
          shiftKey: true,
        },
      },
      {
        windows: {
          ctrlKey: true,
          keyCode: 89,
        },
      },
    ],
  },
  {
    key: COMMANDS.INDENT_LEFT,
    shortcut: {
      windows: {
        ctrlKey: false,
        keyCode: 9,
        shiftKey: true,
      },
      mac: {
        metaKey: false,
        keyCode: 9,
        shiftKey: true,
      },
    },
  },
  {
    key: COMMANDS.INDENT_RIGHT,
    shortcut: {
      windows: {
        ctrlKey: false,
        keyCode: 9,
        shiftKey: false,
      },
      mac: {
        metaKey: false,
        keyCode: 9,
        shiftKey: false,
      },
    },
  },
  {
    key: HyperLinks.KEY,
    shortcut: {
      windows: {
        ctrlKey: true,
        keyCode: 75,
      },
      mac: {
        metaKey: true,
        keyCode: 75,
      },
    },
  },
  {
    key: Table.KEY,
    shortcut: {
      windows: {
        altKey: true,
        ctrlKey: true,
        keyCode: 65,
      },
      mac: {
        metaKey: true,
        altKey: true,
        keyCode: 65,
      },
    },
  },
  {
    key: Quote.KEY,
    value: 'true',
    shortcut: {
      windows: {
        shiftKey: true,
        ctrlKey: true,
        keyCode: 190,
      },
      mac: {
        metaKey: true,
        shiftKey: true,
        keyCode: 190,
      },
    },
  },
  {
    key: Align.KEY,
    value: 'left',
    shortcut: {
      windows: {
        shiftKey: true,
        ctrlKey: true,
        keyCode: 76,
      },
      mac: {
        metaKey: true,
        shiftKey: true,
        keyCode: 76,
      },
    },
  },
  {
    key: Align.KEY,
    value: 'center',
    shortcut: {
      windows: {
        shiftKey: true,
        ctrlKey: true,
        keyCode: 69,
      },
      mac: {
        metaKey: true,
        shiftKey: true,
        keyCode: 69,
      },
    },
  },
  {
    key: Align.KEY,
    value: 'right',
    shortcut: {
      windows: {
        shiftKey: true,
        ctrlKey: true,
        keyCode: 82,
      },
      mac: {
        metaKey: true,
        shiftKey: true,
        keyCode: 82,
      },
    },
  },
  {
    key: Checkbox.KEY,
    value: 'check1',
    shortcut: {
      windows: {
        altKey: true,
        ctrlKey: true,
        keyCode: 84,
      },
      mac: {
        metaKey: true,
        altKey: true,
        keyCode: 84,
      },
    },
  },
  {
    key: COMMANDS.INDENT_RIGHT,
    shortcut: {
      windows: {
        ctrlKey: true,
        keyCode: 221,
      },
      mac: {
        metaKey: true,
        keyCode: 221,
      },
    },
  },
  {
    key: HorizontalLine.KEY,
    shortcut: {
      windows: {
        altKey: true,
        ctrlKey: true,
        keyCode: 83,
      },
      mac: {
        metaKey: true,
        altKey: true,
        keyCode: 83,
      },
    },
  },
  {
    key: List.KEY,
    value: 'bullet',
    shortcut: {
      windows: {
        shiftKey: true,
        ctrlKey: true,
        keyCode: 56,
      },
      mac: {
        metaKey: true,
        shiftKey: true,
        keyCode: 56,
      },
    },
  },
  {
    key: List.KEY,
    value: 'number',
    shortcut: {
      windows: {
        shiftKey: true,
        ctrlKey: true,
        keyCode: 55,
      },
      mac: {
        metaKey: true,
        shiftKey: true,
        keyCode: 55,
      },
    },
  },
];
