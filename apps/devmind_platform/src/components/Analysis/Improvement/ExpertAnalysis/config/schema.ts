import { Schema, displayType } from '@editor-kit/core';

export const schema = new Schema({
  bold: {
    mark: true,
  },
  fontcolor: {
    mark: true,
  },
  fontweight: {
    mark: true,
  },
  italic: {
    mark: true,
  },
  fontfamily: {
    mark: true,
  },
  fontsize: {
    mark: true,
  },
  underline: {
    mark: true,
  },
  strikethrough: {
    mark: true,
  },
  'comment-.*': {
    mark: true,
  },
  backcolor: {
    mark: true,
  },
  backgroundcolor: {
    mark: true,
  },
  'at-holder': {
    mark: true,
  },
  'at-uuid_.*': {
    mark: true,
  },
  'clientside-mention-placeholder': {
    mark: true,
  },
  'inline-code': {
    mark: true,
    tailInherit: false,
  },
  'url-.*': {
    mark: true,
    tailInherit: false,
    refresh: true,
  },
  hyperlink: {
    mark: true,
    tailInherit: false,
    refresh: true,
  },
  'horizontal-line': {
    displayEnter: false,
    display: displayType.block,
  },
  textcolor: {
    mark: true,
  },
  aceTable: {
    display: displayType.block,
    displayEnter: false,
  },
  image: {
    display: displayType.block,
    displayEnter: false,
  },
});
