import { Editor } from '@editor-kit/core';
import { DeltaSet, IBaseDelta, Op } from '@editor-kit/delta';

import { GetExpertAnalysisRequest } from '@quality/common-apis/src/apis/api/insight/expert_analysis';
import { ExpertAnalysis } from '@quality/common-apis/src/apis/api/insight/model';
import { metric_dict } from '@quality/common-apis/src/apis/api/zepto/complex_metric';

import { TimeFilterValue } from '@/components/TimeFilter';

export interface AnaylsisRequestParams
  extends Omit<GetExpertAnalysisRequest, 'version'>,
    Pick<
      ExpertAnalysis,
      | 'metric_name'
      | 'metric_url'
      | 'report_name'
      | 'report_url'
      | 'template_id'
    > {}
export interface ExpertAnalysisProps extends AnaylsisRequestParams {
  editable?: boolean;
  metricData?: any;
  timeFilterInfo?: TimeFilterValue;
  metric_expand_attribute?: metric_dict.MetricExpandAttribute;
  placeholder?: string;
  report_id?: string;
  freezeVersion?: string;
  analysisResponse?: Partial<ExpertAnalysis>;
  loading?: boolean;
  expertValue?: DeltaSet<IBaseDelta<Op>>;
}

export interface InnerExpertAnalysisProps extends ExpertAnalysisProps {
  container: HTMLElement;
}

export interface EAHandle {
  getEditor: () => Editor;
}
