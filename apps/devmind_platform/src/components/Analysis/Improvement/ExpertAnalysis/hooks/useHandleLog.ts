import { ERROR_LEVEL } from '@editor-kit/core';

import { log } from '@quality/common-utils';

export const useHandleLog = () => {
  const handleLog = ({
    level,
    message,
  }: {
    level: ERROR_LEVEL;
    message: string;
  }) => {
    if (level === ERROR_LEVEL.FATAL) {
      log.fatal(message);
    }
    if (level === ERROR_LEVEL.ERROR) {
      log.error(message);
    }
  };

  return handleLog;
};
