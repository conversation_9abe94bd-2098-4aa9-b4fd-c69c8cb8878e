import { useState } from 'react';

import { DeltaSet, ROOT_ZONE, ZoneDelta } from '@editor-kit/delta';
import { useDebounceFn, useDeepCompareEffect } from 'ahooks';

import {
  expertAnalysisServiceClient,
  GetExpertAnalysisRequest,
} from '@quality/common-apis/src/apis/api/insight/expert_analysis';
import { ExpertAnalysis } from '@quality/common-apis/src/apis/api/insight/model';
import { log } from '@quality/common-utils';

import { API_V1 } from '@/constants';

const getInitialContent = () =>
  new DeltaSet({
    [ROOT_ZONE]: new ZoneDelta({
      zoneId: ROOT_ZONE,
      ops: [{ insert: '\n' }],
    }),
  });

export const useFetchAnalysis = (
  params: Omit<GetExpertAnalysisRequest, 'version'>,
) => {
  const [isFetching, setFetching] = useState(false);
  const [value, setValue] = useState<DeltaSet>(getInitialContent());
  const [analysisResponse, setAnalysisResponse] = useState<
    ExpertAnalysis | undefined
  >(undefined);

  const { run: debouncedFetchData } = useDebounceFn(
    () => {
      setFetching(true);

      expertAnalysisServiceClient
        .GetExpertAnalysis({
          ...params,
          version: API_V1,
        })
        .then(res => res.data)
        .then(res => {
          setAnalysisResponse(res);
          if (res) {
            try {
              const data = JSON.parse(res?.expert_analysis);

              setValue(new DeltaSet(data));
            } catch (error) {
              log.error(error);
              setValue(getInitialContent());
            }
          } else {
            setValue(getInitialContent());
          }
        })
        .finally(() => {
          setFetching(false);
        });
    },
    { wait: 100 },
  );

  useDeepCompareEffect(() => {
    debouncedFetchData();
  }, [params]);

  return {
    isFetching,
    value,
    analysisResponse,
    refreshExpertAnalysis: debouncedFetchData,
  };
};
