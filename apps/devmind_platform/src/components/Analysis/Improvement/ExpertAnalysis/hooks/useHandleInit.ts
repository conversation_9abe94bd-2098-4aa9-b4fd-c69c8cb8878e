import { useRef, useState } from 'react';

import { Message } from '@arco-design/web-react';
import { Editor, EditorEventType } from '@editor-kit/core';
import { ROOT_ZONE, ZoneDelta } from '@editor-kit/delta';

import { expertAnalysisServiceClient } from '@quality/common-apis/src/apis/api/insight/expert_analysis';
import { ExpertAnalysis } from '@quality/common-apis/src/apis/api/insight/model';

import { API_V1 } from '@/constants';
import { MetricsModule } from '@/modules/metrics';

import {
  COMMAND_SAVE,
  SHOW_AUTO_RESULT,
  getMentionedUser,
  isAtUserBlock,
} from '../../helper';

const internalPasteWhitelist = ['lark_docs', 'lark_sheet'];

const updateAnalysis = (params: ExpertAnalysis) => {
  if (params?.report_url) {
    params.report_url = params?.report_url?.replace('mode=write&', '');
  }
  if (!params?.report_name) {
    delete params.report_url;
    delete params.report_name;
  }
  return expertAnalysisServiceClient
    .UpdateExpertAnalysis({
      version: API_V1,
      data: {
        ...params,
      },
    })
    .then(res => {
      if (res.data) {
        Message.success('保存分析结论成功');
        MetricsModule.get
          .expertAnalysisSubjectMap()
          .get(res.data.metric_id)
          ?.next({
            payload: {
              data: res.data,
              target: {
                nodeId: res.data.business_id,
                startTime: res.data.start_time,
                endTime: res.data.end_time,
              },
            },
          });
      }
      return res;
    })
    .catch(() => {
      Message.error('添加分析结论失败！');
      return null;
    });
};

export const useHandleInit = (_analysisRes: Partial<ExpertAnalysis>) => {
  const [editor, setEditor] = useState<Editor | null>(null);
  const savedStringifedDeltas = useRef('');
  const analysisData: Partial<ExpertAnalysis> = {
    ...(_analysisRes?.id ? _analysisRes : { ..._analysisRes, id: undefined }),
  };
  const analysisRes = useRef(analysisData);

  analysisRes.current = { ...analysisRes.current, ...analysisData };
  const handleInit = (editor: Editor) => {
    setEditor(editor);

    editor.clipboard.setCanInternalPasteWhitelist(internalPasteWhitelist);

    const saveContent = (auto_generated = true) => {
      const users: Set<string> = new Set();

      const content = editor.getContent();
      const deltas = content.deltas;
      const ZoneDelta = deltas[ROOT_ZONE] as ZoneDelta;

      ZoneDelta.forEach(op => {
        if (isAtUserBlock(op)) {
          const user = getMentionedUser(op);
          if (user != null) {
            users.add(user);
          }
        }
      });

      const stringifedDeltas = JSON.stringify(deltas);

      // 空状态到空状态不保存
      if (
        savedStringifedDeltas.current === '' &&
        ZoneDelta?.ops?.[0]?.insert === '\n'
      ) {
        return;
      }

      if (stringifedDeltas !== savedStringifedDeltas.current) {
        const expert_analysis = stringifedDeltas;
        updateAnalysis({
          ...analysisRes.current,
          // 通过命令进行的保存
          auto_generated,
          mention_users: Array.from(users),
          expert_analysis,
        } as ExpertAnalysis).then(res => {
          if (res?.code === 200) {
            // if (res?.data) {
            //   analysisRes.current = { ...analysisRes.current, ...res.data };
            // } else {
            //   analysisRes.current.id = CLEAR_STATE;
            // }
            analysisRes.current = { ...analysisRes.current, ...res.data };
          }
        });

        savedStringifedDeltas.current = stringifedDeltas;
      }
    };

    const setAutoContent = () => {
      const users: Set<string> = new Set();

      const content = editor.getContent();
      const deltas = content.deltas;
      const ZoneDelta = deltas[ROOT_ZONE] as ZoneDelta;

      ZoneDelta.forEach(op => {
        if (isAtUserBlock(op)) {
          const user = getMentionedUser(op);
          if (user != null) {
            users.add(user);
          }
        }
      });

      const stringifedDeltas = JSON.stringify(deltas);
      savedStringifedDeltas.current = stringifedDeltas;
    };

    editor.on(EditorEventType.BLUR, () => saveContent(false));
    editor.registerCommand(COMMAND_SAVE, () => saveContent(true));
    editor.registerCommand(SHOW_AUTO_RESULT, setAutoContent);
  };

  return { editor, handleInit };
};
