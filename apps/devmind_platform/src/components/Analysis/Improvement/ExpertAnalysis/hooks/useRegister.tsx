import React, { useRef } from 'react';

import {
  Editor,
  IPlugin,
  TextNodeDeserializer,
  BlockElementDeserializer,
} from '@editor-kit/core';
import {
  Bold,
  List,
  Align,
  Aside,
  Quote,
  Indent,
  Italic,
  Heading,
  Profile,
  Suggest,
  Toolbar,
  Checkbox,
  FontSize,
  Markdown,
  ToolbarUi,
  FontColor,
  PCMention,
  Underline,
  FontFamily,
  FontWeight,
  HyperLinks,
  ContextProps,
  MarkdownType,
  Strikethrough,
  LinkToMention,
  HorizontalLine,
  ToolbarElement,
  BackgroundColor,
  PlainTextHandler,
  KeyboardShortcuts,
  createToolbarManager,
} from '@editor-kit/plugins';
import type { AtUserData } from '@editor-kit/plugins';
import ReactDOM from 'react-dom';

import { userOperationServiceClient } from '@quality/common-apis/src/apis/api/user/user';

import { API_V1 } from '@/constants';

import { ZHTexts } from '../config/i18n';
import { defaultShortcutsKeyMap } from '../config/keyMaps';
import { colors } from '../config/palette';

const markdownOptions = {
  types: [
    MarkdownType.heading,
    MarkdownType.ul,
    MarkdownType.ol,
    MarkdownType.checkbox,
    MarkdownType.bold,
    MarkdownType.italic,
    MarkdownType.underline,
    MarkdownType.blockQuote,
    MarkdownType.strikethrough,
    MarkdownType.horizontal,
  ],
};

function renderToolbar(
  context: ContextProps,
  container: HTMLElement,
  _toolbarId?: string,
) {
  const element = ToolbarUi.wrapper(
    ToolbarUi.group(
      ToolbarUi.heading(),
      ToolbarUi.color({ defaultColor: '#000000', colors }),
      ToolbarUi.backgroundColor({ defaultColor: '#FFFFFF', colors }),
    ),
    ToolbarUi.group(
      ToolbarUi.bold(),
      // ToolbarUi.strikethrough(),
      // ToolbarUi.underline(),
      // ToolbarUi.italic(),
    ),
    ToolbarUi.group(
      // ToolbarUi.alignLeft(),
      // ToolbarUi.alignCenter(),
      // ToolbarUi.alignRight(),
      ToolbarUi.unorderedList(),
      ToolbarUi.orderedList(),
    ),
    ToolbarUi.group(
      ToolbarUi.checkbox(),
      // ToolbarUi.blockquote(),
      // ToolbarUi.indent(),
      ToolbarUi.horizontalLine(),
      ToolbarUi.hyperlink(),
    ),
  );
  const toolbarId = _toolbarId || 'adit-toolbar';
  let mountPoint = document.getElementById(toolbarId);
  if (!mountPoint) {
    mountPoint = document.createElement('div');
    mountPoint.setAttribute('id', toolbarId);
    container.appendChild(mountPoint);
  }
  return ToolbarElement.render(element, mountPoint, context);
}

export const useRegister = ({
  toolbarContainer,
  editable,
  toolbarId,
}: {
  toolbarContainer: HTMLElement;
  editable: boolean;
  toolbarId?: string;
}) => {
  const showSuggestTimerRef = useRef<any>(null);

  const register = React.useCallback(
    (editor: Editor) =>
      [
        new Markdown({ editor, markdownOptions }), // markdown
        new List({ editor }), // order/unorder list
        new TextNodeDeserializer(), // HTML反序列化对文本节点的默认处理逻辑，比如空格、换行等字符的处理
        new BlockElementDeserializer(), // HTML反序列化对块级节点的默认处理逻辑，比如div、li节点需要换行，span不换行
        new KeyboardShortcuts({
          editor,
          shortcutsMap: defaultShortcutsKeyMap,
        }), // 快捷键处理插件
        new PlainTextHandler(),
        new Bold({ editor }), // 加粗插件
        new Align({ editor }), // 居左、中、右
        new BackgroundColor({ editor }), // 背景色
        new Italic({ editor }), // 斜体
        new Underline({ editor }), // 下划线
        new Strikethrough({ editor }), // 删除线
        new Quote({ editor }), // 块级引用
        new Heading({ editor }), // H1、H2...
        new FontColor({ editor }), // 字体颜色
        new FontFamily({ editor }), // 字体
        new FontSize({ editor }), // 字体大小
        new FontWeight({ editor }), // 字体粗细
        new HorizontalLine({ editor }), // 分割线
        new Indent({ editor }), // 缩进
        new Checkbox({ editor }), // checkbox/todo
        new HyperLinks({
          editor,
          i18n: ZHTexts,
          customProtocols: ['lark'],
          customLinkRegexp: new RegExp(
            '(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]',
          ),
        }), // 超链接，包括对文本添加超链接属性和自动识别超链接格式的字符
        /* IFTRUE_prodEnv */
        new PCMention({
          editor,
          lang: 'zh',
          // onClick: function (data) { },
          // getPopupContainer() {
          //   return toolbarContainer as any;
          // },
          onSuggest({ onConfirm, anchorRect, mountNode, query }) {
            ReactDOM.render(
              <div style={{ position: 'absolute', zIndex: 2000 }}>
                <Suggest
                  lang="zh"
                  i18n={ZHTexts}
                  anchorRect={anchorRect}
                  editor={editor}
                  onConfirm={onConfirm}
                  query={query}
                  renderAside={() => {
                    return <Aside />;
                  }}
                  getData={() => {
                    return query
                      ? userOperationServiceClient
                          .SearchUserInfo({
                            version: API_V1,
                            username: query,
                          })
                          .then(res => res.data)
                          .then(users => {
                            return users.map(
                              item =>
                                ({
                                  type: 0, // 0 为用户信息，1 为文档信息
                                  avatar_url: item.avatar,
                                  cn_name: item.name,
                                  en_name: item.en_name,
                                  email: item.email,
                                  name: item.username,
                                  id: item.employee_number,
                                  is_external: false,
                                  department: item.dept_name,
                                } as AtUserData),
                            );
                          })
                      : Promise.resolve([]);
                  }}
                />
              </div>,
              mountNode,
            );

            return {
              close: () => {
                ReactDOM.unmountComponentAtNode(mountNode);
              },
            };
          },
          onMouseEnter({ anchorRect, data, mountNode }) {
            if (data.type === 0) {
              // todo 提供一个profile组件
              showSuggestTimerRef.current = setTimeout(() => {
                ReactDOM.render(
                  <Profile lang="zh-CN" user={data} anchorRect={anchorRect} />,
                  mountNode,
                );
              }, 500);
            }
          },
          onMouseLeave({ mountNode }) {
            clearTimeout(showSuggestTimerRef.current);
            showSuggestTimerRef.current = null;
            ReactDOM.unmountComponentAtNode(mountNode);
          },
        }), // Mention
        new LinkToMention({
          editor,
          getLinkInfo: () => {
            return new Promise(resolve => {
              setTimeout(() => {
                resolve({
                  // 文档title
                  title: 'test demo title',
                });
              }, 3000);
            });
          },
        }),
        /* FITRUE_prodEnv */
        editable &&
          new Toolbar({
            editor,
            i18n: ZHTexts,
            Manager: createToolbarManager((context: ContextProps) => {
              return renderToolbar(context, toolbarContainer, toolbarId);
            }),
          }),
      ].filter(Boolean) as IPlugin[],
    [toolbarContainer, toolbarId],
  );

  return register;
};
