import * as React from 'react';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { Empty, Spin } from '@arco-design/web-react';
import { EditorComponent } from '@editor-kit/core';
import { ToolbarItemEnum } from '@editor-kit/plugins';
import clsx from 'clsx';
import { uniqueId } from 'lodash';

import {
  getBasicPlugins,
  getMentionPlugin,
  getToolbarPlugin,
  ToolbarV2Wrapper,
} from '@/modules/editor-kit/plugins';
import { EditorManager } from '@/modules/editor-kit/utils';

import { SHOW_AUTO_RESULT } from '../helper';

import { useHandleInit } from './hooks/useHandleInit';
import { useHandleLog } from './hooks/useHandleLog';
import './styles.scss';
import {
  AnaylsisRequestParams,
  ExpertAnalysisProps,
  InnerExpertAnalysisProps,
  EAHandle,
} from './interface';

const InnerExpertAnalysis = forwardRef<EAHandle, InnerExpertAnalysisProps>(
  (props, ref) => {
    const {
      metric_name,
      metric_url,
      template_id,
      report_name,
      report_url,
      metric_id,
      business_id,
      start_time,
      end_time,
      editable = true,
      analysisResponse,
      loading,
      expertValue: value,
      placeholder = '请输入分析',
    } = props;

    const baseAnalysisRequest: AnaylsisRequestParams = {
      metric_id,
      business_id,
      start_time,
      end_time,
      metric_name,
      metric_url,
      template_id,
      report_name,
      report_url,
    };

    const [uniqueToolbarId] = useState(uniqueId('ExpoertAnalysis__'));

    const [editorManager] = useState(new EditorManager('core'));
    const register = React.useCallback(
      editor => [
        ...getBasicPlugins(editor),
        ...getMentionPlugin(editor),
        ...getToolbarPlugin(editor, {
          toolbarId: uniqueToolbarId,
          items: [
            ToolbarItemEnum.HeadingDropdown,
            ToolbarItemEnum.ColorPicker,
            ToolbarItemEnum.DIVIDER,

            ToolbarItemEnum.Bold,
            ToolbarItemEnum.Hyperlink,
            ToolbarItemEnum.DIVIDER,

            ToolbarItemEnum.UnorderedList,
            ToolbarItemEnum.OrderedList,
            ToolbarItemEnum.DIVIDER,

            ToolbarItemEnum.HorizontalLine,
            ToolbarItemEnum.Checkbox,
          ],
        }),
      ],
      [uniqueToolbarId],
    );

    const { editor, handleInit } = useHandleInit({
      ...baseAnalysisRequest,
      ...(analysisResponse ?? {}),
    });

    const handleLog = useHandleLog();

    useImperativeHandle(
      ref,
      () => ({
        getEditor: () => editor!,
      }),
      [editor],
    );

    useEffect(() => {
      if (editor && value) {
        editor.setContent(value);
        editor.execCommand(SHOW_AUTO_RESULT);
      }
    }, [editor, value]);

    if (!editable && value?.get('0').ops[0].insert === '\n') {
      return <Empty description="暂无分析结论" />;
    }

    return (
      <Spin loading={loading} style={{ width: '330px' }}>
        <ToolbarV2Wrapper
          id={uniqueToolbarId}
          className={clsx('editor-kit-container-v2', !editable && 'hidden')}
        />
        <EditorComponent
          copyable={true}
          editable={!loading && editable}
          modules={editorManager.modules}
          schema={editorManager.schema}
          placeholder={placeholder}
          businessKey={editorManager.businessKey}
          spellCheck={false}
          debounceFlush={100}
          register={register}
          onInit={handleInit}
          onError={handleLog}
        />
      </Spin>
    );
  },
);

export const ExpertAnalysis = forwardRef<EAHandle, ExpertAnalysisProps>(
  (props, ref) => {
    const { metric_id, business_id, start_time, end_time } = props;
    const [container, containerSetter] = useState<any>(null);

    const isReady =
      container && metric_id && business_id && start_time && end_time;

    return (
      <div ref={containerSetter} className="container ExpertAnalysis">
        {isReady ? (
          <InnerExpertAnalysis {...props} container={container} ref={ref} />
        ) : null}
      </div>
    );
  },
);
