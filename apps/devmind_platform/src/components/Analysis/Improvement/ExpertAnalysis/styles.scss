.ExpertAnalysis {
  &.container {
    padding: 0 !important;
    background-color: var(--measure-fill2);
  }

  // toolbar 样式覆盖
  .editor-kit-wrapper-container {
    z-index: 1 !important;
  }

  .editor-kit-container-v2 {
    .editor-kit-toolbar-v2-wrapper {
      position: initial;
      top: 0;
    }
  }

  // 编辑器文字区域样式
  .editor-kit-container {
    font-size: 14px;
    padding: 5px 12px;
    height: 140px;
    overflow-y: auto;

    &::before {
      left: 12px;
      top: 5px;
    }
  }
}
