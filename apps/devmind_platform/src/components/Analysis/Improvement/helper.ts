import {
  MODULE_KEYS,
  ContentState,
  ZoneState,
  Range,
  UndoModule,
  InputModel,
} from '@editor-kit/core';
import { Op } from '@editor-kit/delta';

import { log } from '@quality/common-utils';

export enum ImprovementTabType {
  ExpertAnalysis = '专家分析',
  ImprovementWay = '改进方案',
  ImprovementTask = '改进任务',
}

export const BUSINESS_KEY = 'devmind';

export const modules = {
  [MODULE_KEYS.ContentState]: ContentState,
  [MODULE_KEYS.ZoneState]: ZoneState,
  [MODULE_KEYS.Range]: Range,
  [MODULE_KEYS.UndoModule]: UndoModule,
  [MODULE_KEYS.InputModel]: InputModel,
};

const INTERNAL_AT_ATTRIBUTE_KEY = 'dataMetaBlockProps';
const AT_USER_BLOCK_TYPE = 'AT_USER_BLOCK';

/**
 * @see https://dev.byted.org/ee/editor-kit-plugins/blob/HEAD/src/mention/__doc__/plugin-mention.md#L320
 */
export const isAtUserBlock = (op: Op) => {
  try {
    if (op.attributes?.[INTERNAL_AT_ATTRIBUTE_KEY]) {
      return (
        JSON.parse(op.attributes?.[INTERNAL_AT_ATTRIBUTE_KEY]).blockType ===
        AT_USER_BLOCK_TYPE
      );
    }
    return false;
  } catch (error) {
    return false;
  }
};

export const getMentionedUser = (op: Op): string | null => {
  try {
    const { props } = JSON.parse(op.attributes![INTERNAL_AT_ATTRIBUTE_KEY]);
    const { en_name } = props.data.user;

    return en_name;
  } catch (error) {
    log.error(error);
    return null;
  }
};

export const COMMAND_SAVE = 'save';

export const SHOW_AUTO_RESULT = 'show_auto_result';
