import { Empty, Link } from '@arco-design/web-react';
import { IconFile } from '@arco-design/web-react/icon';

export const ImproveCase = ({ data }: any) => {
  const cases = data.metric_expand_attribute?.improve_case;
  const plan = data.metric_expand_attribute?.improve_plan;
  return (
    <div
      style={{
        minHeight: '156px',
      }}>
      {plan?.length === 0 && cases?.length === 0 && (
        <Empty description="暂无方案/案例" />
      )}
      {plan?.length > 0 && (
        <>
          <div className="mt-[0px] mb-[4px] text-xs text-text3 font-normal">
            方案
          </div>
          {plan.map((i, k) => {
            return (
              <div
                key={k}
                className="w-full h-[54px] p-4 flex items-center bg-fill2 text-[13px] mb-[6px]">
                <Link
                  target={'_blank'}
                  href={i.description}
                  icon={<IconFile />}>
                  {i.name}
                </Link>
              </div>
            );
          })}
        </>
      )}
      {cases?.length > 0 && (
        <>
          <div className="mt-[12px] mb-[4px] text-xs text-text3 font-normal">
            案例
          </div>
          {cases.map((i, k) => {
            return (
              <div
                key={k}
                className="w-full h-[54px] p-4 flex items-center bg-fill2 text-[13px] mb-[6px]">
                <Link
                  target={'_blank'}
                  href={i.description}
                  icon={<IconFile />}>
                  {i.name}
                </Link>
              </div>
            );
          })}
        </>
      )}
    </div>
  );
};
