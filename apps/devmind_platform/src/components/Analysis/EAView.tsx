import * as React from 'react';
import { useCallback, useRef } from 'react';

import { Button, Message } from '@arco-design/web-react';
import { IconCopy } from '@arco-design/web-react/icon';
import { Editor, EditorComponent, IPluginRegister } from '@editor-kit/core';
import { DeltaSet } from '@editor-kit/delta';
import clsx from 'clsx';
import copy from 'copy-to-clipboard';

import { log } from '@quality/common-utils';

import {
  getBasicPlugins,
  getMentionPlugin,
} from '@/modules/editor-kit/plugins';
import { EditorManager } from '@/modules/editor-kit/utils';

import { ContentWrapper } from './Reason/style';

interface EAViewProps {
  deltaSetString?: string;

  withWrapper?: boolean;
}
export const EAView: React.FC<EAViewProps> = ({
  deltaSetString,
  withWrapper = true,
}) => {
  const editorManagerRef = useRef(new EditorManager());
  const ek = useRef<Editor>();
  const [empty, setEmpty] = React.useState(false);

  const register: IPluginRegister = useCallback(
    editor => [...getBasicPlugins(editor), ...getMentionPlugin(editor)],
    [],
  );

  React.useEffect(() => {
    if (!deltaSetString) {
      return;
    }

    try {
      const deltaSet = new DeltaSet(JSON.parse(deltaSetString));
      if (ek.current) {
        ek.current.setContent(deltaSet);

        setEmpty(ek.current.isEmpty());
      }
    } catch (error) {
      log.error(error);
    }
  }, [deltaSetString]);

  return (
    <div className={clsx(empty && 'hidden')}>
      {withWrapper ? (
        <ContentWrapper
          title="专家分析"
          action={
            <Button
              size="mini"
              type="text"
              icon={<IconCopy />}
              onClick={() => {
                const text = ek.current?.getText();
                if (text) {
                  copy(text);
                  Message.success('复制成功');
                }
              }}>
              一键复制
            </Button>
          }>
          <EditorComponent
            className="p-3 rounded border border-[#dde2e9]"
            style={{ background: 'var(--measure-fill3)' }}
            businessKey={editorManagerRef.current.businessKey}
            register={register}
            modules={editorManagerRef.current.modules}
            schema={editorManagerRef.current.schema}
            onError={editorManagerRef.current.onError}
            editable={false}
            onInit={editor => (ek.current = editor)}
          />
        </ContentWrapper>
      ) : (
        <EditorComponent
          className="p-3 rounded border border-[#dde2e9]"
          style={{ background: 'var(--measure-fill3)' }}
          businessKey={editorManagerRef.current.businessKey}
          register={register}
          modules={editorManagerRef.current.modules}
          schema={editorManagerRef.current.schema}
          onError={editorManagerRef.current.onError}
          editable={false}
          onInit={editor => (ek.current = editor)}
        />
      )}
    </div>
  );
};
