import * as React from 'react';

import { Descriptions, Modal, Tag } from '@arco-design/web-react';
import NiceModal, { useModal } from '@ebay/nice-modal-react';

import { ImprovementTaskInfo } from '@quality/common-apis/src/apis/api/insight/model';
import { dateTimeFormat } from '@quality/common-utils';

import { taskLevelMap } from '@/constants/metric';
import UserAvatar from '../UserAvatar';

export const TaskModal = NiceModal.create<{ task: ImprovementTaskInfo }>(
  ({ task }) => {
    const modal = useModal();

    return (
      <Modal
        title="任务详情"
        className="w-[600px]"
        visible={modal.visible}
        onOk={modal.remove}
        onCancel={modal.remove}
        mask
        maskStyle={{ opacity: 0.2 }}
        hideCancel>
        <Descriptions
          column={1}
          className="no-scrollbar"
          style={{
            textOverflow: 'ellipsis',
            wordWrap: 'break-word',
            overflowY: 'scroll',
            overflowX: 'hidden',
          }}
          valueStyle={{
            maxWidth: '420px',
          }}
          data={[
            { label: '任务名称', value: task?.task_name },
            {
              label: '任务描述',
              value: <div>{task?.task_desc}</div>,
            },
            {
              label: '任务责任人',
              value: (
                <UserAvatar
                  size={20}
                  length={10}
                  // wrapperStyle={{transform: 'translateX(6px)'}}
                  dataList={task?.task_owners.map(o => o.name)}
                />
              ),
            },
            {
              label: '优先级',
              value: (
                <Tag
                  color={taskLevelMap[task.task_level].color}
                  size="small"
                  style={{
                    marginRight: '4px',
                    padding: '1px 2px',
                    height: '16px',
                    marginBottom: '1px',
                  }}>
                  {taskLevelMap[task.task_level].label}
                </Tag>
              ),
            },
            {
              label: '计划时间',
              value: `${`${dateTimeFormat(task?.exp_start_time, {
                format: 'YYYY/MM/DD',
              })}`} - ${dateTimeFormat(task?.exp_end_time, {
                format: 'YYYY/MM/DD',
              })}`,
            },
          ]}
          labelStyle={{ paddingRight: 40 }}
        />
      </Modal>
    );
  },
);

interface TasksViewProps {
  tasks: ImprovementTaskInfo[] | undefined;
}
export const TasksView: React.FC<TasksViewProps> = ({ tasks }) => {
  const createTaskModal = useModal(TaskModal);

  return tasks?.length ? (
    <div className="flex flex-col">
      {tasks.map(task => (
        <div
          key={task.id}
          className="flex justify-between"
          onClick={() => createTaskModal.show({ task })}>
          <div className="flex items-center cursor-pointer hover:bg-gray-100">
            <Tag
              style={{ marginRight: 4, padding: '1px 2px', height: 16 }}
              size="small"
              color={taskLevelMap[task.task_level].color}>
              {taskLevelMap[task.task_level].label}
            </Tag>

            <span className="text-xs">{task.task_name}</span>
          </div>

          {task.task_owners.length && (
            <UserAvatar
              size={20}
              length={3}
              dataList={task.task_owners.map(o => o.name)}
            />
          )}
        </div>
      ))}
    </div>
  ) : null;
};
