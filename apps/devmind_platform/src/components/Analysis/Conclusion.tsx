import React, { CSSProperties, FC, useMemo } from 'react';

import { Skeleton, Popover, Empty } from '@arco-design/web-react';
import { useModel } from '@reduck/core';

import { CommonDataUnit } from '@quality/common-apis/src/apis/api/base/data_unit';
import {
  TextStatusType,
  RuleConclusion,
} from '@quality/common-apis/src/apis/rule_engine/rule_text';
import { getValueArrowColor } from '@quality/common-components/src/VisualPanel/_utils';
// eslint-disable-next-line max-len
import { SCCardCompareSpanIcon } from '@quality/common-components/src/VisualPanel/VisualCard/components/ChainRatioCard/style';
import { dataUnitFormatter } from '@quality/common-utils';

import { reportModel } from '@/model/report';

import { reportDetailStoreUse } from '../ReportDetail/store';

const textBgColorMap = {
  [TextStatusType.Good]: '#AFF0B5',
  [TextStatusType.Warning]: '#FFE4BA',
  [TextStatusType.Critical]: '#FDCDC5',
};

const fullScreenTextBgColorMap = {
  [TextStatusType.Good]: 'rgba(43, 166, 33, 0.5)',
  [TextStatusType.Warning]: 'rgba(214, 125, 44, 0.5)',
  [TextStatusType.Critical]: 'rgba(190, 56, 56, 0.5)',
};

interface Props {
  loading?: boolean;
  conlusion: RuleConclusion | undefined;
  dataUnit: CommonDataUnit | undefined;
  empty?: React.ReactNode;
  style?: CSSProperties;
  isShowPeriodView?: boolean;
}

export const getConlusion = conlusion => {
  return conlusion?.phrase_list?.map(({ text }) => text).join('');
};

const Conlusion: FC<Props> = props => {
  const {
    loading = false,
    conlusion,
    dataUnit,
    empty,
    style,
    isShowPeriodView = false,
  } = props;
  const [{ fullScreen }] = useModel([reportModel]);

  const cycleComparisonState = reportDetailStoreUse.cycleComparisonState();

  const periods = conlusion?.phrase_list?.filter(
    item => (item?.hover_texts?.length || 0) > 0,
  );
  const period =
    (periods?.length || 0) > 0 ? periods?.[periods?.length - 1] : undefined;

  const realPeriod = useMemo(() => {
    const periods = period?.hover_texts?.filter(
      items => items.filter(item => Boolean(item.if_bold)).length > 0,
    );
    // 第一种情况 上个周期比和历史对比同时存在
    // 第二种情况，只有历史对比且对比大于一个，取前两个
    // 第三种情况，只有上个周期或历史对比，且值唯一
    if ((periods?.length || 0) > 1 && (period?.hover_texts?.length || 0) > 2) {
      return period?.hover_texts?.slice(-2);
    } else if (
      (periods?.length || 0) === 1 &&
      (period?.hover_texts?.length || 0) > 2
    ) {
      return period?.hover_texts?.slice(0, 2);
    } else {
      return period?.hover_texts;
    }
  }, [period?.hover_texts]);

  return (
    <Skeleton text={{ rows: 2 }} loading={loading} animation={true}>
      <div style={{ fontSize: 14, ...style }}>
        {conlusion?.phrase_list?.length ? (
          <>
            {conlusion?.phrase_list?.map(
              (
                { text, status, if_hover, if_bold, hover_content, hover_texts },
                index,
              ) => (
                <Popover
                  key={index}
                  position="bottom"
                  disabled={!if_hover}
                  className="no-max-popover"
                  triggerProps={{ showArrow: false }}
                  content={
                    hover_content
                      ? hover_content.items.map(({ key, value, need_unit }) => (
                          <div
                            key={key}
                            className="flex items-center text-text4">
                            <span className="mr-2 w-2 h-2 rounded-full bg-brand5" />
                            <span className="mr-4">{key}: </span>
                            <span className="ml-auto font-medium">
                              {need_unit
                                ? dataUnitFormatter(value, dataUnit)
                                : value}
                            </span>
                          </div>
                        ))
                      : hover_texts?.map((item, index) => (
                          <div
                            key={String(index)}
                            style={{ marginBottom: '8px' }}>
                            {item.map(
                              ({
                                text,
                                if_unit,
                                if_bold,
                                text_status,
                                is_rise,
                                texts,
                              }) => {
                                let value = text;
                                if (if_unit) {
                                  value = texts
                                    ? `（${texts
                                        .map((item, index) =>
                                          index === texts.length - 1
                                            ? dataUnitFormatter(item, dataUnit)
                                            : `${dataUnitFormatter(
                                                item,
                                                dataUnit,
                                                false,
                                              )} → `,
                                        )
                                        .join('')}）`
                                    : dataUnitFormatter(value, dataUnit);
                                }
                                return (
                                  <span
                                    key={text}
                                    className={
                                      if_bold
                                        ? 'text-sm font-bold'
                                        : 'text-text4'
                                    }
                                    style={{
                                      color: if_bold
                                        ? '#1D2129'
                                        : text_status != null
                                        ? getValueArrowColor(text_status)
                                        : '#4E5969',
                                    }}>
                                    {is_rise != null ? (
                                      <SCCardCompareSpanIcon
                                        color={getValueArrowColor(text_status)}
                                        className={is_rise ? 'active' : ''}
                                      />
                                    ) : (
                                      value
                                    )}
                                  </span>
                                );
                              },
                            )}
                          </div>
                        ))
                  }>
                  <span
                    className="conclusion--text"
                    style={{
                      background:
                        (fullScreen
                          ? fullScreenTextBgColorMap
                          : textBgColorMap)[status] || 'inherit',
                      fontWeight: if_bold ? 500 : 400,
                      ...(fullScreen ? { color: '#9DA3AF' } : {}),
                    }}>
                    {text}
                  </span>
                </Popover>
              ),
            )}
            {isShowPeriodView &&
              cycleComparisonState &&
              realPeriod?.length &&
              !fullScreen && (
                <div
                  style={{
                    backgroundColor: '#f6f8fa',
                    height: 90,
                    marginTop: 12,
                    borderRadius: 8,
                    padding: 12,
                  }}>
                  {realPeriod?.map((item, index) => (
                    <div
                      key={String(index)}
                      style={{
                        marginBottom: '8px',
                      }}>
                      {item.map(
                        ({
                          text,
                          if_unit,
                          if_bold,
                          text_status,
                          is_rise,
                          texts,
                        }) => {
                          let value = text;
                          if (if_unit) {
                            value = texts
                              ? `（${texts
                                  .map((item, index) =>
                                    index === texts.length - 1
                                      ? dataUnitFormatter(item, dataUnit)
                                      : `${dataUnitFormatter(
                                          item,
                                          dataUnit,
                                          false,
                                        )} → `,
                                  )
                                  .join('')}）`
                              : dataUnitFormatter(value, dataUnit);
                          }
                          return (
                            <span
                              key={text}
                              className={
                                if_bold ? 'text-sm font-bold' : 'text-text4'
                              }
                              style={{
                                color: if_bold
                                  ? '#1D2129'
                                  : text_status != null
                                  ? getValueArrowColor(text_status)
                                  : '#4E5969',
                              }}>
                              {is_rise != null ? (
                                <SCCardCompareSpanIcon
                                  color={getValueArrowColor(text_status)}
                                  className={is_rise ? 'active' : ''}
                                />
                              ) : (
                                value
                              )}
                            </span>
                          );
                        },
                      )}
                    </div>
                  ))}
                </div>
              )}
          </>
        ) : empty ? (
          empty
        ) : (
          <Empty description="暂无结论" className="!p-0" />
        )}
      </div>
    </Skeleton>
  );
};

export default Conlusion;
