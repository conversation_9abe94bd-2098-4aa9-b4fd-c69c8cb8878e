import { FC, ReactNode } from 'react';

import styled from '@jupiter/plugin-runtime/styled';

export const SCBoldText = styled.span`
  font-size: 13px;
  font-weight: 500;
  color: ${({ theme }) => theme.textColor.text5};
`;

export const ContentWrapper: FC<{ title: string; action?: ReactNode }> = ({
  title,
  action = null,
  children,
}) => (
  <div className="flex flex-col gap-1">
    <div className="flex justify-between items-center">
      <div className="font-medium text-black T3-E-B">{title}</div>

      {action}
    </div>

    <div>{children}</div>
  </div>
);
