/* eslint-disable react/no-unused-prop-types */
import * as React from 'react';
import { useState } from 'react';

import {
  Button,
  Form,
  Grid,
  Message,
  Modal,
  Alert,
  Select,
  Notification,
} from '@arco-design/web-react';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { Editor } from '@editor-kit/core';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import { takeRight, get, isEmpty } from 'lodash';
import styled from 'styled-components';

import {
  AnalysisDimInfo,
  QueryData,
} from '@quality/common-apis/src/apis/api/base/base_data';
import { CommonDataUnit } from '@quality/common-apis/src/apis/api/base/data_unit';
import { expertAnalysisServiceClient } from '@quality/common-apis/src/apis/api/insight/expert_analysis';
import {
  AnalysisDirectionNegative,
  AnalysisDirectionPositive,
  StoryTypeComplexMeta,
  StoryTypeMeta,
} from '@quality/common-apis/src/apis/api/metric_platform/consts';
import { QueryRequest } from '@quality/common-apis/src/apis/api/query/query';
import { API_V1 } from '@quality/common-components';

import { isMetaMetricData } from '@/components/MetricDetailDrawer/utils';
import {
  endTimeFormat,
  startTimeFormat,
  TimeFilterValue,
} from '@/components/TimeFilter/interface';
import { MetricData } from '@/utils/metrics';

import { COMMAND_SAVE } from '../Improvement/helper';

import { generateExpertAnalysis } from './helper';

import '../../MetricDetailDrawer/components/index.scss';

const StyledFormItem = styled(Form.Item)`
  margin-bottom: 0 !important;
`;

const FieldsDict = {
  fluctuationAnalysisDim: 'fluctuation_param.analysis_dim',
  fluctuationInfluenceDirections: 'fluctuation_param.influence_directions',
  mainDistanceAnalysisDim: 'main_distance_param.analysis_dim',
  mainDistanceAnalysisPotentialDirections:
    'main_distance_param.potential_directions',
};

const isFormEmpty = data => {
  const formFields = Object.keys(FieldsDict).map(key =>
    get(data, FieldsDict[key]),
  );
  return formFields.every(field => isEmpty(field));
};

const isAnalysisDirectionAble = data => {
  return (
    isEmpty(get(data, FieldsDict.fluctuationAnalysisDim)) !==
      isEmpty(get(data, FieldsDict.fluctuationInfluenceDirections)) ||
    isEmpty(get(data, FieldsDict.mainDistanceAnalysisDim)) !==
      isEmpty(get(data, FieldsDict.mainDistanceAnalysisPotentialDirections))
  );
};

interface AutoGeneratorProps {
  editable?: boolean;
  getEditor: (() => Editor | undefined) | undefined;
  dimensionList: AnalysisDimInfo[];
  dataUnit: CommonDataUnit | undefined;
  dataSource: QueryData | undefined;
  metricData: MetricData | undefined;
  queryParams: QueryRequest | undefined;
  timeFilterInfo: TimeFilterValue;
  nodeId: string;
  refreshExpertAnalysis: () => void;
}
const AutoGenModal = NiceModal.create<
  AutoGeneratorProps & {
    setParentLoading: (loading: boolean) => void;
  }
>(
  ({
    getEditor,
    dataUnit,
    dimensionList,
    metricData,
    queryParams,
    dataSource,
    timeFilterInfo,
    setParentLoading,
    nodeId,
    refreshExpertAnalysis,
  }) => {
    const modal = useModal();
    const [form] = Form.useForm();
    const [errorTip, setErrorTip] = useState<string>('');

    const dimOptions = React.useMemo(
      () =>
        dimensionList.map(item => ({
          label: item.display_name,
          value: item.dim_id,
        })),
      [dimensionList],
    );

    const dimMap = React.useMemo(
      () => new Map(dimensionList.map(item => [item.dim_id, item] ?? [])),
      [dimensionList],
    );

    const { run: tryToGenerate } = useRequest(
      async () => {
        const data = await form.validate();
        if (isFormEmpty(data)) {
          setErrorTip('波动影响与主要差距,至少需一个有数据');
          return;
        } else if (isAnalysisDirectionAble(data)) {
          setErrorTip('分析维度与其对应排序,需要同时填写');
          return;
        }
        setErrorTip('');
        const fluctuation_dims = form
          .getFieldValue(FieldsDict.fluctuationAnalysisDim)
          ?.map(item => ({
            display_name: dimMap.get(item)?.display_name,
            dim_id: item,
          }));
        const main_distance_dims = form
          .getFieldValue(FieldsDict.mainDistanceAnalysisDim)
          ?.map(item => ({
            display_name: dimMap.get(item)?.display_name,
            dim_id: item,
          }));

        // 生成接口里会进行保存
        // const saveRes =
        //   await expertAnalysisServiceClient.SaveExpertAnalysisGenerateConfig({
        //     version: API_V1,
        //     story_id: metricData?.id ?? '',
        //     node_id: nodeId,
        //     data: { fluctuation_dims, main_distance_dims },
        //   });
        // if (saveRes.code !== 200) {
        //   return;
        // }

        const editor = getEditor?.();

        const refer_time =
          dataSource?.chart_data?.rule_conclusion?.extra_info?.refer_index;

        const [target_time] = takeRight(dataSource?.chart_data?.x_axis, 1).map(
          item => item.string_value,
        );
        const analysis_direction =
          metricData?.metric_attribute?.analysis_direction || '';

        const { series } = dataSource?.chart_data || {};
        const baseline_value =
          series?.[0].extra_data?.[series?.[0].extra_data?.length - 1]
            ?.baseline_value || '';
        const valid_baseline = dataSource?.valid_baseline || [];

        const default_value = metricData?.metric_attribute?.default_value || '';
        const metric_direction =
          metricData!.metric_attribute?.analysis_direction || '';

        const [startDay, endDay] = timeFilterInfo?.range || [];

        if (!editor) {
          modal.hide();
          return;
        }

        setParentLoading(true);
        editor?.setEditable(false);
        const res = await expertAnalysisServiceClient
          .GenerateExpertAnalysis({
            version: API_V1,
            config_req: {
              story_id: metricData?.id ?? '',
              node_id: nodeId,
              data: { fluctuation_dims, main_distance_dims },
            },
            fluctuation_param: {
              analysis_direction,
              analysis_dim: form.getFieldValue(
                FieldsDict.fluctuationAnalysisDim,
              )?.[0], // 实际上不使用
              analysis_dims: form.getFieldValue(
                FieldsDict.fluctuationAnalysisDim,
              ),
              influence_directions: form.getFieldValue(
                FieldsDict.fluctuationInfluenceDirections,
              ),
              analysis_time: {
                refer_time: refer_time!,
                target_time,
              },
              default_value,
              rule_extra_info:
                dataSource?.chart_data?.rule_conclusion?.extra_info,
            },
            main_distance_param: {
              analysis_dim: form.getFieldValue(
                FieldsDict.mainDistanceAnalysisDim,
              )?.[0], // 实际上不使用
              analysis_dims: form.getFieldValue(
                FieldsDict.mainDistanceAnalysisDim,
              ),
              potential_directions: form.getFieldValue(
                FieldsDict.mainDistanceAnalysisPotentialDirections,
              ),
              baseline_value,
              default_value,
              metric_direction,
              valid_baseline,
              start_time: dayjs(startDay).format(startTimeFormat),
              end_time: dayjs(endDay).format(endTimeFormat),
            },
            query_req: queryParams!,
          })
          .finally(() => {
            setParentLoading(false);
            editor?.setEditable(true);
          });

        if (res.code === 200) {
          if (res.data?.fluctuation_analysis || res.data?.main_distance_data) {
            generateExpertAnalysis(res.data!, dimensionList, dataUnit, editor);
          } else {
            Message.error('暂无分析结论');
          }
          refreshExpertAnalysis();
          modal.remove();
        } else if (res.code === 500) {
          Notification.error({
            title: `请求失败:`,
            content: res.msg,
            position: 'bottomRight',
          });
        }
        modal.hide();
      },
      { manual: true },
    );
    const { run: getGenerateConfig } = useRequest(
      async () => {
        const res =
          await expertAnalysisServiceClient.GetExpertAnalysisGenerateConfig({
            version: API_V1,
            story_id: metricData?.id ?? '',
            story_type: isMetaMetricData(metricData!)
              ? StoryTypeMeta
              : StoryTypeComplexMeta,
            node_id: nodeId,
          });
        form.setFieldValue(
          FieldsDict.fluctuationAnalysisDim,
          res.data?.fluctuation_dims?.map(item => item.dim_id) ??
            dimensionList.slice(0, 1).map(item => item.dim_id),
        );
        form.setFieldValue(FieldsDict.fluctuationInfluenceDirections, [
          (dataSource?.chart_data?.rule_conclusion?.extra_info?.history_score ??
            0) <= 0
            ? AnalysisDirectionNegative
            : AnalysisDirectionPositive,
        ]);
        form.setFieldValue(
          FieldsDict.mainDistanceAnalysisDim,
          res.data?.main_distance_dims?.map(item => item.dim_id) ??
            dimensionList.slice(0, 1).map(item => item.dim_id),
        );
        form.setFieldValue(FieldsDict.mainDistanceAnalysisPotentialDirections, [
          AnalysisDirectionPositive,
        ]);
      },
      { manual: true },
    );

    React.useEffect(() => {
      if (modal.visible && dataSource) {
        form.resetFields();
        setErrorTip('');
        getGenerateConfig();
      }
    }, [modal.visible, dataSource]);
    return (
      <Modal
        autoFocus
        title={'设置自动分析维度'}
        visible={modal.visible}
        okText={'生成'}
        onCancel={modal.hide}
        wrapStyle={{ zIndex: 1010 }}
        wrapClassName={'drill_modal'}
        maskStyle={{ zIndex: 1005 }}
        onOk={() => {
          tryToGenerate();
        }}>
        <Form layout="horizontal" form={form}>
          {errorTip && (
            <Alert
              type="error"
              style={{ marginBottom: 10 }}
              content={errorTip}
            />
          )}
          <Grid.Row gutter={[0, 24]}>
            <Grid.Col span={24}>
              <StyledFormItem label={'波动影响'}>
                <Grid.Row gutter={[0, 12]}>
                  <Grid.Col span={24}>
                    <StyledFormItem
                      field={FieldsDict.fluctuationAnalysisDim}
                      rules={[
                        {
                          validator(value: any, callback) {
                            if (value?.length > 3) {
                              return callback('波动影响最多可选择三项维度');
                            }
                          },
                        },
                      ]}>
                      <Select
                        allowClear
                        mode="multiple"
                        placeholder={'请选择'}
                        options={dimOptions}
                        dragToSort={true}
                      />
                    </StyledFormItem>
                  </Grid.Col>
                  <Grid.Col span={24}>
                    <StyledFormItem
                      field={FieldsDict.fluctuationInfluenceDirections}>
                      <Select
                        allowClear
                        mode="multiple"
                        placeholder={'请选择'}
                        options={[
                          {
                            label: '按正向贡献度排序',
                            value: AnalysisDirectionPositive,
                          },
                          {
                            label: '按负向贡献度排序',
                            value: AnalysisDirectionNegative,
                          },
                        ]}
                      />
                    </StyledFormItem>
                  </Grid.Col>
                </Grid.Row>
              </StyledFormItem>
            </Grid.Col>
            <Grid.Col span={24}>
              <StyledFormItem label={'主要差距'}>
                <Grid.Row gutter={[0, 12]}>
                  <Grid.Col span={24}>
                    <StyledFormItem
                      field={FieldsDict.mainDistanceAnalysisDim}
                      rules={[
                        {
                          validator(value: any, callback) {
                            if (value?.length > 3) {
                              return callback('主要差距最多可选择三项维度');
                            }
                          },
                        },
                      ]}>
                      <Select
                        allowClear
                        mode="multiple"
                        placeholder={'请选择'}
                        options={dimOptions}
                        dragToSort={true}
                      />
                    </StyledFormItem>
                  </Grid.Col>
                  <Grid.Col span={24}>
                    <StyledFormItem
                      field={FieldsDict.mainDistanceAnalysisPotentialDirections}
                      rules={[
                        {
                          validator(value: any, callback) {
                            if (value?.length > 1) {
                              return callback('只可选择一个排序');
                            }
                          },
                        },
                      ]}>
                      <Select
                        allowClear
                        mode="multiple"
                        placeholder={'请选择'}
                        options={[
                          {
                            label: '按提升潜力正序',
                            value: AnalysisDirectionPositive,
                          },
                          {
                            label: '按提升潜力倒序',
                            value: AnalysisDirectionNegative,
                          },
                        ]}
                      />
                    </StyledFormItem>
                  </Grid.Col>
                </Grid.Row>
              </StyledFormItem>
            </Grid.Col>
          </Grid.Row>
        </Form>
      </Modal>
    );
  },
);

export const AutoGenerator: React.FC<AutoGeneratorProps> = props => {
  const autoGenModal = useModal(AutoGenModal);
  const [loading, setLoading] = useState(false);
  const { editable, getEditor, refreshExpertAnalysis } = props;
  if (!editable) {
    return null;
  }

  return (
    <div className="flex justify-between items-center">
      <Button
        loading={loading}
        type="text"
        onClick={() =>
          autoGenModal.show({ ...props, setParentLoading: setLoading })
        }>
        自动生成
      </Button>
      <Button
        type="text"
        style={{ color: 'var(--measure-text3)' }}
        onClick={() => {
          const editor = getEditor?.();
          editor?.setText('');
          editor?.execCommand(COMMAND_SAVE);
          refreshExpertAnalysis();
        }}>
        清空
      </Button>
    </div>
  );
};
