/* eslint-disable max-len */
/* eslint-disable react/jsx-no-comment-textnodes */
/* eslint-disable react/prop-types */
import { FC, useEffect, useMemo, useState } from 'react';

import { Divider, Popover } from '@arco-design/web-react';
import { useRequest } from '@byted/hooks';
import { useModel } from '@reduck/core';
import dayjs from 'dayjs';
import { MacScrollbar } from 'mac-scrollbar';

import {
  AnalysisDimInfo,
  QueryData,
} from '@quality/common-apis/src/apis/api/base/base_data';
import { CommonDataUnit } from '@quality/common-apis/src/apis/api/base/data_unit';
import {
  insightAscribeServiceClient,
  MainDistanceItem,
} from '@quality/common-apis/src/apis/api/insight/autoinsight';
import { QueryRequest } from '@quality/common-apis/src/apis/api/query/query';
import { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import {
  QUERYCACHE,
  QUERYCACHE_ENABLE,
  dataUnitFormatter,
  QUERYCACHE_DISABLE,
} from '@quality/common-utils';

import { GetSpaceJumpWarp } from '@/components/MetricDetailDrawer/interface';
import { eventBusLoadingModel } from '@/components/MetricDetailDrawer/utils';
import {
  endTimeFormat,
  startTimeFormat,
  TimeFilterValue,
} from '@/components/TimeFilter';
import UserAvatar from '@/components/UserAvatar';
import { API_V1 } from '@/constants';
import { useCacheEvent } from '@/hooks';

import { EAView } from '../EAView';
import { TasksView } from '../TasksView';

import { LongTxt } from './Fluctuation';
import { PotentialSort } from './interface';
import List, { RenderItemProps } from './List';
import { SCBoldText, ContentWrapper } from './style';

interface Props {
  metricData: MetricMetaData | ComplexMetricMetaData | undefined;
  dataSource: QueryData | undefined;
  dataUnit: CommonDataUnit | undefined;
  queryParams: QueryRequest | undefined;
  dimensionId: string;
  potentialSort?: PotentialSort;
  loading: boolean;
  timeFilterInfo: TimeFilterValue;
  cacheEvent?: symbol;
  maxHeight?: number | string;
  getSpaceJumpWarp?: GetSpaceJumpWarp;
  dimensionList: AnalysisDimInfo[];
}

const PrimaryGap: FC<Props> = props => {
  const {
    metricData,
    dataSource,
    dataUnit,
    queryParams,
    timeFilterInfo,
    dimensionId,
    loading,
    cacheEvent,
    potentialSort,
    maxHeight,
    getSpaceJumpWarp,
    dimensionList,
  } = props;
  const [errMsg, setErrMsg] = useState<string | undefined>(undefined);
  const [, action] = useModel(eventBusLoadingModel);

  const dimension = useMemo(() => {
    return metricData?.analysis_info?.analysis_dim_ids?.find(
      ({ dim_id }) => dim_id === dimensionId,
    );
  }, [metricData?.analysis_info?.analysis_dim_ids, dimensionId]);

  const dimensionName = dimension?.display_name ?? '';
  const isEmployeeDim = dimension?.is_employee_dim ?? false;

  const [listData, setListData] = useState<any>();
  const [sorting, setSorting] = useState(false);
  // 基线差距
  const {
    run: fetchGap,
    data: gapResult,
    loading: gapLoading,
  } = useRequest(async (cacheable = true) => {
    if (!dataSource || !queryParams || !dimensionId || !timeFilterInfo) {
      return;
    }
    const { chart_data, valid_baseline = [] } = dataSource;
    if (!chart_data || !valid_baseline.length) {
      return;
    }
    const { series } = chart_data;
    if (!series.length) {
      return;
    }

    const baselineValue =
      series[0].extra_data?.[series[0].extra_data?.length - 1]
        ?.baseline_value ?? '';
    if (baselineValue === '') {
      return;
    }

    const default_value = metricData?.metric_attribute?.default_value ?? '';
    const [startDay, endDay] = timeFilterInfo?.range;
    const { warmup_extra, ...params } = queryParams;
    action.setLoading({ gapLoading: true });
    const res = await insightAscribeServiceClient.MainDistance(
      {
        version: API_V1,
        query_req: params,
        dim_id: dimensionId,
        baseline_value: baselineValue,
        default_value,
        valid_baseline,
        start_time: dayjs(startDay).format(startTimeFormat),
        end_time: dayjs(endDay).format(endTimeFormat),
        metric_direction:
          metricData!.metric_attribute?.analysis_direction ?? '',
        warmup_extra,
        analysis_type: dimensionList?.find(
          item => item.display_name === '人员' && item.dim_id === dimensionId,
        )
          ? 1
          : undefined,
      },
      { [QUERYCACHE]: cacheable ? QUERYCACHE_ENABLE : QUERYCACHE_DISABLE },
    );
    action.setLoading({ gapLoading: false });
    if (res.code !== 200) {
      setErrMsg(res.msg);
    } else {
      setErrMsg(undefined);
    }
    return (res as any)?.data?.[0].items ?? [];
  });
  useEffect(() => {
    fetchGap();
  }, [
    dataSource?.query_id,
    JSON.stringify(queryParams),
    dimensionId,
    timeFilterInfo.range.join(''),
  ]);

  // 触发无缓存请求
  useCacheEvent({
    eventName: cacheEvent,
    callback: () => fetchGap(false),
  });

  const renderListItem = (props: RenderItemProps) => {
    const { data, index } = props;
    const {
      item_name,
      item_name_cn,
      item_value,
      potential_data,
      baseline_data,
      expert_analysis,
      improvement_tasks,
    } = data as MainDistanceItem;
    const { compare_rate, baseline_value, compare_to_baseline } = baseline_data;
    const {
      item_change_rate,
      metric_change_rate,
      protential_value,
      rise_or_fall,
    } = potential_data;
    const percentCompareRatio = `${(
      Math.abs(Number(compare_rate)) * 100
    ).toFixed(2)}%`;
    const realMetricData = dataUnitFormatter(item_value, dataUnit);
    const realBaselineValue = dataUnitFormatter(baseline_value, dataUnit);
    const metricChangeRatePercent = `${(
      Math.abs(Number(metric_change_rate)) * 100
    ).toFixed(2)}%`.replace('.00', '');
    const itemChangePercent = `${(
      Math.abs(Number(item_change_rate)) * 100
    ).toFixed(2)}%`.replace('.00', '');

    const renderJump = getSpaceJumpWarp?.(item_name, dimensionId || '');

    const textDom = <LongTxt>{item_name}</LongTxt>;
    const dimDom = isEmployeeDim ? (
      <UserAvatar username={item_name_cn} size={20} />
    ) : (
      textDom
    );

    const nameDom = renderJump ? renderJump(dimDom) : dimDom;

    const jumpDom = renderJump ? renderJump(textDom) : textDom;

    return (
      <div className="flex items-center">
        <SCBoldText>{index}</SCBoldText>
        <Divider className="mx-3 h-[6px] border-text2" type="vertical" />
        <Popover
          className="no-max-popover"
          content={
            <MacScrollbar suppressScrollX={true} className="max-h-[260px]">
              <div className="flex flex-col gap-3">
                <ContentWrapper title="数据明细">
                  <div className="text-text4">
                    <div className="text-text4">
                      {`Top${index} (提升潜力${protential_value}）`}
                    </div>
                    <div>
                      {jumpDom}
                      {` (${realMetricData}) ${compare_to_baseline}（${realBaselineValue}）${percentCompareRatio}。`}
                    </div>
                    <div>
                      {`若能优化 ${itemChangePercent}，整体将${rise_or_fall} ${metricChangeRatePercent}。`}
                    </div>
                  </div>
                </ContentWrapper>

                {expert_analysis ? (
                  <EAView deltaSetString={expert_analysis} />
                ) : null}

                {improvement_tasks?.unfinished_tasks ? (
                  <ContentWrapper title="待改进任务">
                    <TasksView tasks={improvement_tasks.unfinished_tasks} />
                  </ContentWrapper>
                ) : null}
              </div>
            </MacScrollbar>
          }>
          {nameDom}
          <SCBoldText>{` ${realMetricData}，`}</SCBoldText>
          <SCBoldText className="!text-text3 mx-1">
            {compare_to_baseline}
          </SCBoldText>
          <SCBoldText>{` ${percentCompareRatio}`}</SCBoldText>
        </Popover>
      </div>
    );
  };

  useEffect(() => {
    setSorting(true);
    const sortData =
      gapResult?.sort((a, b) => {
        const compare =
          a.potential_data.protential_value - b.potential_data.protential_value;
        return potentialSort === PotentialSort.Ascending
          ? compare
          : compare * -1;
      }) ?? [];
    setListData(sortData);
    setTimeout(() => {
      setSorting(false);
    }, 100);
  }, [gapResult, potentialSort]);

  return (
    <List
      data={listData}
      loading={loading || gapLoading || sorting}
      dimensionName={dimensionName}
      renderListItem={renderListItem}
      maxHeight={maxHeight}
      errmsg={errMsg}
    />
  );
};

export default PrimaryGap;
