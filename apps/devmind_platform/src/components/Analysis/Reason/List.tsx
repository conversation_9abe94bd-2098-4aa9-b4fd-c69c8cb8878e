import { FC, useRef, ReactNode, useEffect } from 'react';

import { Empty, Skeleton } from '@arco-design/web-react';
import { IconUp, IconDown } from '@arco-design/web-react/icon';
import {
  useBoolean,
  useUpdateLayoutEffect,
  useVirtualList,
  usePersistCallback,
} from '@byted/hooks';
import styled from '@jupiter/plugin-runtime/styled';

import type { MetricSheetSeries } from '@quality/common-apis/src/apis/api/base/base_data';
import type {
  BaseLineDistanceItem,
  MainDistanceItem,
} from '@quality/common-apis/src/apis/api/insight/autoinsight';
import type {
  DimDetail,
  DataCompareInfo,
} from '@quality/common-apis/src/apis/api/query/ascribe';

const SCList = styled.div`
  scrollbar-gutter: stable;
  // overscroll-behavior: contain;

  ::-webkit-scrollbar {
    width: 4px;
  }
  ::-webkit-scrollbar-thumb {
    background-color: #c2c7cc;
  }
  ::-webkit-scrollbar-track {
    background-color: transparent;
  }
`;

export interface RenderItemProps {
  data:
    | DimDetail
    | DataCompareInfo
    | MetricSheetSeries[]
    | BaseLineDistanceItem
    | MainDistanceItem;
  index: number;
}

interface Props {
  loading: boolean;
  dimensionName: string;
  renderListItem: (props: RenderItemProps) => ReactNode;
  data: (
    | DimDetail
    | DataCompareInfo
    | MetricSheetSeries[]
    | BaseLineDistanceItem
  )[];
  maxHeight?: number | string;
  errmsg?: string | undefined;
}

const List: FC<Props> = props => {
  const {
    data,
    dimensionName,
    renderListItem,
    loading,
    errmsg,
    maxHeight = 'auto',
  } = props;

  const collapseButtonRef = useRef<HTMLDivElement>(null); // 折叠按钮dom
  const { state: collapsed, toggle: toggleCollapse } = useBoolean(true); // 是否折叠
  const { state: showButtonShadow, toggle: toggleShadow } = useBoolean(false); // 是否显示阴影
  const handleListScroll = () => {
    const dom = scrollerProps.ref.current;
    if (!dom) {
      return;
    }
    // 判断是否滚到到底部
    // scrollTop 像素根据屏幕dpi不同可能是小数,需要+1计算
    toggleShadow(dom.scrollTop + dom.clientHeight + 1 < dom.scrollHeight);
  };
  // 设置虚拟列表
  const {
    list: virtualList,
    scrollerProps,
    wrapperProps,
    scrollTo,
  } = useVirtualList(
    data ?? [],
    {
      itemHeight: 60,
      overscan: 30,
      onScroll: handleListScroll,
    },
    [JSON.stringify(data)],
  );
  // 判断是否需要加shadow
  useUpdateLayoutEffect(() => {
    const listDom = scrollerProps.ref.current;
    if (!collapseButtonRef.current || !listDom) {
      return;
    }
    // 计算是否滚动到底部
    if (!collapsed && listDom.clientHeight !== listDom.scrollHeight) {
      toggleShadow(true);
    } else {
      showButtonShadow && toggleShadow(false);
    }
  }, [collapsed]);

  const handleRenderListItem = usePersistCallback(renderListItem);

  useEffect(() => {
    toggleShadow(false);
    !collapsed && toggleCollapse(true);
  }, [data]);

  return (
    <SCList {...scrollerProps} style={{ ...scrollerProps.style, maxHeight }}>
      <Skeleton text={{ rows: 4 }} animation={true} loading={loading}>
        {data?.length > 0 ? (
          <>
            <div
              style={{
                ...wrapperProps.style,
                height: collapsed ? 'auto' : wrapperProps.style.height,
              }}>
              {(collapsed ? virtualList.slice(0, 3) : virtualList).map(
                ({ data, index }) => (
                  <div
                    key={index}
                    className="w-full h-[54px] p-4 flex items-center bg-fill2 text-[13px] mb-[6px]">
                    {handleRenderListItem({ data, index: index + 1 })}
                  </div>
                ),
              )}
              {data.length > 3 && (
                <div
                  ref={collapseButtonRef}
                  className="w-full h-[46px] p-4 flex items-center justify-between bg-fill2 sticky bottom-0"
                  style={
                    showButtonShadow
                      ? { boxShadow: '0px -5px 10px -5px #999' }
                      : {}
                  }>
                  <div className="font-medium text-[13px]">{`全部${dimensionName} (共 ${data.length} 个)`}</div>
                  <div
                    className="text-text4 text-[13px] cursor-pointer"
                    onClick={() => {
                      scrollTo(0);
                      toggleCollapse();
                    }}>
                    {!collapsed ? (
                      <>
                        收起
                        <IconUp className="ml-2 text-xs" />
                      </>
                    ) : (
                      <>
                        展开
                        <IconDown className="ml-2 text-xs" />
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </>
        ) : (
          <Empty description={errmsg || '暂无数据'} className="!p-0 mb-4" />
        )}
      </Skeleton>
    </SCList>
  );
};

export default List;
