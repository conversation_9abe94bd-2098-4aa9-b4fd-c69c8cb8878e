/* eslint-disable react/prop-types */
import { FC, useEffect } from 'react';

import { Divider, Popover } from '@arco-design/web-react';
import { useRequest } from '@byted/hooks';
import dayjs from 'dayjs';

import { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { CommonDataUnit } from '@quality/common-apis/src/apis/api/base/data_unit';
import {
  insightAscribeServiceClient,
  BaseLineDistanceItem,
} from '@quality/common-apis/src/apis/api/insight/autoinsight';
import { QueryRequest } from '@quality/common-apis/src/apis/api/query/query';
import { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import {
  QUERYCACHE,
  QUERYCACHE_ENABLE,
  dataUnitFormatter,
  QUERYCACHE_DISABLE,
} from '@quality/common-utils';

import { TimeFilterValue } from '@/components/TimeFilter';
import {
  endTimeFormat,
  startTimeFormat,
} from '@/components/TimeFilter/interface';
import { API_V1 } from '@/constants';
import { useCacheEvent } from '@/hooks';

import { Direction } from './interface';
import List, { RenderItemProps } from './List';
import { SCBoldText } from './style';

interface Props {
  metricData: MetricMetaData | ComplexMetricMetaData | undefined;
  dataSource: QueryData | undefined;
  dataUnit: CommonDataUnit | undefined;
  queryParams: QueryRequest | undefined;
  direction: Direction;
  dimensionId: string;
  timeFilterInfo: TimeFilterValue;
  loading: boolean;
  cacheEvent?: symbol;
  maxHeight?: number | string;
}

const Gap: FC<Props> = props => {
  const {
    metricData,
    dataSource,
    dataUnit,
    queryParams,
    direction,
    dimensionId,
    timeFilterInfo,
    loading,
    cacheEvent,
    maxHeight,
  } = props;

  const {
    display_name: dimensionName = '',
    dim_data_type: dimensionDataType = '',
  } =
    metricData?.analysis_info?.analysis_dim_ids?.find(
      ({ dim_id }) => dim_id === dimensionId,
    ) ?? {};

  // 基线差距
  const {
    run: fetchGap,
    data: gapResult,
    loading: gapLoading,
  } = useRequest(async (cacheable = true) => {
    if (!dataSource || !queryParams || !dimensionId || !timeFilterInfo) {
      return;
    }
    const { chart_data, valid_baseline = [] } = dataSource;
    if (!chart_data || !valid_baseline.length) {
      return;
    }
    const { series } = chart_data;
    if (!series.length) {
      return;
    }

    const [startDay, endDay] = timeFilterInfo.range;

    const currentValue = series[0].data[series[0].data.length - 1];
    const baselineValue =
      series[0].extra_data?.[series[0].extra_data?.length - 1]
        ?.baseline_value ?? '';
    if (baselineValue === '') {
      return;
    }

    const res = await insightAscribeServiceClient.BaseLineDistance(
      {
        version: API_V1,
        data: {
          query: queryParams,
          dim_id: dimensionId,
          data_type: dimensionDataType,
          current_value: currentValue,
          baseline_value: baselineValue,
          valid_baseline,
          start_time: dayjs(startDay).format(startTimeFormat),
          end_time: dayjs(endDay).format(endTimeFormat),
          metric_direction:
            metricData!.metric_attribute?.analysis_direction ?? '',
          warmup_extra: queryParams.warmup_extra,
        },
      },
      { [QUERYCACHE]: cacheable ? QUERYCACHE_ENABLE : QUERYCACHE_DISABLE },
    );
    return (res as any).data ?? [];
  });
  useEffect(() => {
    fetchGap();
  }, [
    dataSource?.query_id,
    JSON.stringify(queryParams),
    dimensionId,
    direction,
  ]);

  // 触发无缓存请求
  useCacheEvent({
    eventName: cacheEvent,
    callback: () => fetchGap(false),
  });

  const renderListItem = (props: RenderItemProps) => {
    const { data, index } = props;
    const {
      item_name,
      item_value,
      baseline_name,
      baseline_value,
      compare_to_baseline,
      compare_rate,
    } = data as BaseLineDistanceItem;

    const percentCompareRatio = `${(
      Math.abs(Number(compare_rate)) * 100
    ).toFixed(2)}%`;
    const realMetricData = dataUnitFormatter(item_value, dataUnit);
    const realBaselineValue = dataUnitFormatter(baseline_value, dataUnit);

    return (
      <div className="flex items-center">
        <SCBoldText>{index}</SCBoldText>
        <Divider className="mx-3 h-[6px] border-text2" type="vertical" />
        <Popover
          className="no-max-popover"
          content={
            <>
              <div className="text-text4">
                {`Top${index} ${item_name} (${realMetricData})`}
              </div>
              <div className="flex justify-between">
                <div className="flex gap-2 items-center">
                  <span className="bg-brand5 rounded-full w-2 h-2" />
                  基线
                </div>
                <div>{baseline_name}</div>
              </div>
              <div className="flex justify-between">
                <div className="flex gap-2 items-center">
                  <span className="bg-brand5 rounded-full w-2 h-2" />
                  基线值
                </div>
                <div>{realBaselineValue}</div>
              </div>
            </>
          }>
          <SCBoldText>{item_name}</SCBoldText>
          <SCBoldText className="!text-text3 mx-1">
            {compare_to_baseline}
          </SCBoldText>
          <SCBoldText>{percentCompareRatio}</SCBoldText>
        </Popover>
      </div>
    );
  };

  return (
    <List
      data={gapResult ?? []}
      loading={loading || gapLoading}
      dimensionName={dimensionName}
      renderListItem={renderListItem}
      maxHeight={maxHeight}
    />
  );
};

export default Gap;
