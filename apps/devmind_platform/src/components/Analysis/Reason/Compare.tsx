/* eslint-disable react/prop-types */
import { FC, useEffect } from 'react';

import { Divider, Popover } from '@arco-design/web-react';
import { useRequest } from '@byted/hooks';
import { takeRight } from 'lodash';

import { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { CommonDataUnit } from '@quality/common-apis/src/apis/api/base/data_unit';
import { insightAscribeServiceClient } from '@quality/common-apis/src/apis/api/insight/autoinsight';
import { DataCompareInfo } from '@quality/common-apis/src/apis/api/query/ascribe';
import { QueryRequest } from '@quality/common-apis/src/apis/api/query/query';
import { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import {
  QUERYCACHE,
  QUERYCACHE_ENABLE,
  dataUnitFormatter,
  QUERYCACHE_DISABLE,
} from '@quality/common-utils';

import { API_V1 } from '@/constants';
import { useCacheEvent } from '@/hooks';

import { LongTxt } from './Fluctuation';
import { Direction } from './interface';
import List, { RenderItemProps } from './List';
import { SCBoldText } from './style';

interface Props {
  metricData: MetricMetaData | ComplexMetricMetaData | undefined;
  dataSource: QueryData | undefined;
  dataUnit: CommonDataUnit | undefined;
  queryParams: QueryRequest | undefined;
  direction: Direction;
  dimensionId: string;
  loading: boolean;
  cacheEvent?: symbol;
  maxHeight?: number | string;
}

const Compare: FC<Props> = props => {
  const {
    metricData,
    dataSource,
    dataUnit,
    queryParams,
    direction,
    dimensionId,
    loading,
    cacheEvent,
    maxHeight,
  } = props;

  const dimensionName =
    metricData?.analysis_info?.analysis_dim_ids?.find(
      ({ dim_id }) => dim_id === dimensionId,
    )?.display_name ?? '';

  // 环比影响
  const {
    run: fetchCompare,
    loading: compareLoading,
    data: compareResult,
  } = useRequest(async (cacheable = true) => {
    if (!dataSource || !queryParams || !dimensionId || !metricData) {
      return;
    }
    const { chart_data } = dataSource;
    if (!chart_data) {
      return;
    }
    const { x_axis } = chart_data;
    if (!x_axis.length) {
      return;
    }
    const [refer_time, target_time] = takeRight(x_axis, 2).map(
      ({ string_value }) => string_value,
    );
    const res = await insightAscribeServiceClient.GetCompareAnalysis(
      {
        version: API_V1,
        query_req: queryParams,
        influence_direction: direction,
        analysis_direction:
          metricData.metric_attribute?.analysis_direction ?? '',
        analysis_dim: dimensionId,
        analysis_time: {
          refer_time,
          target_time,
        },
        default_value: metricData.metric_attribute?.default_value,
        warmup_extra: queryParams.warmup_extra,
      },
      { [QUERYCACHE]: cacheable ? QUERYCACHE_ENABLE : QUERYCACHE_DISABLE },
    );
    return res.data;
  });
  useEffect(() => {
    fetchCompare();
  }, [
    dataSource?.query_id,
    JSON.stringify(queryParams),
    dimensionId,
    direction,
  ]);

  // 触发无缓存请求
  useCacheEvent({
    eventName: cacheEvent,
    callback: () => fetchCompare(false),
  });

  const renderListItem = (props: RenderItemProps) => {
    const { data, index } = props;
    const { dim_item, change_ratio, ref_value, tar_value } =
      data as DataCompareInfo;

    const percentChangeRatio = `${(Math.abs(change_ratio) * 100).toFixed(2)}%`;
    const directionText = `${change_ratio >= 0 ? '上升' : '下降'}`;
    // 计算实际带单位的值
    const realRefValue = dataUnitFormatter(ref_value, dataUnit);
    const realTarValue = dataUnitFormatter(tar_value, dataUnit);

    return (
      <Popover
        className="no-max-popover"
        content={
          <div className="text-text4">
            <div>
              {/* eslint-disable-next-line max-len */}
              {`Top${index} ${dim_item} 环比${directionText} ${percentChangeRatio} (指标值 ${realRefValue} → ${realTarValue})`}
            </div>
          </div>
        }>
        <SCBoldText>{index}</SCBoldText>
        <Divider className="mx-3 h-[6px] border-text2" type="vertical" />
        <LongTxt>{dim_item}</LongTxt>
        <SCBoldText className="!text-text3 mx-1">{`环比${directionText}`}</SCBoldText>
        <SCBoldText>{percentChangeRatio}</SCBoldText>
      </Popover>
    );
  };

  return (
    <List
      data={compareResult ?? []}
      loading={loading || compareLoading}
      dimensionName={dimensionName}
      renderListItem={renderListItem}
      maxHeight={maxHeight}
    />
  );
};

export default Compare;
