import { FC, useState, useMemo, useEffect, useRef } from 'react';

import { Radio, Select, Message, Tooltip } from '@arco-design/web-react';
import { IconQuestionCircle } from '@arco-design/web-react/icon';

import type { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { QueryRequest } from '@quality/common-apis/src/apis/api/query/query';
import { TextStatusType } from '@quality/common-apis/src/apis/rule_engine/rule_text';

import type { DomainMeasureObjFilterValue } from '@/components/DomainMeasureObjFilter';
import type {
  GetSpaceJumpWarp,
  MetricData,
} from '@/components/MetricDetailDrawer/interface';
import { TimeFilterValue } from '@/components/TimeFilter';
import { useSpaceUrlQuery } from '@/hooks/url';

import { ExpertAnalysis } from '../Improvement/ExpertAnalysis';
import { useFetchAnalysis } from '../Improvement/ExpertAnalysis/hooks/useFetchAnalysis';
import {
  AnaylsisRequestParams,
  ExpertAnalysisProps,
  EAHandle,
} from '../Improvement/ExpertAnalysis/interface';

import { AutoGenerator } from './AutoGenerator';
import Fluctuation from './Fluctuation';
import { Direction, PotentialSort } from './interface';
import PrimaryGap from './PrimaryGap';

enum AnalysisType {
  Fluctuation = '波动影响',
  Compare = '环比影响',
  Rank = '指标排名',
  Gap = '基线差距',
  PrimaryGap = '主要差距',
  ExpertAnalysis = '专家分析',
}

interface Props {
  metricData: MetricData | undefined;
  queryParams: QueryRequest | undefined;
  dataSource: QueryData | undefined;
  nodeId: string;
  timeFilterInfo: TimeFilterValue;
  domainMeasureObjFilter?: DomainMeasureObjFilterValue;
  loading?: boolean;
  freezeVersion?: string;
  cacheEvent?: symbol;
  listMaxHeight?: number | string;
  expertProps: ExpertAnalysisProps;
  getSpaceJumpWarp?: GetSpaceJumpWarp;
}

const analysisRadios = [
  AnalysisType.Fluctuation,
  // AnalysisType.Compare,
  // AnalysisType.Rank,
  AnalysisType.PrimaryGap,
  AnalysisType.ExpertAnalysis,
];

const potentialSortOptions = [
  {
    label: '按提升潜力正序',
    value: PotentialSort.Descending,
  },
  {
    label: '按提升潜力倒序',
    value: PotentialSort.Ascending,
  },
];

const analysisTypeTips = {
  [AnalysisType.Fluctuation]: '波动影响：对指标波动贡献最大的Top N个维度值。',
  [AnalysisType.PrimaryGap]: '主要差距：提升潜力最大TopN个维度值。',
};

const Reason: FC<Props> = props => {
  const {
    nodeId,
    metricData,
    dataSource,
    queryParams,
    expertProps,
    timeFilterInfo,
    loading = false,
    cacheEvent,
    listMaxHeight,
    getSpaceJumpWarp,
  } = props;
  const {
    urlQuery: { spaceId },
  } = useSpaceUrlQuery();

  const isPositiveMetric =
    metricData?.metric_attribute?.analysis_direction === 'Positive';
  if (queryParams && spaceId) {
    queryParams.query_info.tag_id = spaceId;
    queryParams.query_info.node_id = nodeId;
  }

  const directionOptions = [
    {
      label: '正向贡献度',
      value: Direction.Positive,
      tip: `正向贡献度：对指标${
        isPositiveMetric ? '上升' : '下降'
      }贡献最大的TopN个。`,
    },
    {
      label: '负向贡献度',
      value: Direction.Negative,
      tip: `负向贡献度：对指标${
        !isPositiveMetric ? '上升' : '下降'
      }贡献最大的TopN个。`,
    },
  ];

  const {
    metric_name,
    metric_url,
    template_id,
    report_name,
    report_url,
    metric_id,
    business_id,
    start_time,
    end_time,
  } = expertProps;

  const baseAnalysisRequest: AnaylsisRequestParams = {
    metric_id,
    business_id,
    start_time,
    end_time,
    metric_name,
    metric_url,
    template_id,
    report_name,
    report_url,
  };
  const expertAnalysisRef = useRef<EAHandle>(null);

  const {
    value: expertValue,
    analysisResponse,
    isFetching,
    refreshExpertAnalysis,
  } = useFetchAnalysis(baseAnalysisRequest);

  const [analysisType, setAnalysisType] = useState<AnalysisType>(
    AnalysisType.Fluctuation,
  );
  const [manualTypeChanged, setManualTypeChanged] = useState(false);
  const tagStatus = dataSource?.chart_data?.rule_conclusion?.tag.status;
  const [dimensionId, setDimensionId] = useState<string>('');
  const [potentialSort, setPotentialSort] = useState<PotentialSort>(
    PotentialSort.Descending,
  );
  const [direction, setDirection] = useState<Direction>(Direction.Negative);

  const dimensionList = metricData?.analysis_info?.analysis_dim_ids ?? [];

  useEffect(() => {
    const newDimensionId = dimensionList[0]?.dim_id ?? '';
    newDimensionId !== dimensionId && setDimensionId(newDimensionId);
  }, [dimensionList]);

  useEffect(() => {
    setDirection(
      tagStatus === TextStatusType.Good
        ? Direction.Positive
        : Direction.Negative,
    );
  }, [dataSource]);

  // 数据单位
  const dataUnit = useMemo(() => {
    return dataSource?.data_unit?.find(
      ({ aliasId }) =>
        aliasId === queryParams?.query_info.select_alias_id_list?.[0]?.alias_id,
    )?.data_unit;
  }, [dataSource, queryParams]);

  const handleAnalysisTypeChange = (type: AnalysisType) => {
    if (
      !dimensionId &&
      type !== AnalysisType.ExpertAnalysis &&
      analysisType !== AnalysisType.ExpertAnalysis
    ) {
      Message.error('请先选择维度');
      return;
    }
    setManualTypeChanged(true);
    setAnalysisType(type);
    if (type === AnalysisType.ExpertAnalysis) {
      refreshExpertAnalysis();
    }
  };
  const handleDirectionChange = (direction: Direction) => {
    setDirection(direction);
  };
  const handleDimensionChange = (id: string) => {
    setDimensionId(id);
  };

  useEffect(() => {
    if (expertValue.get('0').ops[0].insert !== '\n' && !manualTypeChanged) {
      setAnalysisType(AnalysisType.ExpertAnalysis);
    }
  }, [expertValue]);

  return (
    <div className="flex flex-col">
      <div className="text-xs text-text3">🔍 主要原因</div>
      <Radio.Group
        className="mt-3 !flex"
        type="button"
        value={analysisType}
        onChange={handleAnalysisTypeChange}>
        {analysisRadios.map(type => (
          <Radio value={type} key={type} className="">
            <div>
              {type}
              {analysisTypeTips[type] && (
                <Tooltip className="w-[240px]" content={analysisTypeTips[type]}>
                  <IconQuestionCircle className="ml-1 text-text3" />
                </Tooltip>
              )}
            </div>
          </Radio>
        ))}
      </Radio.Group>
      {analysisType !== AnalysisType.ExpertAnalysis && (
        <div className="flex gap-4 my-4">
          {[AnalysisType.Fluctuation, AnalysisType.Compare].includes(
            analysisType,
          ) && (
            <Select
              className="flex-1"
              value={direction}
              options={directionOptions}
              renderFormat={(_, value) => {
                const optionMeta = directionOptions.find(
                  i => i.value === value,
                );
                return (
                  <div>
                    {optionMeta?.label}
                    <Tooltip className="w-[240px]" content={optionMeta?.tip}>
                      <IconQuestionCircle className="ml-1 text-text3" />
                    </Tooltip>
                  </div>
                );
              }}
              onChange={handleDirectionChange}
            />
          )}
          {analysisType === AnalysisType.PrimaryGap && (
            <Select
              className="flex-1"
              value={potentialSort}
              onChange={setPotentialSort}
              options={potentialSortOptions}
            />
          )}

          <Select
            className="flex-1"
            value={dimensionId}
            prefix="维度:"
            style={{ width: 157 }}
            onChange={handleDimensionChange}>
            {dimensionList.map(({ dim_id, display_name }) => (
              <Select.Option key={dim_id} value={dim_id}>
                {display_name}
              </Select.Option>
            ))}
          </Select>
        </div>
      )}
      <div className="flex-1 h-0 min-h-0">
        {(() => {
          switch (analysisType) {
            case AnalysisType.Fluctuation:
              return (
                <Fluctuation
                  getSpaceJumpWarp={getSpaceJumpWarp}
                  metricData={metricData}
                  dataUnit={dataUnit}
                  dataSource={dataSource}
                  queryParams={queryParams}
                  direction={direction}
                  dimensionId={dimensionId}
                  loading={loading}
                  cacheEvent={cacheEvent}
                  maxHeight={listMaxHeight}
                />
              );
            // case AnalysisType.Compare:
            //   return (
            //     <Compare
            //       metricData={metricData}
            //       dataUnit={dataUnit}
            //       dataSource={dataSource}
            //       queryParams={queryParams}
            //       direction={direction}
            //       dimensionId={dimensionId}
            //       loading={loading}
            //       cacheEvent={cacheEvent}
            //       maxHeight={listMaxHeight}
            //     />
            //   );
            case AnalysisType.ExpertAnalysis:
              return (
                <div className="mt-3">
                  <ExpertAnalysis
                    {...expertProps}
                    ref={expertAnalysisRef}
                    analysisResponse={analysisResponse}
                    loading={isFetching}
                    expertValue={expertValue}
                  />
                  <AutoGenerator
                    editable={expertProps.editable}
                    dataUnit={dataUnit}
                    dataSource={dataSource}
                    queryParams={queryParams}
                    dimensionList={dimensionList}
                    metricData={metricData}
                    timeFilterInfo={timeFilterInfo}
                    getEditor={() => expertAnalysisRef.current?.getEditor()}
                    nodeId={nodeId}
                    refreshExpertAnalysis={refreshExpertAnalysis}
                  />
                </div>
              );
            case AnalysisType.PrimaryGap:
              return (
                <PrimaryGap
                  getSpaceJumpWarp={getSpaceJumpWarp}
                  metricData={metricData}
                  dataUnit={dataUnit}
                  potentialSort={potentialSort}
                  dataSource={dataSource}
                  queryParams={queryParams}
                  dimensionId={dimensionId}
                  loading={loading}
                  cacheEvent={cacheEvent}
                  maxHeight={listMaxHeight}
                  timeFilterInfo={timeFilterInfo}
                  dimensionList={dimensionList}
                />
              );
            default:
              return null;
          }
        })()}

        {/* TODO: 后续用 tabs 替换 radio-group */}
        {/* <Tabs
          activeTab={analysisType}
          headerPadding={false}
          overflow="dropdown"
          onChange={handleAnalysisTypeChange}
          renderTabHeader={(props, DefaultTabHeader) => {
            return (
              <>
                <DefaultTabHeader {...props} />
                <div className="flex gap-4 my-4">
                  {[AnalysisType.Fluctuation, AnalysisType.Compare].includes(
                    analysisType,
                  ) && (
                    <Select
                      className="flex-1"
                      value={direction}
                      options={directionOptions}
                      onChange={handleDirectionChange}
                    />
                  )}
                  <Select
                    className="flex-1"
                    value={dimensionId}
                    prefix="维度:"
                    onChange={handleDimensionChange}>
                    {dimensionList.map(({ id, name }) => (
                      <Select.Option key={id} value={id}>
                        {name}
                      </Select.Option>
                    ))}
                  </Select>
                </div>
              </>
            );
          }}>
          <Tabs.TabPane
            key={AnalysisType.Fluctuation}
            title={AnalysisType.Fluctuation}>
            <Fluctuation
              metricData={metricData}
              dataUnit={dataUnit}
              dataSource={dataSource}
              queryParams={queryParams}
              direction={direction}
              dimensionInfo={dimensionInfo}
              loading={loading}
              cacheEvent={cacheEvent}
            />
          </Tabs.TabPane>
          <Tabs.TabPane key={AnalysisType.Compare} title={AnalysisType.Compare}>
            <Compare
              metricData={metricData}
              dataUnit={dataUnit}
              dataSource={dataSource}
              queryParams={queryParams}
              direction={direction}
              dimensionInfo={dimensionInfo}
              loading={loading}
              cacheEvent={cacheEvent}
            />
          </Tabs.TabPane>
          <Tabs.TabPane key={AnalysisType.Rank} title={AnalysisType.Rank}>
            <Rank
              metricData={metricData}
              dataUnit={dataUnit}
              direction={direction}
              dimensionInfo={dimensionInfo}
              businessName={businessName}
              timeFilterInfo={timeFilterInfo}
              loading={loading}
              cacheEvent={cacheEvent}
            />
          </Tabs.TabPane>
          <Tabs.TabPane key={AnalysisType.Gap} title={AnalysisType.Gap}>
            <Gap
              metricData={metricData}
              dataUnit={dataUnit}
              dataSource={dataSource}
              queryParams={queryParams}
              direction={direction}
              dimensionInfo={dimensionInfo}
              timeFilterInfo={timeFilterInfo}
              loading={loading}
              cacheEvent={cacheEvent}
            />
          </Tabs.TabPane>
        </Tabs> */}
      </div>
    </div>
  );
};

export default Reason;
