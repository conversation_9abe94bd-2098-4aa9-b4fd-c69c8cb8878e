/* eslint-disable prefer-template */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
import { Editor } from '@editor-kit/core';
import { ZoneDelta, Op } from '@editor-kit/delta';
import { OL_ID_ATTR_KEY } from '@editor-kit/plugins';

import { AnalysisDimInfo } from '@quality/common-apis/src/apis/api/base/base_data';
import { CommonDataUnit } from '@quality/common-apis/src/apis/api/base/data_unit';
import { ExpertAnalysisData } from '@quality/common-apis/src/apis/api/insight/expert_analysis';
import {
  AnalysisDirectionPositive,
  AnalysisDirectionNegative,
} from '@quality/common-apis/src/apis/api/metric_platform/consts';
import { DimTips } from '@quality/common-apis/src/apis/api/query/ascribe';
import {
  MetricTypeComplexMeta,
  MetricTypeMeta,
} from '@quality/common-apis/src/apis/api/zepto/consts';
import { dataUnitFormatter, uuid } from '@quality/common-utils';

import type { MetricData } from '@/components/MetricDetailDrawer/interface';
import { isMetaMetricData } from '@/components/MetricDetailDrawer/utils';

import { COMMAND_SAVE } from '../Improvement/helper';

const LARK_R600 = '#D83931';
const LARK_G600 = '#2EA121';

type TextToken<T = undefined> = {
  rawText: string;
  isEmpty: boolean;
  variable?: T;
};

// const getLmkrOrderListOp = (prop: {
//   [OL_ID_ATTR_KEY]: string;
//   list?: string;
// }): Op => ({
//   insert: '*',
//   attributes: {
//     ...prop,
//     lmkr: '1',
//   },
// });

export const getCombineParamsFromMetricData = (
  metricData: MetricData | undefined,
) => {
  const story_type = isMetaMetricData(metricData!)
    ? MetricTypeMeta
    : MetricTypeComplexMeta;

  return {
    story_type,
    story_id: metricData ? metricData.id : '',
  };
};

// const genDimensionality = (
//   dimCName: string,
// ): TextToken<{ dimCName: string }> => {
//   const rawText = dimCName
//     ? `${i18n.k2869637328({ dimCName }, '从{dimCName}维度看')}`
//     : '';
//   return {
//     rawText: `${i18n.k2869637328({ dimCName }, '从{dimCName}维度看')}`,
//     isEmpty: !rawText,
//     variable: { dimCName },
//   };
// };

interface IFluctuationGenerator {
  dataUnit: CommonDataUnit | undefined;
}
export class FluctuationGenerator {
  dataUnit: IFluctuationGenerator['dataUnit'];

  constructor({ dataUnit }: IFluctuationGenerator) {
    this.dataUnit = dataUnit;
  }

  genContributionDirection(
    direction: string,
  ): TextToken<{ direction: string }> {
    const rawText =
      direction === AnalysisDirectionPositive
        ? '正向贡献度'
        : direction === AnalysisDirectionNegative
        ? '负向贡献度'
        : '';
    const variable = { direction };

    return {
      rawText,
      isEmpty: !rawText,
      variable,
    };
  }

  genTopN(
    direction: string,
    dimension: string,
  ): TextToken<{ direction: string }> {
    const rawText =
      direction === AnalysisDirectionPositive
        ? `指标变好主要受益于以下「${dimension}」：`
        : direction === AnalysisDirectionNegative
        ? `影响指标变差的「${dimension}」有：`
        : '';

    return {
      rawText,
      isEmpty: !rawText,
      variable: { direction },
    };
  }

  getDirectionTextColor(direction: string) {
    return direction === AnalysisDirectionPositive ? LARK_G600 : LARK_R600;
  }

  genDirectionText(changeRatio: number): TextToken<{ changeRatio: number }> {
    const rawText = `${changeRatio >= 0 ? '上升' : '下降'}`;

    return {
      rawText,
      isEmpty: false,
      variable: {
        changeRatio,
      },
    };
  }

  genPercent(value: number): TextToken<{ value: number }> {
    return {
      rawText: `${(Math.abs(value) * 100).toFixed(2)}%`,
      isEmpty: false,
      variable: {
        value,
      },
    };
  }

  genUnitValue(value: number): TextToken<{ value: number }> {
    const rawText = dataUnitFormatter(value, this.dataUnit);
    return {
      rawText,
      isEmpty: !rawText,
      variable: {
        value,
      },
    };
  }

  getFluctuation(dim_tips: DimTips, dim_item: string[]) {
    const analysis = dim_tips.time_part.map(
      (item, index) =>
        `${item.time_text}，${
          index === 0 ? this.genItemName(dim_item).rawText : ''
        }${this.genDirectionText(item.change_ratio).rawText}${
          this.genPercent(item.change_ratio).rawText
        }${index !== dim_tips.time_part.length - 1 ? '；' : ''}`,
    );
    const value = dim_tips.dim_values.map((item, index) =>
      index === dim_tips.dim_values.length - 1
        ? this.genUnitValue(Number(item)).rawText
        : `${dataUnitFormatter(Number(item), this.dataUnit, false)} → `,
    );
    return `${analysis.join('')}（${value.join('')}）。`;
  }

  genRefToTarText(
    refValue: number,
    tarValue: number,
  ): TextToken<{ refValue: number; tarValue: number }> {
    return {
      rawText: ` (${this.genUnitValue(refValue).rawText} → ${
        this.genUnitValue(tarValue).rawText
      })`,
      isEmpty: false,
      variable: {
        refValue,
        tarValue,
      },
    };
  }

  genItemName(dim_item: string[]): TextToken<{ dim_item: string[] }> {
    const rawText =
      dim_item.length > 1 ? ` ${dim_item[0]} - ${dim_item[1]} ` : dim_item[0];

    return {
      rawText,
      isEmpty: !rawText,
      variable: {
        dim_item,
      },
    };
  }
}

interface IMainDistanceGenerator {
  dataUnit: CommonDataUnit | undefined;
}

export class MainDistanceGenerator {
  dataUnit: CommonDataUnit | undefined;

  constructor({ dataUnit }: IMainDistanceGenerator) {
    this.dataUnit = dataUnit;
  }

  genProtentialSummaryText(
    protentialValue: number,
  ): TextToken<{ protentialValue: number }> {
    const rawText = protentialValue != null ? `提升潜力${protentialValue}` : '';
    return {
      rawText,
      isEmpty: !rawText,
      variable: { protentialValue },
    };
  }

  genUnitValue(value: number | string): TextToken<{ value: number | string }> {
    const rawText = dataUnitFormatter(value, this.dataUnit);
    return {
      rawText,
      isEmpty: !rawText,
      variable: { value },
    };
  }

  genPercent(value: number | string): TextToken<{ value: number | string }> {
    return {
      rawText: `${(Math.abs(Number(value)) * 100).toFixed(2)}%`.replace(
        '.00',
        '',
      ),
      isEmpty: false,
      variable: {
        value,
      },
    };
  }

  genProtentialDescText(
    item_value: number,
    compare_to_baseline: string,
    baseline_value: string,
    compare_rate: number,
  ): TextToken {
    return {
      rawText: ` (${
        this.genUnitValue(item_value).rawText
      }) ${compare_to_baseline} (${
        this.genUnitValue(baseline_value).rawText
      }) ${this.genPercent(compare_rate).rawText}`,
      isEmpty: false,
    };
  }

  genProtentialAdviseText(
    item_change_rate: number,
    rise_or_fall: string,
    metric_change_rate: number,
  ): TextToken {
    return {
      rawText:
        '若能优化 ' +
        this.genPercent(item_change_rate).rawText +
        '，整体将' +
        rise_or_fall +
        ` ${this.genPercent(metric_change_rate).rawText} `,
      isEmpty: false,
    };
  }

  genTopN(
    direction: string,
    dimension: string,
  ): TextToken<{ direction: string }> {
    const rawText =
      direction === AnalysisDirectionPositive
        ? `提升以下「${dimension}」对整体收益最大：`
        : direction === AnalysisDirectionNegative
        ? `提升以下「${dimension}」对整体收益最小：`
        : '';

    return {
      rawText,
      isEmpty: !rawText,
      variable: { direction },
    };
  }

  getDirectionTextColor(direction: string) {
    return direction === AnalysisDirectionPositive ? LARK_R600 : LARK_G600;
  }

  genItemName(
    item_name_list: string[] | undefined = [],
  ): TextToken<{ item_name_list: string[] }> {
    const rawText =
      item_name_list.length > 1
        ? ` ${item_name_list[0]} - ${item_name_list[1]} `
        : item_name_list[0];
    return {
      rawText,
      isEmpty: !rawText,
      variable: {
        item_name_list,
      },
    };
  }
}

type EAOp = {
  op: Op;
  children?: EAOp[];
};

export const generateExpertAnalysis = (
  input: ExpertAnalysisData,
  dimensionList: AnalysisDimInfo[],
  dataUnit: CommonDataUnit | undefined,
  editor?: Editor,
) => {
  const delta = new ZoneDelta({ zoneId: '0' });
  // const opDimensionMap = new Map<string, EAOp[]>();
  const opDimensionList: Array<EAOp> = [];

  const dimMap = Object.fromEntries(
    dimensionList.map(dim => [dim.dim_id, dim.display_name]),
  );
  const ol_id = uuid();

  const fluctuationGenerator = new FluctuationGenerator({ dataUnit });
  const mainDistanceGenerator = new MainDistanceGenerator({ dataUnit });

  input.fluctuation_analysis?.forEach(item => {
    const dimId =
      item.dim_ids.length === 1 ? item.dim_ids[0] : item.combine_dim_id!;

    // if (!opDimensionMap.has(dimId)) {
    //   opDimensionMap.set(dimId, []);
    // }

    const topNToken = fluctuationGenerator.genTopN(
      item.influence_direction,
      dimMap[dimId] ?? '',
    );
    if (item.dim_detail.length) {
      opDimensionList.push({
        op: {
          insert: topNToken.rawText,
          attributes: {
            fontcolor: fluctuationGenerator.getDirectionTextColor(
              topNToken.variable!.direction,
            ),
          },
        },
        children: item.dim_detail.map(detail => {
          const { dim_tips, dim_item } = detail;
          return {
            op: {
              insert: fluctuationGenerator.getFluctuation(dim_tips, dim_item),
            },
          };
        }),
      });
    }
  });

  input.main_distance_data?.forEach(item => {
    const dimId =
      item.dim_ids.length === 1 ? item.dim_ids[0] : item.combine_dim_id!;

    // if (!opDimensionMap.has(dimId)) {
    //   opDimensionMap.set(dimId, []);
    // }

    const topNToken = mainDistanceGenerator.genTopN(
      item.potential_direction,
      dimMap[dimId] ?? '',
    );

    if (item.items.length) {
      opDimensionList.push({
        op: {
          insert: topNToken.rawText,
          attributes: {
            fontcolor: mainDistanceGenerator.getDirectionTextColor(
              topNToken.variable!.direction,
            ),
          },
        },
        children: item.items.map(detail => {
          return {
            op: {
              insert:
                mainDistanceGenerator.genItemName(detail.item_name_list)
                  .rawText +
                mainDistanceGenerator.genProtentialDescText(
                  detail.item_value,
                  detail.baseline_data.compare_to_baseline,
                  detail.baseline_data.baseline_value,
                  detail.baseline_data.compare_rate,
                ).rawText +
                '，' +
                mainDistanceGenerator.genProtentialAdviseText(
                  detail.potential_data.item_change_rate,
                  detail.potential_data.rise_or_fall,
                  detail.potential_data.metric_change_rate,
                ).rawText,
            },
          };
        }),
      });
    }
  });

  // console.log(opDimensionMap);

  // delta.
  // const ops: Op[] = [];

  let j = 1;
  opDimensionList.forEach(eaOp => {
    delta.insertLineMarker({
      [OL_ID_ATTR_KEY]: ol_id,
      list: 'number1',
      start: `${j++}`,
    });
    delta.insert(eaOp.op.insert as string, eaOp.op.attributes);
    delta.insertEnter();

    let k = 1;
    eaOp.children?.forEach(childOp => {
      delta.insertLineMarker({
        [OL_ID_ATTR_KEY]: ol_id,
        list: 'number2',
        start: `${k++}`,
      });
      delta.insert(childOp.op.insert as string);
      delta.insertEnter();
    });
    // ops.push(getLmkrOrderListOp({ 'ol-id': ol_id }));
    // ops.push()
  });

  // console.log(delta);
  editor?.setContent(delta);
  editor?.execCommand(COMMAND_SAVE);
  const data = { '0': delta };
  return JSON.stringify(data);
};
