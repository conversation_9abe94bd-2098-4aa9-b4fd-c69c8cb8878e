/* eslint-disable react/prop-types */
import { FC, useEffect, useCallback } from 'react';

import { Divider, Popover } from '@arco-design/web-react';
import { useModel } from '@jupiter/plugin-runtime/model';

import { MetricSheetSeries } from '@quality/common-apis/src/apis/api/base/base_data';
import { CommonDataUnit } from '@quality/common-apis/src/apis/api/base/data_unit';
import { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import { CHART_TYPE } from '@quality/common-components';
import { dataUnitFormatter } from '@quality/common-utils';

import type { DomainMeasureObjFilterValue } from '@/components/DomainMeasureObjFilter';
import { useQueryRequest } from '@/components/MetricDetailDrawer/hooks';
import metricDetailDrawerModel from '@/components/MetricDetailDrawer/model';
import { isMetaMetricData } from '@/components/MetricDetailDrawer/utils';
import type { TimeFilterValue } from '@/components/TimeFilter';
import { useCacheEvent } from '@/hooks';

import { Direction } from './interface';
import List, { RenderItemProps } from './List';
import { SCBoldText } from './style';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';

interface Props {
  metricData: MetricMetaData | ComplexMetricMetaData | undefined;
  dataUnit: CommonDataUnit | undefined;
  direction: Direction;
  dimensionId: string;
  nodeId: string;
  timeFilterInfo: TimeFilterValue;
  domainMeasureObjFilter?: DomainMeasureObjFilterValue;
  loading: boolean;
  cacheEvent?: symbol;
  maxHeight?: number | string;
}

const Rank: FC<Props> = props => {
  const {
    metricData,
    dataUnit,
    direction,
    dimensionId,
    nodeId,
    timeFilterInfo,
    domainMeasureObjFilter = { measureObjId: '', filter: [] },
    loading,
    cacheEvent,
    maxHeight,
  } = props;

  const { virtualItem } = useVirtualSpaceType()
  const spaceId = virtualItem?.SpaceId ?? ''

  const dimensionName =
    metricData?.analysis_info?.analysis_dim_ids?.find(
      ({ dim_id }) => dim_id === dimensionId,
    )?.display_name ?? '';

  const [{ freezeVersion }] = useModel([
    metricDetailDrawerModel,
    ({ config: { freezeVersion } }) => ({ freezeVersion }),
  ]);

  // 指标排名
  const {
    loading: rankLoading,
    run: rankQuery,
    data: rankResult,
  } = useQueryRequest<CHART_TYPE.SHEET>(metricData, {
    freezeVersion,
  });

  const handleFetch = useCallback(
    (cacheable = true) => {
      if (!dimensionId || !metricData || !nodeId) {
        return;
      }
      rankQuery(
        {
          chartType: CHART_TYPE.SHEET,
          nodeId,
          timeFilterInfo,
          measureDimensionId: dimensionId,
          domainMeasureObjFilter,
          customParams: {
            query_info: {
              order_list: [
                {
                  alias_id: isMetaMetricData(metricData)
                    ? metricData.id
                    : metricData.complex_metric_list[0].id,
                  order:
                    metricData.metric_attribute?.analysis_direction ===
                    Direction.Positive
                      ? 'desc'
                      : 'asc', // 指标排名 正向从大到小，负向从小到大，和对比分析有区别
                },
              ],
            },
          },
          spaceId
        },
        cacheable,
      );
    },
    [
      dimensionId,
      direction,
      timeFilterInfo.range.join(''),
      nodeId,
      rankQuery,
      freezeVersion,
      spaceId
    ],
  );
  useEffect(() => {
    handleFetch();
  }, [handleFetch]);
  // 触发无缓存请求
  useCacheEvent({
    eventName: cacheEvent,
    callback: () => handleFetch(false),
  });

  const renderListItem = (props: RenderItemProps) => {
    const { data, index } = props;

    const [{ value }, { value: metricData }] = data as MetricSheetSeries[];
    const realMetricData = dataUnitFormatter(metricData, dataUnit);

    return (
      <div className="flex items-center">
        <SCBoldText>{index}</SCBoldText>
        <Divider className="mx-3 h-[6px] border-text2" type="vertical" />
        <Popover
          className="no-max-popover"
          content={
            <div className="text-text4">{`Top${index} ${value} (${realMetricData})`}</div>
          }>
          <SCBoldText>{value}</SCBoldText>
        </Popover>
      </div>
    );
  };

  return (
    <List
      data={rankResult?.dataSource?.metric_sheet_data?.series ?? []}
      loading={loading || rankLoading}
      dimensionName={dimensionName}
      renderListItem={renderListItem}
      maxHeight={maxHeight}
    />
  );
};

export default Rank;
