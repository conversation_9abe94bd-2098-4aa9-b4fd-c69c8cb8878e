/* eslint-disable react/prop-types */
import { FC, useEffect, useMemo, useState } from 'react';

import { Divider, Popover } from '@arco-design/web-react';
import { useRequest } from '@byted/hooks';
import { useModel } from '@reduck/core';
import { takeRight, cloneDeep } from 'lodash';
import { MacScrollbar } from 'mac-scrollbar';
import styled from 'styled-components';

import { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { CommonDataUnit } from '@quality/common-apis/src/apis/api/base/data_unit';
import { insightAscribeServiceClient } from '@quality/common-apis/src/apis/api/insight/autoinsight';
import { DimDetail } from '@quality/common-apis/src/apis/api/query/ascribe';
import { QueryRequest } from '@quality/common-apis/src/apis/api/query/query';
import { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import {
  QUERYCACHE,
  QUERYCACHE_ENABLE,
  dataUnitFormatter,
  QUERYCACHE_DISABLE,
} from '@quality/common-utils';

import LineThumbnail from '@/components/LineThumbnail';
import { GetSpaceJumpWarp } from '@/components/MetricDetailDrawer/interface';
import {
  eventBusLoadingModel,
  isMetaMetricData,
} from '@/components/MetricDetailDrawer/utils';
import UserAvatar from '@/components/UserAvatar';
import { API_V1 } from '@/constants';
import { useCacheEvent } from '@/hooks';

import { EAView } from '../EAView';
import { TasksView } from '../TasksView';

import { FluctuationGenerator } from './helper';
import { Direction } from './interface';
import List, { RenderItemProps } from './List';
import { SCBoldText, ContentWrapper } from './style';

interface Props {
  metricData: MetricMetaData | ComplexMetricMetaData | undefined;
  dataSource: QueryData | undefined;
  dataUnit: CommonDataUnit | undefined;
  queryParams: QueryRequest | undefined;
  direction: Direction;
  dimensionId: string;
  loading: boolean;
  cacheEvent?: symbol;
  maxHeight?: number | string;
  getSpaceJumpWarp?: GetSpaceJumpWarp;
}

export interface DimDetailWithNodeInfo extends DimDetail {
  style?: React.CSSProperties;
  lineData?: QueryData;
}

const LongTxtWrap = styled.span`
  font-weight: 500;
  cursor: pointer;
  :hover {
    color: #165dff;
  }
`;

export const LongTxt = ({
  maxlen = 30,
  children,
}: {
  maxlen?: number;
  children?: any;
}) => {
  let fullv = children;
  // fullv =
  // '的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的的';
  if (fullv?.length <= maxlen) {
    return <span className="font-bold">{fullv}</span>;
  }

  const value = fullv.length > maxlen ? `${fullv.slice(0, maxlen - 1)}` : fullv;

  return (
    <Popover content={fullv}>
      <LongTxtWrap>
        {value}
        {<span className="more">{' ... '}</span>}
      </LongTxtWrap>
    </Popover>
  );
};

const Fluctuation: FC<Props> = props => {
  const {
    metricData,
    dataSource,
    dataUnit,
    queryParams,
    direction,
    dimensionId,
    loading,
    cacheEvent,
    maxHeight,
    getSpaceJumpWarp,
  } = props;
  const [errMsg, setErrMsg] = useState<string | undefined>(undefined);
  const [, action] = useModel(eventBusLoadingModel);

  const dimension = useMemo(() => {
    return metricData?.analysis_info?.analysis_dim_ids?.find(
      ({ dim_id }) => dim_id === dimensionId,
    );
  }, [metricData?.analysis_info?.analysis_dim_ids, dimensionId]);

  const dimensionName = dimension?.display_name ?? '';

  const isEmployeeDim = dimension?.is_employee_dim ?? false;

  // 波动分析
  const {
    run: fetchFluctuantion,
    loading: fluctuationLoading,
    data: fluctuationResult,
  } = useRequest(async (cacheable = true) => {
    if (!dataSource || !queryParams || !dimensionId || !metricData) {
      return;
    }
    // 判断是否支持波动分析
    const isChangeDimascribe = isMetaMetricData(metricData)
      ? metricData.is_change_dimascribe
      : metricData.complex_metric_list[0].is_change_dimascribe;
    if (!isChangeDimascribe) {
      return [];
    }

    const { chart_data } = dataSource;
    if (!chart_data) {
      return;
    }
    const { x_axis } = chart_data;
    if (!x_axis.length) {
      return;
    }
    const [refer_time, target_time] = takeRight(x_axis, 2).map(
      ({ string_value }) => string_value,
    );
    const { warmup_extra, ...params } = queryParams;
    action.setLoading({ fluctuationLoading: true });
    const res = await insightAscribeServiceClient.GetFluctuationAnalysis(
      {
        version: API_V1,
        query_req: { ...params, baselines: [] }, // 波动分析不需要基线相关信息
        dim_radix: 1,
        influence_direction: direction,
        analysis_direction:
          metricData.metric_attribute?.analysis_direction ?? '',
        analysis_dims: [dimensionId],
        analysis_time: {
          refer_time,
          target_time,
        },
        default_value: metricData.metric_attribute?.default_value,
        warmup_extra,
        rule_extra_info: dataSource.chart_data?.rule_conclusion?.extra_info,
      },
      { [QUERYCACHE]: cacheable ? QUERYCACHE_ENABLE : QUERYCACHE_DISABLE },
    );
    action.setLoading({ fluctuationLoading: false });
    if (res.code !== 200) {
      setErrMsg(res.msg);
    } else {
      setErrMsg(undefined);
    }
    return formatData(res.data?.[0]?.dim_detail ?? []);
  });
  useEffect(() => {
    fetchFluctuantion();
  }, [dataSource?.query_id, dimensionId, direction]);

  // 触发无缓存请求
  useCacheEvent({
    eventName: cacheEvent,
    callback: () => fetchFluctuantion(false),
  });

  const fluctuationGenerator = useMemo(
    () => new FluctuationGenerator({ dataUnit }),
    [dataUnit],
  );

  const formatData = (data: DimDetail[]): DimDetailWithNodeInfo[] => {
    return data.map(item => {
      // 是用dataSource来构造dim_values数据的缩略图
      const lineData = cloneDeep(dataSource);
      if (lineData?.chart_data) {
        lineData.chart_data.x_axis = new Array(item.dim_tips.dim_values.length)
          .fill(0)
          .map((_item, index) => ({
            display_value: String(index),
            string_value: String(index),
            int_value: '0',
            float_value: 0,
          }));
        lineData.chart_data.series[0].data = item.dim_tips.dim_values;
        lineData.chart_data.series[0].extra_data = item.dim_tips.dim_values.map(
          item => ({
            baseline_value: item,
            rolling_avg: '',
          }),
        );
      }

      return {
        ...item,
        lineData,
      };
    });
  };

  const renderListItem = (props: RenderItemProps) => {
    const { data, index } = props;

    const {
      dim_item,
      change_ratio,
      contribution,
      expert_analysis,
      improvement_tasks,
      dim_item_cn,
      dim_tips,
      lineData,
    } = data as DimDetailWithNodeInfo;

    const percentContribution = `${(Math.abs(contribution) * 100).toFixed(2)}%`;
    const contributionDirection = `${
      direction === Direction.Positive ? '正' : '负'
    }向贡献度`;
    const directionText = `${change_ratio >= 0 ? '上升' : '下降'}`;
    const percentChangeRatio = `${(Math.abs(change_ratio) * 100).toFixed(2)}%`;

    // 计算实际带单位的值
    // const realRefValue = dataUnitFormatter(ref_value, dataUnit);
    // const realTarValue = dataUnitFormatter(tar_value, dataUnit);

    // 是用dataSource来构造dim_values数据的缩略图

    const renderJump = getSpaceJumpWarp?.(dim_item[0], dimensionId || '');

    const textDom = <LongTxt>{dim_item[0]}</LongTxt>;
    const dimDom = isEmployeeDim ? (
      <UserAvatar
        username={dim_item_cn ? dim_item_cn[0] : dim_item[0]}
        size={20}
      />
    ) : (
      textDom
    );

    const nameDom = renderJump ? renderJump(dimDom) : dimDom;
    const dimName = fluctuationGenerator.genItemName(dim_item).rawText;

    const popoverNameDom = renderJump ? renderJump(dimName) : dimName;
    return (
      <div className="flex items-center">
        <SCBoldText>{index}</SCBoldText>
        <Divider className="mx-3 h-[6px] border-text2" type="vertical" />
        <Popover
          className="no-max-popover"
          content={
            <MacScrollbar suppressScrollX={true} className="max-h-[260px]">
              <div className="flex flex-col gap-3">
                <ContentWrapper title={'波动影响'}>
                  <div className="flex">
                    <div style={{ width: '220px', fontSize: '14px' }}>
                      <div>
                        {`Top${index} （${contributionDirection} ${percentContribution}），`}
                      </div>
                      <div>
                        {dim_tips?.time_part?.map((item, index) => (
                          <span key={String(index)}>
                            {`${item.time_text}，`}
                            {index === 0 && popoverNameDom}
                            {
                              fluctuationGenerator.genDirectionText(
                                item.change_ratio,
                              ).rawText
                            }
                            {`${
                              fluctuationGenerator.genPercent(item.change_ratio)
                                .rawText
                            }`}
                            {index !== dim_tips.time_part.length - 1 && '；'}
                          </span>
                        ))}

                        <span>
                          {`（${dim_tips?.dim_values
                            ?.map((item, index) =>
                              index === dim_tips.dim_values.length - 1
                                ? fluctuationGenerator.genUnitValue(
                                    Number(item),
                                  ).rawText
                                : `${dataUnitFormatter(
                                    Number(item),
                                    dataUnit,
                                    false,
                                  )} → `,
                            )
                            .join('')}）。`}
                        </span>
                      </div>
                    </div>
                    <div
                      id="chartContainer"
                      style={{ width: '100px', margin: 'auto' }}>
                      <LineThumbnail
                        dataSource={lineData}
                        queryInfo={queryParams?.query_info}
                        displayConfig={queryParams?.display_config}
                        width={100}
                        showTooltips={false}
                      />
                    </div>
                  </div>
                </ContentWrapper>

                {expert_analysis ? (
                  <EAView deltaSetString={expert_analysis} />
                ) : null}

                {improvement_tasks?.unfinished_tasks ? (
                  <ContentWrapper title="待改进任务">
                    <TasksView tasks={improvement_tasks.unfinished_tasks} />
                  </ContentWrapper>
                ) : null}
              </div>
            </MacScrollbar>
          }>
          {nameDom}
          <SCBoldText className="!text-text3 mx-1">{directionText}</SCBoldText>
          <SCBoldText>{percentChangeRatio}</SCBoldText>
        </Popover>
      </div>
    );
  };

  return (
    <List
      data={fluctuationResult ?? []}
      loading={loading || fluctuationLoading}
      dimensionName={dimensionName}
      renderListItem={renderListItem}
      maxHeight={maxHeight}
      errmsg={errMsg}
    />
  );
};

export default Fluctuation;
