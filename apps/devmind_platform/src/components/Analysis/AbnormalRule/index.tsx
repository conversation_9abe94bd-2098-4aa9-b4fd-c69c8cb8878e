import * as React from 'react';

import { Empty, Skeleton, Spin } from '@arco-design/web-react';
import { useRequest } from 'ahooks';

import {
  queryServiceClient,
  type QueryRequest,
} from '@quality/common-apis/src/apis/api/query/query';
import type { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import type { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import {
  QUERYCACHE,
  QUERYCACHE_ENABLE,
  QUERYCACHE_DISABLE,
} from '@quality/common-utils';

import { REASON_CACHE_EVENT } from '@/components/MetricDetailDrawer/components/Analysis';
import {
  TimeGranularityText,
  type TimeFilterValue,
} from '@/components/TimeFilter/interface';
import { API_V1 } from '@/constants';
import { useCacheEvent } from '@/hooks';

interface ExceptionRuleProps {
  metricData: MetricMetaData | ComplexMetricMetaData | undefined;
  queryParams: QueryRequest | undefined;
  timeFilterInfo: TimeFilterValue;
}
export const AbnormalRule: React.FC<ExceptionRuleProps> = ({
  metricData,
  queryParams,
  timeFilterInfo,
}) => {
  const { loading, data, run } = useRequest(
    async (cacheable = true) => {
      if (!queryParams) {
        return;
      }
      const res = await queryServiceClient.GetAbnormalAlert(
        {
          version: API_V1,
          query_req: queryParams!,
          metric_id: metricData?.id || '',
          granularity_type: TimeGranularityText[timeFilterInfo.granularity],
        },
        {
          [QUERYCACHE]: cacheable ? QUERYCACHE_ENABLE : QUERYCACHE_DISABLE,
        },
      );
      return res.data.abnormalAlerts;
    },
    { refreshDeps: [queryParams, timeFilterInfo.granularity] },
  );

  // 触发无缓存请求
  useCacheEvent({
    eventName: REASON_CACHE_EVENT,
    callback: () => run(false),
  });

  return (
    <>
      <div className="flex flex-col">
        <div className="flex justify-between">
          <div className="text-xs text-text3" style={{ lineHeight: '26px' }}>
            {'🔴 异常提醒'}
          </div>
        </div>
        <div style={{ maxHeight: '200px', overflow: 'auto' }}>
          <div>
            <Spin loading={loading} style={{ width: '100%' }}>
              <Skeleton
                loading={loading}
                text={{
                  rows: 2,
                  style: { lineHeight: '20', fontSize: '13px' },
                }}
                style={{ height: '200px', width: '330px' }}>
                {data && data?.length > 0 ? (
                  <ul className="flex flex-col">
                    {data?.map((rule, index: number) => (
                      <li key={index} className="flex items-start">
                        <span
                          className="conclusion--text"
                          style={{
                            fontWeight: 400,
                            fontSize: '13px',
                            lineHeight: '20px',
                            marginLeft: '8px',
                          }}>
                          {rule}
                        </span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <Empty
                    description="暂无数据"
                    className="flex items-center h-full"
                  />
                )}
              </Skeleton>
            </Spin>
          </div>
        </div>
      </div>
    </>
  );
};
