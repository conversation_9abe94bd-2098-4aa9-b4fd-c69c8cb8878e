import { FC } from 'react';

import { Popover, Divider, Tag } from '@arco-design/web-react';
import { IconQuestionCircle } from '@arco-design/web-react/icon';
import styled from '@jupiter/plugin-runtime/styled';
import { useModel } from '@reduck/core';
import cs from 'classnames';
import { marked } from 'marked';

import {
  MetricInfo,
  MetricAttribute,
} from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import { Markdown } from '@quality/common-components/src/Form/Markdown';

import UserAvatar from '@/components/UserAvatar';
import { MetricUseText, MetricCommonLevelText } from '@/constants';
import globalModel from '@/model/global';
import { reportModel } from '@/model/report';

// 避免受外部样式影响
const SCPopover = styled.div`
  font-weight: normal;
  max-height: 550px;
  min-width: 260px;
  overflow: auto;
  white-space: normal;
  padding: 12px 12px 12px 16px;
  b {
    font-weight: bold;
  }
`;

interface Props {
  name: string;
  owners: string[];
  description: string;
  metricInfo?: MetricInfo;
  metricAttribute?: MetricAttribute;
  className?: string;
  ellipsis?: boolean;
  iconSize?: number;
  inMetricCard?: boolean;
}

const renderer = new marked.Renderer();
renderer.link = function (href, title, text) {
  const link = marked.Renderer.prototype.link.apply(this, [href, title, text]);
  return link.replace('<a', "<a target='_blank'");
};
marked.setOptions({ renderer });

const MetricName: FC<Props> = props => {
  const {
    className,
    name,
    owners,
    description,
    metricInfo,
    metricAttribute,
    ellipsis = false,
    iconSize,
    inMetricCard,
  } = props;
  const { calc_caliber, drag_direction } = metricInfo ?? {};
  const { metric_use = '', attr = '', level = '' } = metricAttribute ?? {};
  const [{ configEnumInfo }] = useModel([globalModel]);
  const [{ fullScreen }] = useModel([reportModel]);

  const metricTypeArr = [
    MetricCommonLevelText[level] || level,
    MetricUseText[metric_use],
    configEnumInfo.attrMaps?.find(item => item.code === attr)?.display_name,
  ].filter(Boolean);

  return (
    <div className={cs('flex items-center', className)}>
      <span
        style={{
          marginRight: '4px',
          ...(fullScreen && !inMetricCard ? { lineHeight: '32px' } : {}),
        }}
        className={cs({
          truncate: ellipsis,
          'text-[#66FFF6]': fullScreen && !inMetricCard,
          'text-[#9DA3AF]': fullScreen && inMetricCard,
          'text-[24px]': fullScreen && !inMetricCard,
        })}>
        {name}
      </span>
      <Popover
        position="rt"
        className="no-max-popover"
        triggerProps={{ popupStyle: { padding: 0 }, updateOnScroll: true }}
        content={
          <SCPopover
            className="cursor-default thin-scrollbar scroll-stable"
            onClick={e => e.stopPropagation()}>
            <div className="mb-4 font-medium">{name}</div>
            <div className="mb-[6px]">负责人:</div>
            <UserAvatar dataList={owners} />
            <Divider className="!my-3" />
            <div className="mb-[6px]">指标类型:</div>
            <div className="flex gap-2">
              {metricTypeArr.map(text => (
                <Tag key={text} color="arcoblue" className="!rounded-[32px]">
                  {text}
                </Tag>
              ))}
            </div>
            {description && (
              <>
                <Divider className="my-3" />
                <div className="mb-[6px]">指标说明:</div>
                <Markdown value={description} disabled={true} />
              </>
            )}
            {calc_caliber && (
              <>
                <Divider className="my-3" />
                <div className="mb-[6px]">计算口径:</div>
                <Markdown value={calc_caliber} disabled={true} />
              </>
            )}
            {drag_direction && (
              <>
                <Divider className="my-3" />
                <div className="mb-[6px]">牵引方向:</div>
                <Markdown value={drag_direction} disabled={true} />
              </>
            )}
          </SCPopover>
        }>
        <IconQuestionCircle
          className={cs(['flex-shrink-0', fullScreen && 'hidden'])}
          style={iconSize ? { fontSize: iconSize } : {}}
        />
      </Popover>
    </div>
  );
};

export default MetricName;
