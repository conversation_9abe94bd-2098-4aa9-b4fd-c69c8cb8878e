import { cloneElement, FC, ReactElement, ReactNode, useMemo } from 'react';

import { Modal } from '@arco-design/web-react';
import { create, useModal } from '@ebay/nice-modal-react';
import { useModel } from '@reduck/core';
import clsx from 'clsx';
import styled from 'styled-components';

import { reportModel } from '@/model/report';

export interface LargeViewModalProps {
  title: ReactNode;
  content: ReactElement;
}

const ScaleButton = styled.div`
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0px 0px 0px 1px #dde2e9, 0px 2px 1px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  cursor: pointer;
`;

export const LargeViewModal = create<LargeViewModalProps>(props => {
  const { title, content } = props;

  const modal = useModal();
  const [{ fullScreen }] = useModel([reportModel]);

  const largeContent = useMemo(() => {
    return modal.visible
      ? cloneElement(content, {
          style: { width: '100%', height: '100%' },
        })
      : null;
  }, [modal.visible]);

  return (
    <Modal
      visible={modal.visible}
      style={{ width: '80vw' }}
      className={clsx(fullScreen && 'fullScreenModal')}
      title={
        <div className="flex items-center justify-between">
          <div>{title}</div>
          <ScaleButton onClick={modal.hide}>
            <iconpark-icon name="modal-fold" size="12px" />
          </ScaleButton>
        </div>
      }
      footer={null}
      closeIcon={null}
      maskClosable
      onCancel={modal.hide}>
      <div style={{ height: '80vh' }}>{largeContent}</div>
    </Modal>
  );
});

export const ExpandButton: FC = () => (
  <ScaleButton>
    <iconpark-icon class="expandButton" size="12px" name="modal-expand" />
  </ScaleButton>
);
