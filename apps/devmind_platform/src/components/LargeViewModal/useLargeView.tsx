import { useEffect } from 'react';

import { useModal } from '@ebay/nice-modal-react';
import { useModel } from '@reduck/core';

import { reportModel } from '@/model/report';

import {
  LargeViewModal,
  ExpandButton,
  LargeViewModalProps,
} from './LargeViewModal';

export const useLargeView = (options: LargeViewModalProps) => {
  const modal = useModal(LargeViewModal);
  const [{}, { setFullScreen }] = useModel(reportModel);
  const toggle = () => {
    modal.visible ? modal.hide() : modal.show(options);
  };

  const open = () => {
    modal.show(options);
  };
  const handleFullscreenChange = () => {
    if (document.fullscreenElement) {
      setFullScreen(true);
      // 设置为暗黑主题
      document.body.setAttribute('arco-theme', 'dark');
    } else {
      // 恢复亮色主题
      document.body.removeAttribute('arco-theme');
      setFullScreen(false);
    }
  };
  const close = modal.hide;
  useEffect(() => {
    // 监听退出全屏时，弹窗关闭
    document.addEventListener(
      'fullscreenchange',
      function () {
        handleFullscreenChange();
        if (!document.fullscreenElement) {
          close();
        }
      },
      true,
    );
    return () => {
      document.removeEventListener(
        'fullscreenchange',
        function () {
          handleFullscreenChange();
          if (!document.fullscreenElement) {
            close();
          }
        },
        true,
      );
    };
  }, []);

  return {
    expandButton: (
      <div
        onClick={e => {
          e.stopPropagation();
          open();
        }}>
        <ExpandButton />
      </div>
    ),
    toggle,
    open,
    close,
  };
};
