import { FC, useMemo, useState, useRef, useEffect } from 'react';

import {
  Popconfirm,
  Select,
  Space,
  Spin,
  Message,
  Empty,
  Button,
  Tooltip,
} from '@arco-design/web-react';
import { useRequest, useDebounceFn } from 'ahooks';
import styled from 'styled-components';

import { MeasureObject } from '@quality/common-apis/src/apis/api/base/base_data';
import { reportTemplateServiceClient } from '@quality/common-apis/src/apis/api/insight_report/report';
import { FilterItem } from '@quality/common-apis/src/apis/api/insight_report/report_model';

import { API_V1 } from '@/constants';
import { NodeManager } from '@/modules/node';

const { Option } = Select;

const LoadTitle = styled.div`
  font-size: 12px;
  color: #86909c;
  .arco-spin-icon {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
`;

export interface DomainMeasureObjFilterValue {
  measureObjId: string | undefined;
  filter: string[];
}

interface Props {
  value: DomainMeasureObjFilterValue;
  nodeId: string;
  timeRange: string[];
  domainMeasureObjList: { id: string; name: string }[];
  metricInfoList: {
    metricId: string;
    metricType: string;
    domainMeasureObjs: MeasureObject[];
    modelId: string;
  }[];
  getPopupContainer?: (node: HTMLElement) => Element;
  onChange?: (value: DomainMeasureObjFilterValue) => void;
  disabled?: boolean;
}

const DomainMeasureObjFilter: FC<Props> = props => {
  const {
    value,
    domainMeasureObjList,
    metricInfoList,
    nodeId,
    timeRange: [start_time, end_time],
    getPopupContainer,
    onChange,
    disabled = false,
  } = props;

  const [measureObjId, setMeasureObjId] = useState<string | undefined>(
    value.measureObjId,
  );
  const [filter, setFilter] = useState<string[]>(value.filter ?? []);
  const refCanTriggerLoadMore = useRef(true);
  const [fetching, setFetching] = useState(false);
  const [page, setPage] = useState(1);
  const [haveData, sethaveData] = useState(true);
  const [searchKey, setSearchKey] = useState('');

  const measureObjName =
    domainMeasureObjList.find(({ id }) => id === value.measureObjId)?.name ??
    '';

  const handleMeasureObjChange = (id: string) => {
    setSearchKey('');
    mutate([]);
    sethaveData(true);
    setMeasureObjId(id);
    setFilter([]);
  };

  const handleOk = () => {
    // 没有筛选具体值，则清空度量对象选择
    const newMeasureObjId = filter.length ? measureObjId : '';
    onChange?.({
      measureObjId: newMeasureObjId,
      filter,
    });
  };

  const handleVisibleChange = (visible: boolean) => {
    if (visible) {
      value.filter.sort().join('') !== filter.sort().join('') &&
        setFilter(value.filter);
      value.measureObjId !== measureObjId &&
        setMeasureObjId(value.measureObjId);

      value.filter?.length && !data?.length && mutate(value.filter);
    }
  };

  // 等各个指标详情有数据了
  const isReady = metricInfoList.every(({ modelId }) => Boolean(modelId));

  const { loading, data, mutate } = useRequest(
    async () => {
      if (!measureObjId) {
        Message.error('请先选择领域度量对象');
        return [];
      }

      const filterMetricInfoList = metricInfoList.reduce<FilterItem[]>(
        (prev, { metricId, metricType, domainMeasureObjs, modelId }) => {
          // 筛选出配置了对应领域度量对象的指标卡信息
          const dim_id = domainMeasureObjs.find(
            ({ id }) => id === measureObjId,
          )?.dimension_id;

          if (dim_id) {
            prev.push({
              metric_id: metricId,
              metric_type: metricType,
              dim_id,
              model_id: modelId,
            });
          }
          return prev;
        },
        [],
      );

      if (!filterMetricInfoList.length) {
        Message.error(
          `不存在配置 ${
            domainMeasureObjList.find(({ id }) => id === measureObjId)?.name ??
            ''
          } 领域度量对象的指标`,
        );
        return;
      }

      setFetching(true);
      const spaceType = NodeManager.getModule()?.getSpaceType(nodeId);
      const resp = await reportTemplateServiceClient.GetFilterCandidateValue(
        {
          version: API_V1,
          data: {
            value_like: searchKey,
            limit: 50,
            items: filterMetricInfoList,
            node_id: nodeId,
            space_type: spaceType!,
            time_range: { start_time, end_time },
            obj_id: measureObjId,
            page,
          },
        },
        { hideNotice: true },
      );
      setFetching(false);
      let res;
      if (resp.code === 200) {
        // if (measureObjId === MEASURE_OBJ_ID_PEOPLE) {
        //   // 人员的时候 返回结果 data 是两个对象 CN / EN
        //   let cns = (resp.data as { CN: string[] }).CN;
        //   let ens = (resp.data as { EN: string[] }).EN;
        //   res = cns.map((item, idx) => ({
        //     value: ens[idx],
        //     label: item,
        //   }));
        // } else {
        //   // 其他情况就是一个 data
        res = ((resp?.data || []) as string[]).map(item => ({
          value: item,
          label: item,
        }));
        // }
        return data ? [...data, ...res] : res;
      } else {
        sethaveData(false);
        setPage(page - 1);
        return [...data];
      }
    },
    {
      refreshDeps: [page, searchKey, measureObjId],
      debounceWait: 300,
      // 人员才需要自动展开
      ready: Boolean(measureObjId) && haveData && isReady,
      manual: false,
    },
  );
  const { run: debounceSearch } = useDebounceFn(
    v => {
      setSearchKey(v);
      mutate([]);
      setPage(1);
      sethaveData(true);
    },
    { wait: 300 },
  );

  useEffect(() => {
    // 如果碰到度量类目清空，需要清除搜索出来的；历史记录
    if (!measureObjId) {
      mutate([]);
    }
  }, [measureObjId]);

  const popupScrollHandler = element => {
    const { scrollTop, scrollHeight, clientHeight } = element;
    const scrollBottom = scrollHeight - (scrollTop + clientHeight);

    if (scrollBottom < 10) {
      if (!fetching && refCanTriggerLoadMore.current) {
        if (!haveData) {
          return;
        }
        setPage(page + 1);
        refCanTriggerLoadMore.current = false;
      }
    } else {
      refCanTriggerLoadMore.current = true;
    }
  };

  const renderContent = () => {
    return (
      <Space direction="vertical">
        <Select
          className="!max-w-[320px]"
          placeholder="选择类目"
          allowClear={true}
          value={measureObjId || undefined}
          onChange={handleMeasureObjChange}>
          {domainMeasureObjList.map(({ id, name }) => (
            <Option key={id} value={id}>
              {name}
            </Option>
          ))}
        </Select>
        <Select
          value={filter || undefined}
          className="!w-[158px]"
          placeholder="请输入搜索"
          allowClear={true}
          allowCreate={false}
          onClear={() => {
            setSearchKey('');
          }}
          showSearch={true}
          filterOption={false}
          onPopupScroll={measureObjId === '1' ? popupScrollHandler : undefined}
          mode="multiple"
          maxTagCount={1}
          style={{ overflow: 'hidden' }}
          notFoundContent={
            loading ? (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Spin style={{ margin: 12 }} />
              </div>
            ) : (
              <Empty />
            )
          }
          onSearch={debounceSearch}
          onChange={setFilter}>
          {data?.map(({ value, label }) => (
            <Option key={value} value={value}>
              <Tooltip content={label}>
                <div
                  style={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}>
                  {label}
                </div>
              </Tooltip>
            </Option>
          ))}
          {(() => {
            if (measureObjId !== '1') {
              return null;
            }
            if (data?.length > 0 && !haveData) {
              return (
                <LoadTitle className="w-full flex justify-center items-center">
                  - 已展示全部 -
                </LoadTitle>
              );
            }
            if (fetching) {
              return (
                <LoadTitle className="w-full flex justify-center items-center">
                  <Spin />
                  <span>正在加载中</span>
                </LoadTitle>
              );
            }
            return null;
          })()}
        </Select>
      </Space>
    );
  };

  const buttonText = useMemo(
    () =>
      value.measureObjId && value.filter?.length
        ? `${measureObjName}/${value.filter?.join?.('、')}`
        : '高级筛选',
    [value],
  );

  return (
    <Tooltip
      position="top"
      disabled={!value.measureObjId && !value.filter?.length}
      content={`${measureObjName}/${value.filter?.join?.('、')}`}
      getPopupContainer={() => document.body}>
      <Popconfirm
        className="z-[998]"
        trigger="click"
        title={renderContent()}
        icon={null}
        position="bl"
        disabled={disabled}
        triggerProps={{ clickOutsideToClose: false }}
        getPopupContainer={getPopupContainer}
        onOk={handleOk}
        onVisibleChange={handleVisibleChange}>
        <Button
          type="outline"
          className="max-w-[320px] !px-3 !shadow-none"
          disabled={disabled}>
          <div className="flex items-center truncate">
            <iconpark-icon
              name="domain-measure-obj-filter"
              class="mr-[6px] text-base"
            />
            {buttonText}
          </div>
        </Button>
      </Popconfirm>
    </Tooltip>
  );
};

export default DomainMeasureObjFilter;
