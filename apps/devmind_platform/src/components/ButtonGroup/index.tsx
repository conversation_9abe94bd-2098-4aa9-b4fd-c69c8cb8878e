import { FC, ReactNode } from 'react';

import { Link, Button, Dropdown, Menu } from '@arco-design/web-react';
import { IconMore } from '@arco-design/web-react/icon';
import cs from 'classnames';

import { inDashboardUrl } from '@/utils/url';

export interface ButtonItemProps {
  title: string;
  disabled?: boolean;
  type?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'dashed'
    | 'text'
    | 'outline'
    | 'with-confirm';
  status?: 'warning' | 'danger' | 'success';
  onClick?: () => void;
  node?: ReactNode;
  isShow?: boolean;
}

interface Props {
  buttons: ButtonItemProps[];
  limit?: number;
}

const ButtonGroup: FC<Props> = props => {
  const { buttons, limit = 2 } = props;

  const showButtons = buttons.slice(0, limit);
  let menuButtons = buttons.slice(limit, buttons.length);
  if (inDashboardUrl()) {
    let index = menuButtons.findIndex(e => e.title.includes('订阅'));
    index > -1 && menuButtons.splice(index, 1);
  }
  return (
    <div className="flex items-center gap-6">
      {showButtons.map((props, index) => (
        <ButtonItem {...props} key={index} />
      ))}

      {menuButtons.length > 0 &&
        menuButtons.some(({ isShow = true }) => isShow) && (
          <Dropdown
            position="br"
            droplist={
              <Menu>
                {menuButtons.map(
                  ({
                    title,
                    disabled,
                    status,
                    onClick,
                    type,
                    node,
                    isShow = true,
                  }) =>
                    isShow && (
                      <Menu.Item
                        className={cs({ '!text-error4': status === 'danger' })}
                        key={title}
                        disabled={disabled}
                        onClick={onClick}>
                        {type === 'with-confirm' ? node : title}
                      </Menu.Item>
                    ),
                )}
              </Menu>
            }>
            <div className="w-6 h-6 cursor-pointer hover:bg-fill3 flex items-center justify-center">
              <IconMore />
            </div>
          </Dropdown>
        )}
    </div>
  );
};

const ButtonItem: FC<ButtonItemProps> = ({
  title,
  type = 'text',
  disabled = false,
  isShow = true,
  status,
  onClick,
  node,
  ...reset
}) => {
  if (!isShow) {
    return null;
  }

  if (type === 'text') {
    return (
      <Link
        key={title}
        className="!p-0"
        disabled={disabled}
        status={status === 'danger' ? 'error' : status}
        onClick={onClick}>
        {title}
      </Link>
    );
  }

  if (type === 'with-confirm') {
    return <>{node}</>;
  }

  return (
    <Button
      {...reset}
      key={title}
      disabled={disabled}
      status={status}
      type={type}
      onClick={onClick}>
      {title}
    </Button>
  );
};

export default ButtonGroup;
