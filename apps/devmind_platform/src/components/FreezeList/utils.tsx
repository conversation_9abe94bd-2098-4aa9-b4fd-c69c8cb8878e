import { useModel } from '@jupiter/plugin-runtime/model';

import { useReportConfigUrlQuery } from '@/hooks/url';
import { reportModel } from '@/model/report';

export const useFreezeVersion = () => {
  const [{ freezeList }] = useModel([
    reportModel,
    state => {
      return {
        freezeList: state.freezeList,
      };
    },
  ]);
  const { urlQuery } = useReportConfigUrlQuery();
  return freezeList.find(item => item.freeze_key === urlQuery.freezeVersion);
};
