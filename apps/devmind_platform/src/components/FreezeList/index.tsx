import React, { FC, ReactNode } from 'react';

import {
  Divider,
  Dropdown,
  Menu,
  Button,
  Message,
  Popover,
  Modal,
  Space,
  Tag,
} from '@arco-design/web-react';
import {
  IconCopy,
  IconClose,
  IconDelete,
  IconDown,
  IconInfoCircleFill,
} from '@arco-design/web-react/icon';
import GuideTip from '@byted-paas/guide-tip';
import { useModel } from '@jupiter/plugin-runtime/model';
import { useLocalStorageState } from 'ahooks';
import copy from 'clipboard-copy';
import dayjs from 'dayjs';
import styled from 'styled-components';

import {
  GetReportFreezeReq,
  reportTemplateServiceClient,
} from '@quality/common-apis/src/apis/api/insight_report/report';
import { FreezeVersion } from '@quality/common-apis/src/apis/api/insight_report/report_model';
import { SealStatus } from '@quality/common-apis/src/apis/api/insight_report/sealing_version';

import UserAvatar from '@/components/UserAvatar';
import { API_V1, Authority } from '@/constants';
import { useReportConfigUrlQuery } from '@/hooks/url';
import './styles.scss';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { reportModel } from '@/model/report';
import { hasAuthority } from '@/utils/auth';

import { startTimeFormat } from '../TimeFilter';

export const SpaceItem = styled(Space)<{ disabled: boolean }>`
  display: inline-flex;
  align-items: center;
  font-size: 13px;
  line-height: 22px;
  justify-content: space-between;
  cursor: ${props => (props.disabled ? 'not-allowed' : 'pointer')};
`;

export const FREEZE_TIME_FROMAT = 'YYYYMMDD';
export interface VersionProps {
  disabled?: boolean;
  noFreeze?: boolean;
  onChange?: (value?: string) => void;
  isDrawer?: boolean; // 是否使用在指标故事抽屉中，用于区分单独请求单个指标故事数据的情况
  drawerVisible?: boolean;
}

export function getVersionTag(status, text?: ReactNode) {
  let tagDom;
  if (status <= 1 || status === SealStatus.Suspend) {
    tagDom = <Tag color="orangered">{text || getVersionText(status)}</Tag>;
  } else if (status === 2) {
    tagDom = <Tag>{text || getVersionText(status)}</Tag>;
  } else if (status === SealStatus.Fail) {
    tagDom = <Tag color="red">{text || getVersionText(status)}</Tag>;
  } else {
    tagDom = <Tag color="green">{text || getVersionText(status)}</Tag>;
  }
  return tagDom;
}

export function getVersionText(status) {
  let textDom;
  if (status <= 1 || status === SealStatus.Suspend) {
    textDom = '快照保存中';
  } else if (status === 2) {
    textDom = '快照版本';
  } else if (status === SealStatus.Fail) {
    textDom = '快照失败';
  } else {
    return null;
  }
  return textDom;
}

// function isEqual(str1: string | undefined, str2: string | undefined): boolean {
//   // 先把undefined和空字符串处理为同一种情况，然后进行比较
//   const normalizedStr1 = str1 === undefined ? '' : str1;
//   const normalizedStr2 = str2 === undefined ? '' : str2;

//   return normalizedStr1 === normalizedStr2;
// }

// eslint-disable-next-line no-empty-pattern
const FreezeList: FC<VersionProps> = ({
  onChange = () => null,
  disabled = false,
  noFreeze = false,
  isDrawer = false,
  drawerVisible,
}) => {
  const [
    {
      freezeVersion: reportFreezeVersion,
      freezeList: list,
      reportDetail,
      viewMode,
    },
    { setFreezeVersion: setReportFreezeVersion, setFreezeList: setList },
  ] = useModel([
    reportModel,
    state => {
      return {
        reportDetail: state.reportDetail,
        freezeVersion: state.freezeVersion,
        freezeList: state.freezeList,
        viewMode: state.viewMode,
      };
    },
  ]);

  const [value, setValue] = React.useState<
    Partial<FreezeVersion> | null | undefined
  >(reportFreezeVersion);
  const { virtualSpaceKey } = useVirtualSpaceType();

  React.useEffect(() => {
    setValue(reportFreezeVersion);
  }, [reportFreezeVersion, drawerVisible]);

  // const [{ userInfo }] = useModel([
  //   globalModel,
  //   state => ({
  //     userInfo: state.userInfo,
  //   }),
  // ]);

  const { urlQuery, setUrlQuery } = useReportConfigUrlQuery();

  const { reportId, freezeVersion, nodeId } = urlQuery;

  const changeUrl = freezeVersion => {
    setUrlQuery({
      ...urlQuery,
      freezeVersion,
      mode: 'read',
    });
  };

  const handleVersionChange = key => {
    const version = list.find(item => item.id === key);
    if (isDrawer) {
      setValue(version);
    } else {
      setReportFreezeVersion(version);
      changeUrl(version?.freeze_key || '');
    }

    onChange(version?.freeze_key || '');
  };

  const handleVersionDelete = ({ id: version_id, freeze_key }: any) => {
    reportTemplateServiceClient
      .DelReportFreeze({
        version_id,
        freeze_key,
        version: API_V1,
        space_type: virtualSpaceKey,
        node_id: nodeId,
      })
      .then(() => {
        Message.success('删除快照版本成功');
        fetchVersions().then((list = []) => {
          const version = list?.find(
            item => item?.freeze_status === SealStatus.Success,
          );
          setReportFreezeVersion(version);
          setValue(version);
          changeUrl(version?.freeze_key || '');
          onChange(version?.freeze_key || '');
        });
      })
      .catch(_ => {
        Message.error('删除快照版本失败');
      });
  };

  const fetchVersions = (): Promise<FreezeVersion[]> => {
    if (reportId) {
      const params: GetReportFreezeReq = {
        report_id: reportId || '',
        version: API_V1,
      };
      return reportTemplateServiceClient.GetReportFreeze(params).then(res => {
        setList(res?.data || []);
        let defaultVersion: FreezeVersion | null | undefined;
        if (freezeVersion) {
          defaultVersion = res?.data?.find(
            item => item.freeze_key === freezeVersion,
          );
          setReportFreezeVersion(defaultVersion);
          setValue(defaultVersion);
        }
        return res?.data || [];
      });
    } else {
      setList([]);
      return Promise.resolve([]);
    }
  };

  React.useEffect(() => {
    fetchVersions().then((list = []) => {
      if (freezeVersion) {
        const version = list?.find(item => item?.freeze_key === freezeVersion);
        // 快照已被后台删除，置为实时数据
        if (!version) {
          setReportFreezeVersion(undefined);
          setValue(undefined);
          changeUrl('');
          onChange('');
        }
      }
    });
  }, [reportId]);

  React.useEffect(() => {
    return () => {
      setVisible(false);
    };
  }, []);

  const [visible, setVisible] = useLocalStorageState<boolean>(
    'freezeVersionGuide',
    { defaultValue: true },
  );
  const hasAdminAuthority = hasAuthority(
    reportDetail?.template_info?.authorization_type as string,
    Authority.admin,
  );

  const hasEditAuthority = hasAuthority(
    reportDetail?.template_info?.authorization_type as string,
    Authority.write,
  );
  const isManager = hasEditAuthority || hasAdminAuthority;

  const normalTagDom = (
    <Tag size="small" color="green">
      <span>实时数据</span>
    </Tag>
  );

  const normalTagItem = (
    <>
      {normalTagDom}
      <span className="text-[13px] ml-[8px]">
        上次刷新: {dayjs().format(startTimeFormat)}
      </span>
    </>
  );

  function getVersionList(data: FreezeVersion[]) {
    return (
      <Menu
        style={{ width: 436 }}
        defaultChecked={true}
        selectedKeys={[value?.id || '']}
        onClickMenuItem={key => {
          handleVersionChange(key);
        }}>
        <Menu.Item
          className="pl-[10px] pr-[12px] font-normal"
          defaultChecked={true}
          key="">
          <SpaceItem disabled={disabled}>{normalTagItem}</SpaceItem>
        </Menu.Item>
        {viewMode === 'default'
          ? data.map(item => {
              const isSnapShotting =
                item.freeze_status !== SealStatus.Success &&
                item.freeze_status !== SealStatus.Fail;
              return (
                <Menu.Item
                  className="version-menu-item pl-[10px] pr-[12px] font-normal"
                  defaultChecked={true}
                  key={item.id}>
                  <SpaceItem disabled={disabled}>
                    {getVersionTag(item.freeze_status)}
                    <Popover
                      className="no-max-popover"
                      triggerProps={{
                        style: {
                          maxWidth: 400,
                        },
                      }}
                      content={
                        <span style={{ whiteSpace: 'nowrap' }}>
                          任务ID: {item.freeze_key}
                          <Button
                            onClick={e => {
                              copy(item.freeze_key);
                              e.stopPropagation();
                              Message.success('复制成功');
                            }}
                            icon={<IconCopy />}
                            type="text"
                          />
                        </span>
                      }>
                      <span className="text-[13px] ml-[8px]">
                        版本号: NO.
                        {dayjs(item?.publish_date).format(FREEZE_TIME_FROMAT)}
                      </span>
                    </Popover>

                    <span className="text-[13px] ml-[16px]">
                      发布人: &nbsp;
                    </span>
                    <UserAvatar
                      wrapperStyle={{ marginLeft: -4 }}
                      username={item.publisher}
                    />
                    {Boolean(isManager) && !isSnapShotting && (
                      <Divider
                        type="vertical"
                        style={{ marginRight: -4, marginLeft: -2 }}
                      />
                    )}
                    {Boolean(isManager) && !isSnapShotting && (
                      <div className="version-delete-btn">
                        <IconDelete
                          onClick={e => {
                            e.stopPropagation();
                            Modal.confirm({
                              title: '确认要删除该报告版本吗？',
                              content: '删除该版本后，版本数据将无法恢复。',
                              onOk: () => handleVersionDelete(item),
                              cancelText: '取消',
                              okText: '删除',
                              okButtonProps: {
                                status: 'danger',
                              },
                            });
                          }}
                        />
                      </div>
                    )}
                  </SpaceItem>
                </Menu.Item>
              );
            })
          : null}
      </Menu>
    );
  }
  // 指标卡详情过滤封板保存中的数据
  const filterList =
    list?.filter(
      o =>
        !(
          (o.freeze_status <= 1 || o.freeze_status === SealStatus.Suspend) &&
          isDrawer
        ),
    ) || [];

  const isNormal = noFreeze && !(filterList.length > 0);

  return (
    <GuideTip
      popoverStyle={{ maxWidth: 384 }}
      style={{ maxWidth: 384 }}
      visible={visible}
      content={
        <div style={{ fontSize: 12 }}>
          <IconInfoCircleFill />
          <span style={{ margin: '0px 8px' }}>
            你可点击此处进行封板报告版本的切换和实时数据的查看
          </span>
          <IconClose onClick={() => setVisible(false)} />
        </div>
      }>
      <Dropdown
        disabled={disabled}
        droplist={getVersionList(filterList)}
        trigger="click"
        onVisibleChange={visible => Boolean(visible) && setVisible(false)}
        position="bl">
        {getVersionTag(
          isNormal ? undefined : value?.freeze_status,
          <SpaceItem
            disabled={disabled}
            className={`${disabled ? 'text-gray' : 'text-black'}`}>
            {!isNormal && value ? (
              <SpaceItem disabled={disabled}>
                {getVersionText(value?.freeze_status)}: NO.
                {dayjs(value?.publish_date).format(FREEZE_TIME_FROMAT)}
              </SpaceItem>
            ) : (
              normalTagDom
            )}
            <IconDown />
          </SpaceItem>,
        )}
      </Dropdown>
    </GuideTip>
  );
};

export default FreezeList;
