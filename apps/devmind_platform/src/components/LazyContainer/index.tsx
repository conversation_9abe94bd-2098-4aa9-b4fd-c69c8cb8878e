import React, { useEffect, useRef, useState } from 'react';

import { useUniqueId } from '@/hooks/useUniqueId';

export interface Props {
  children:
    | React.ReactNode
    | (({ isInView }: { isInView: boolean }) => React.ReactNode);
  style?: React.CSSProperties;
  onLoad?: () => void;
  onChange?: (isInView: boolean) => void;
  rootId?: string;
}

export function LazyContainer({
  children,
  style,
  onLoad,
  onChange,
  rootId,
}: Props) {
  const id = useUniqueId();
  const [loaded, setLoaded] = useState(false);
  const [containerHeight, setContainerHeight] = useState<number | null>(null);

  const [isInView, setIsInView] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    LazyContainer.addCallback(id, entry => {
      if (!loaded && entry.isIntersecting) {
        setLoaded(true);
        onLoad?.();
      }

      setIsInView(entry.isIntersecting);
      onChange?.(entry.isIntersecting);
    });
    const root = rootId ? document.querySelector(rootId) : null;
    const observable = new IntersectionObserver(
      entries => {
        for (const entry of entries) {
          LazyContainer.callbacks[entry.target.id]?.(entry);
        }
      },
      { root, rootMargin: '400px 0px 400px 0px' },
    );

    const wrapperEl = wrapperRef.current;

    if (wrapperEl) {
      observable.observe(wrapperEl);
    }

    return () => {
      delete LazyContainer.callbacks[id];
      wrapperEl && LazyContainer.observer.unobserve(wrapperEl);
      if (Object.keys(LazyContainer.callbacks).length === 0) {
        observable.disconnect();
      }
    };
  }, []);

  return (
    <div
      id={id}
      ref={ref => {
        if (containerHeight == null && ref) {
          setContainerHeight(ref.offsetHeight);
          (wrapperRef.current as any) = ref;
        }
      }}
      style={{
        minHeight: containerHeight || 0,
        ...style,
      }}>
      {loaded &&
        (typeof children === 'function' ? children({ isInView }) : children)}
    </div>
  );
}

LazyContainer.callbacks = {} as Record<
  string,
  (e: IntersectionObserverEntry) => void
>;
LazyContainer.addCallback = (
  id: string,
  c: (e: IntersectionObserverEntry) => void,
) => (LazyContainer.callbacks[id] = c);
LazyContainer.observer = new IntersectionObserver(
  entries => {
    for (const entry of entries) {
      LazyContainer.callbacks[entry.target.id](entry);
    }
  },
  { rootMargin: '100px' },
);
