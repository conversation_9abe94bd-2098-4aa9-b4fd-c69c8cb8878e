import * as React from 'react';

import { useModel } from '@jupiter/plugin-runtime/model';
import { usePlateEditorRef } from '@udecode/plate';

import { reportModel } from '@/model/report';

interface EditorRefEffectProps {
  id: string;
}
export const EditorRefEffect: React.FC<EditorRefEffectProps> = ({ id }) => {
  const editorReference = usePlateEditorRef(id);
  const [_, { setEditorRefById }] = useModel([reportModel, _ => ({})]);

  // store editor reference
  React.useEffect(() => {
    if (editorReference) {
      setEditorRefById({ [id]: editorReference });
    }

    return () => {
      setEditorRefById({ [id]: null });
    };
  }, []);

  return null;
};
