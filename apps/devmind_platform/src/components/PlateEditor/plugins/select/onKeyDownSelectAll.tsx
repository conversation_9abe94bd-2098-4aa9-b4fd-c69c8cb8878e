import React from 'react';

import { Button, Message, MessageProps } from '@arco-design/web-react';
import { MessageType } from '@arco-design/web-react/es/Message';
import {
  getEdgePoints,
  select,
  PlateEditor,
  Value,
  DOMHandlerReturnType,
} from '@udecode/plate';
import { isHotkey } from 'is-hotkey';
import { BasePoint, Path } from 'slate';

import { SELECT_ALL_TIPS } from '@/constants/editor';

const TIPS_FLAG = '1';

const isSelectAllShortcut = isHotkey('mod+a');

let tipsClearup: MessageType | null = null;

const setNotShowTips = () => {
  localStorage.setItem(SELECT_ALL_TIPS.FOREVER, TIPS_FLAG);
  tipsClearup?.();
};

const messageConfig: MessageProps = {
  content: (
    <div className="inline-flex items-center">
      <strong>连续按下 ⌘ + A 以选中全文</strong>
      <Button size="small" type="text" onClick={setNotShowTips}>
        不再提醒
      </Button>
    </div>
  ),
  transitionClassNames: 'slideTop',
};

export const onKeyDownSelectAll = <
  V extends Value = Value,
  E extends PlateEditor<V> = PlateEditor<V>,
>(
  editor: E,
): DOMHandlerReturnType => {
  return event => {
    const e = event as KeyboardEvent;

    function selectBlock(anchor: BasePoint, focus: BasePoint) {
      select(editor, { anchor, focus });

      const notShowTips =
        localStorage.getItem(SELECT_ALL_TIPS.FOREVER) ||
        sessionStorage.getItem(SELECT_ALL_TIPS.ONCE);

      if (notShowTips) {
        return;
      }

      tipsClearup = Message.info(messageConfig);
      sessionStorage.setItem(SELECT_ALL_TIPS.ONCE, TIPS_FLAG);
    }

    if (isSelectAllShortcut(e)) {
      if (editor.selection) {
        const { anchor, focus } = editor.selection;
        /* 选区是否跨 block */
        if (Path.isCommon(anchor.path, focus.path)) {
          /* 选区是否已选中整个 block ？ */
          const [blockStart, blockEnd] = getEdgePoints(
            editor,
            editor.selection.anchor.path,
          );

          const blockSelectAll =
            anchor.offset === blockStart.offset &&
            focus.offset === blockEnd.offset;

          /* 如果选区不是整个 block ，则选中整个 block */
          if (!blockSelectAll) {
            e.preventDefault();
            selectBlock(blockStart, blockEnd);
          }
        }
      }
    }
  };
};
