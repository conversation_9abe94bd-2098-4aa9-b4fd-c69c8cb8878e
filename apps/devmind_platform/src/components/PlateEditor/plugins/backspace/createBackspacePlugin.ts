import {
  createPluginFactory,
  getAboveNode,
  queryNode,
  QueryNodeOptions,
  TNodeEntry,
} from '@udecode/plate';
import { isHotkey } from 'is-hotkey';

export type BackspacePlugin = {
  query?: QueryNodeOptions;
};

const isBackspaceShortcut = isHotkey('backspace');

export const KEY_BACKSPACE = 'backspace';

export const createBackspacePlugin = createPluginFactory<BackspacePlugin>({
  key: KEY_BACKSPACE,
  handlers: {
    onKeyDown:
      (editor, { options: { query } }) =>
      event => {
        if (!isBackspaceShortcut(event) || !editor.selection) {
          return;
        }

        const [cell] =
          getAboveNode(editor, {
            // 这里不需要指定 path 因为用不到
            match: node => queryNode([node] as unknown as TNodeEntry, query),
          }) || [];

        if (cell) {
          event.preventDefault();
          editor.deleteBackward('block');
        }
      },
  },
});
