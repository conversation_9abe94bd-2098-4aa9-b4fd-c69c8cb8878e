// import {
//   autoformatArrow,
//   autoformatLegal,
//   autoformatLegalHtml,
//   autoformatMath,
//   autoformatPunctuation,
//   autoformatSmartQuotes,
// } from '@udecode/plate';

import { autoformatBlocks, overviewAutoformatBlocks } from './autoformatBlocks';
import { autoformatLists } from './autoformatLists';
import { autoformatMarks } from './autoformatMarks';
// import { autoformatLists } from './autoformatLists';
// import { autoformatMarks } from './autoformatMarks';

export const autoformatRules = [
  ...autoformatBlocks,
  // ...autoformatLists,
  // ...autoformatMarks,
  // ...autoformatSmartQuotes,
  // ...autoformatPunctuation,
  // ...autoformatLegal,
  // ...autoformatLegalHtml,
  // ...autoformatArrow,
  // ...autoformatMath,
];

export const overviewAutoformatRules = [
  ...overviewAutoformatBlocks,
  ...autoformatLists,
  ...autoformatMarks,
];
