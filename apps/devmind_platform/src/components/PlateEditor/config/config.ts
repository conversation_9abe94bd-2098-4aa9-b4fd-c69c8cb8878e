import {
  PlatePlugin,
  ExitBreakPlugin,
  IndentPlugin,
  ResetNodePlugin,
  // TrailingBlockPlugin,
  ELEMENT_PARAGRAPH,
  KEYS_HEADING,
  isBlockAboveEmpty,
  isSelectionAtBlockStart,
  ELEMENT_BLOCKQUOTE,
  createPlateUI,
  AutoformatPlugin,
  ELEMENT_H1,
  withProps,
  ELEMENT_H2,
  ELEMENT_H3,
  SoftBreakPlugin,
  withPlaceholders,
  Value,
  SelectOnBackspacePlugin,
} from '@udecode/plate';
import { css } from 'styled-components';

import { BackspacePlugin } from '../plugins';
import {
  ELEMENT_METRIC_CARD_AREA,
  MetricCardArea,
  MetricCardAreaProps,
} from '../ui/metric-card-area';
import { ELEMENT_RICHTEXT, LarkEditor } from '../ui/rich-text';
import { StyledElement } from '../ui/styled-components/StyledElement/StyledElement';

import { autoformatRules } from './autoformat/autoformatRules';

const ELEMENT_HEADING = [ELEMENT_H1, ELEMENT_H2, ELEMENT_H3];

const resetBlockTypesCommonRule = {
  types: [ELEMENT_BLOCKQUOTE, ...ELEMENT_HEADING],
  defaultType: ELEMENT_PARAGRAPH,
};

interface MainConfig {
  autoformat: Partial<PlatePlugin<AutoformatPlugin>>;
  exitBreak: Partial<PlatePlugin<ExitBreakPlugin>>;
  indent: Partial<PlatePlugin<IndentPlugin>>;
  // mentionItems: any;
  resetBlockType: Partial<PlatePlugin<ResetNodePlugin>>;
  softBreak?: Partial<PlatePlugin<SoftBreakPlugin>>;
  selectOnBackspace: Partial<PlatePlugin<SelectOnBackspacePlugin>>;
  backspace: Partial<PlatePlugin<BackspacePlugin>>;
  // trailingBlock: Partial<PlatePlugin<TrailingBlockPlugin>>;
  blockMenu: Partial<PlatePlugin>;
  metricCardArea?: Partial<PlatePlugin<MetricCardAreaProps<Value>>>;
}

const CustomVoidElements = [ELEMENT_METRIC_CARD_AREA, ELEMENT_RICHTEXT];

export const MAIN_CONFIG: MainConfig = {
  indent: {
    inject: {
      props: {
        validTypes: [ELEMENT_PARAGRAPH],
      },
    },
  },
  softBreak: {
    options: {
      rules: [
        { hotkey: 'shift+enter' },
        {
          hotkey: 'enter',
          query: {
            allow: [ELEMENT_BLOCKQUOTE],
          },
        },
      ],
    },
  },
  exitBreak: {
    options: {
      rules: [
        {
          hotkey: 'mod+enter',
        },
        {
          hotkey: 'mod+shift+enter',
          before: true,
        },
        {
          hotkey: 'enter',
          query: {
            start: true,
            end: true,
            allow: [...KEYS_HEADING],
          },
        },
      ],
    },
  },
  autoformat: {
    options: {
      rules: autoformatRules,
    },
  },
  resetBlockType: {
    options: {
      rules: [
        {
          ...resetBlockTypesCommonRule,
          hotkey: 'Enter',
          predicate: isBlockAboveEmpty,
        },
        {
          ...resetBlockTypesCommonRule,
          hotkey: 'Backspace',
          predicate: isSelectionAtBlockStart,
        },
      ],
    },
  },
  selectOnBackspace: {
    options: {
      query: {
        allow: CustomVoidElements,
      },
    },
  },
  backspace: {
    options: {
      query: {
        allow: CustomVoidElements,
      },
    },
  },
  blockMenu: {},
  metricCardArea: {},
};

export const getDefaultComponents = (editorID: string, readOnly?: boolean) => {
  let components = createPlateUI({
    [ELEMENT_PARAGRAPH]: withProps(StyledElement, {
      styles: {
        root: css`
          margin: 8px 0;
          padding: 0;
          line-height: 1.8;
        `,
      },
      prefixClassNames: 'p',
    }),
    [ELEMENT_H1]: withProps(StyledElement, {
      placeholder: 'H1',
      styles: {
        root: css`
          margin: 1em 0 10px;
          font-size: 1.625rem;
          font-weight: 500;
          line-height: 1.8;
        `,
      },
    }),
    [ELEMENT_H2]: withProps(StyledElement, {
      styles: {
        root: css`
          margin: 0.9em 0 8px;
          font-size: 1.375rem;
          font-weight: 500;
          line-height: 1.8;
        `,
      },
    }),
    [ELEMENT_H3]: withProps(StyledElement, {
      styles: {
        root: css`
          margin: 0.8em 0 8px;
          font-size: 1.25rem;
          font-weight: 500;
          line-height: 1.8;
        `,
      },
    }),
    [ELEMENT_METRIC_CARD_AREA]: withProps(MetricCardArea, {
      editorID,
      readOnly,
    }),
    [ELEMENT_RICHTEXT]: withProps(LarkEditor, { editorID }),
  });

  components = withPlaceholders(components, [
    ...ELEMENT_HEADING.map(elm => ({
      key: elm,
      placeholder: `${elm.toUpperCase()} 标题`,
      hideOnBlur: false,
    })),
  ]);

  return components;
};

export const getDefaultEditableProps = () => ({
  spellCheck: false,
  autoFocus: false,
  style: {},
});
