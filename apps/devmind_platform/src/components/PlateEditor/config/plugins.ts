import {
  createAutoformatPlugin,
  createBlockquotePlugin,
  createCodeBlockPlugin,
  createHeadingPlugin,
  createIndentPlugin,
  createParagraphPlugin,
  createResetNodePlugin,
  createSoftBreakPlugin,
  // createTrailingBlockPlugin,
  createExitBreakPlugin,
  createSelectOnBackspacePlugin,
  createNodeIdPlugin,
} from '@udecode/plate';

import { uuid } from '@quality/common-utils';

import { ReportEditorProps } from '../editor';
import { createSelectAllPlugin, createBackspacePlugin } from '../plugins';
import { createBlockMenuPlugin } from '../ui/block-menu';
import { createMetricCardAreaPlugin } from '../ui/metric-card-area';
import { createRichTextPlugin } from '../ui/rich-text';

import { MAIN_CONFIG } from './config';

export const getMainPlugin = (props: ReportEditorProps) => {
  const plugins = [
    createParagraphPlugin(), // paragraph element
    createBlockquotePlugin(), // blockquote element
    createCodeBlockPlugin(), // code block element
    createHeadingPlugin(), // heading elements
    createIndentPlugin(MAIN_CONFIG.indent),
    createAutoformatPlugin(MAIN_CONFIG.autoformat),
    createResetNodePlugin(MAIN_CONFIG.resetBlockType),
    createExitBreakPlugin(MAIN_CONFIG.exitBreak),
    createSoftBreakPlugin(MAIN_CONFIG.softBreak),
    // createTrailingBlockPlugin(MAIN_CONFIG.trailingBlock),
    createSelectOnBackspacePlugin(MAIN_CONFIG.selectOnBackspace),
    createBackspacePlugin(MAIN_CONFIG.backspace),
    // 指标卡区域
    createMetricCardAreaPlugin(),
    // 扩展菜单
    createBlockMenuPlugin({
      ...MAIN_CONFIG.blockMenu,
      props: {
        readOnly: props.readOnly,
        editorID: props.id,
      },
    }),
    // 富文本
    createRichTextPlugin(),

    // 全选 plugin
    createSelectAllPlugin(),
    // 给节点增加 uuid
    createNodeIdPlugin({
      options: {
        idCreator: () => uuid(),
        reuseId: true,
      },
    }),
  ];

  return plugins;
};
