.reportEditor {
  [data-slate-node='element'] {
    transition: background-color 0.1s ease;

    &.hovered {
      background-color: #ecf0fe;
    }
  }

  .block-flash {
    box-sizing: border-box;
    animation-name: block-flash;
    animation-duration: 1000ms;

    animation-iteration-count: 1;
    animation-timing-function: linear;
    button svg {
      color: #F7BA1E;
    }
  }

  :target {
    scroll-margin-top: 80px;
  }
}

@keyframes block-flash {
  0% {
    border: 1px solid transparent;
  }

  50% {
    border: 1px solid var(--measure-brand4);
  }
  100% {
    border: 1px solid transparent;
  }
}
