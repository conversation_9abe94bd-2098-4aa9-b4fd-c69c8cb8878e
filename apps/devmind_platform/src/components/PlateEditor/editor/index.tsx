import React, { useCallback, useMemo, useRef } from 'react';

import { useModel } from '@jupiter/plugin-runtime/model';
import {
  Plate,
  PlateEditor,
  createPlugins,
  focusEditor,
  Value,
  ELEMENT_H1,
} from '@udecode/plate';
import { useThrottleFn } from 'ahooks';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { ReactEditor } from 'slate-react';

import { log } from '@quality/common-utils';

import { forceDetailEditToken } from '@/components/Space/components/ReportConfigModal/ReportConfig/Create';
import { EditorOnChangeEvent } from '@/events/report';
import { reportModel } from '@/model/report';
import { ReportManager } from '@/modules/report';
import { inDashboardUrl } from '@/utils/url';

import { EditorRefEffect } from '../components/EditorRefEffect';
import { UpdateEffect } from '../components/UpdateEffect';
import {
  getDefaultComponents,
  getDefaultEditableProps,
} from '../config/config';
import { getMainPlugin } from '../config/plugins';
import { BlockMenu } from '../ui/block-menu/components/BlockMenu';
import { BottomSpace } from '../ui/bottom-space/components/BottomSpace';
import {
  serialize,
  deserialize,
  replaceAll,
  clearHistory,
  initReportDefaultValue,
} from '../utils';

import 'tippy.js/dist/tippy.css';
import './styles.scss';

const SAVE_INTERVAL = 5000;

export interface ReportEditorProps {
  id: string;
  initialValue?: string;
  readOnly?: boolean;
  showMenu?: boolean;
  freezed?: boolean;
}

export const isEmptyEditorContent = value => {
  if (!inDashboardUrl()) {
    return false;
  }
  let empty = false;

  let childrenEmpty = true;
  value?.map(v => {
    if (v?.metrics?.length > 0 && v?.type === 'metric-card') {
      childrenEmpty = false;
    }
    v?.children?.map(c => {
      if (c?.text !== '' && v?.type !== 'metric-card') {
        childrenEmpty = false;
      }

      return c;
    });
    return v;
  });

  return empty || childrenEmpty;
};

export const ReportEditor: React.FC<ReportEditorProps> = props => {
  const {
    id: editorID,
    readOnly = false,
    freezed = false,
    showMenu = true,
  } = props;

  const [{ editorReference, templateId }, { processOps }] = useModel([
    reportModel,
    state => ({
      templateId: state.reportDetail.template_info?.template_id,
      editorReference: state.editorRefById[editorID],
    }),
  ]);
  const { defaultEditableProps, plugins } = useMemo(() => {
    const components = getDefaultComponents(editorID, readOnly);
    const defaultEditableProps = getDefaultEditableProps();

    const plugins = createPlugins(getMainPlugin(props), {
      components,
    });
    return {
      plugins,
      defaultEditableProps,
    };
  }, [editorID, readOnly]);

  const currentAstReference = useRef<Value>(
    deserialize(props.initialValue, initReportDefaultValue()),
  );
  /** only used for compare */
  const currentTextReference = useRef<string | undefined>(props.initialValue);
  const { run: throttledProcessOps } = useThrottleFn(
    (editor: PlateEditor) => {
      processOps({ editor });
    },
    {
      wait: SAVE_INTERVAL,
      leading: true,
      trailing: true,
    },
  );

  const { run: throttledSaver } = useThrottleFn(
    (newValue: Value) => {
      // DEBUG: console
      const newText = serialize(newValue);
      currentTextReference.current = newText;

      // log.info('editor value:', newValue);
      ReportManager.getModule()
        ?.getReportEvents()
        .publish(new EditorOnChangeEvent());
    },
    {
      wait: SAVE_INTERVAL,
      leading: true,
      trailing: true,
    },
  );

  const onChange = (newValue: Value) => {
    // check isComposing and if just set_selection
    if (
      editorReference == null ||
      ReactEditor.isComposing(editorReference as ReactEditor)
    ) {
      return;
    }
    if (editorReference.operations.every(op => op.type === 'set_selection')) {
      return;
    }
    currentAstReference.current = newValue;
    throttledSaver(newValue);
    throttledProcessOps(editorReference);
  };

  const initializeValue = useCallback(() => {
    log.info('props.initialValue changed!!!!', props.initialValue);
    /* 初始化时通知侧边栏刷新一次 */
    if (editorReference) {
      ReportManager.getModule()
        ?.getReportEvents()
        .publish(new EditorOnChangeEvent());
    }
    if (
      currentTextReference.current !== props.initialValue &&
      editorReference
    ) {
      const newValue = deserialize(
        props.initialValue,
        initReportDefaultValue(),
      );
      // empty strategy
      const needEdit = localStorage.getItem(forceDetailEditToken) === '1';
      if (needEdit) {
        newValue[0].type = ELEMENT_H1;
        localStorage.removeItem(forceDetailEditToken);
      }
      currentAstReference.current = newValue;
      replaceAll(editorReference, newValue);

      clearHistory(editorReference);
      focusEditor(editorReference);
    }
    // 主编辑器内容跟着 templateId 走
  }, [templateId, props.initialValue, editorReference]);

  return (
    <DndProvider backend={HTML5Backend}>
      <Plate
        id={editorID}
        onChange={onChange}
        initialValue={currentAstReference.current}
        editableProps={{
          id: editorID,
          className: 'reportEditor',
          ...defaultEditableProps,
          readOnly: readOnly || freezed,
        }}
        plugins={plugins}>
        <EditorRefEffect id={editorID} />
        <UpdateEffect effect={initializeValue} />
        {!readOnly && showMenu && (
          <BlockMenu id={editorID} readOnly={readOnly || freezed} />
        )}
      </Plate>
      <BottomSpace id={editorID} editor={editorReference} readOnly={readOnly} />
    </DndProvider>
  );
};
