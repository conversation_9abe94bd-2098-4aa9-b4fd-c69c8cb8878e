import React from 'react';

import {
  getStyledNodeStyles,
  getRootProps,
  StyledElementProps,
} from '@udecode/plate';
import { castArray } from 'lodash';

import { genRecordIdAttrs } from '../../../utils/attribute';

/**
 * StyledElement with no default styles.
 */
export const StyledElement = (props: StyledElementProps) => {
  const { attributes, children, nodeProps, styles, element } = props;

  const rootProps = getRootProps(props);
  const rootStyles = castArray(styles?.root ?? []);
  const nodePropsStyles = nodeProps?.styles?.root?.css ?? [];

  const { root } = getStyledNodeStyles({
    ...nodeProps,
    styles: { root: [...rootStyles, ...nodePropsStyles] },
  });

  const recordIdAttr = genRecordIdAttrs(element);

  return (
    <div
      {...attributes}
      css={root.css}
      {...rootProps}
      {...nodeProps}
      // 增加 id 用于锚点定位
      {...recordIdAttr}>
      {children}
    </div>
  );
};
