.blockMenu_dropdown-menu {
  @apply w-[228px] p-0;

  .arco-dropdown-menu-inner {
    @apply p-[8px];
  }

  .divider {
    @apply mx-[-8px] my-[4px] w-auto max-w-none;
  }

  .title-item {
    @apply text-[14px] text-gray-500;
  }

  .item {
    @apply flex items-center flex-shrink-0 p-[4px] leading-[32px] h-[32px]
    rounded-[4px]
    select-none;

    .item-content {
      @apply flex items-center  gap-[12px]
      text-[14px] font-normal;

      .menu-icon,
      .menu-text {
        @apply flex;
      }
    }

    &.delete {
      &:hover {
        @apply text-[#E2574D] bg-[#FCEEED];

        path {
          @apply fill-[#E2574D];
        }
      }
    }
  }

  .flatten-item-list {
    @apply grid px-[4px] py-0 mb-[-4px] justify-between;
    grid-template-columns: repeat(6, 24px);

    &:first-child {
      @apply pt-[4px];
    }

    .flatten-item {
      @apply flex items-center justify-center
    w-[24px] h-[24px] rounded-[4px] text-center
    overflow-hidden p-0 m-0 mb-[12px] cursor-pointer;

      &.actived {
        @apply bg-[#E8EDFE];

        path,
        rect {
          @apply fill-[#4171F3];
        }
      }

      &::hover {
        @apply bg-[#F2F3F5];
      }
    }
  }

  // 子菜单样式覆盖
  &.submenu {
    transform: translate(5px, 9px);
    .arco-dropdown-menu-inner {
      @apply p-[4px];
    }
  }
}

// 菜单动画
.blockMenu_slide-left {
  animation: blockMenu_slide-slide-left 0.1s
    cubic-bezier(0.25, 0.46, 0.45, 0.94) both 0.1s;
}
.blockMenu_slide-right {
  animation: blockMenu_slide-slide-right 0.1s
    cubic-bezier(0.25, 0.46, 0.45, 0.94) both 0.1s;
}

@keyframes blockMenu_slide-slide-left {
  // 在动画期间不响应鼠标事件，避免【=】面板动画结束时触发了 mouseleave 导致面板消失
  0% {
    opacity: 0;
    transform: translateX(30px);
    pointer-events: none;
  }

  99% {
    opacity: 1;
    transform: translateX(0);
    pointer-events: none;
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes blockMenu_slide-slide-right {
  // 在动画期间不响应鼠标事件，避免【=】面板动画结束时触发了 mouseleave 导致面板消失
  0% {
    opacity: 0;
    transform: translate(-20px, 9px);
    pointer-events: none;
  }

  99% {
    opacity: 1;
    transform: translate(5px, 9px);
    pointer-events: none;
  }

  100% {
    opacity: 1;
    transform: translate(5px, 9px);
  }
}
