import * as React from 'react';

import { Divider, Menu, MenuItemProps } from '@arco-design/web-react';
import clsx from 'clsx';

import { inDashboardUrl } from '@/utils/url';

import { flattenItems, MenuItemKey, iconMap, IconType } from '../constants';
// 这里引 scss 文件的原因是 styled-components 会对 Menu 进行一层包裹
// 导致 Menu 组件内部判断自身不在 Dropdown 中而有样式问题.
import { useMenuHandler } from '../hooks/useMenuHandler';
import { isVoidType } from '../utils/getNodeType';
import { genMenuKey } from '../utils/menuKeyHelper';

import './styles.scss';

// TODO 需要重构

const Item = ({
  className,
  icon,
  title,
  ...props
}: MenuItemProps & { icon?: any }) => (
  <Menu.Item className={clsx('item', className)} {...props}>
    <div className="item-content">
      {icon ? <div className="menu-icon">{icon}</div> : null}
      {title ? <div className="menu-text">{title}</div> : null}
    </div>
  </Menu.Item>
);

const ItemGroup: React.FC<{ title: string }> = ({ title, children }) => (
  <>
    <div className={clsx('item', 'title-item')}>{title}</div>
    {children}
  </>
);

const getCreationMenuItems: React.FC<{
  below?: boolean;
}> = ({ below = false }) => {
  return (
    <>
      <ItemGroup title="基础">
        <div className="flatten-item-list">
          {flattenItems.map(itemKey => (
            <Menu.Item
              key={genMenuKey(itemKey, below)}
              className={clsx('flatten-item')}>
              {iconMap[itemKey]}
            </Menu.Item>
          ))}
        </div>
      </ItemGroup>
      <ItemGroup title="数据">
        <Item
          key={genMenuKey(MenuItemKey.MetricCardArea, below)}
          icon={iconMap[IconType.MetricCardArea]}
          title="添加卡片"
        />
        {/* IFTRUE_prodEnv */}
        {!inDashboardUrl() && (
          <Item
            key={genMenuKey(MenuItemKey.RichText, below)}
            icon={iconMap[IconType.RichText]}
            title="富文本"
          />
        )}
        {/* FITRUE_prodEnv */}
      </ItemGroup>
    </>
  );
};

const getEditingMenuItems: React.FC<Partial<DropMenuProps>> = ({
  activeType,
  isEmptyBlock,
}) => {
  const isVoidBlock = isVoidType(activeType);
  const flattenMenuItems = (
    <>
      <div className="flatten-item-list">
        {flattenItems.map(itemType => (
          <Menu.Item
            key={itemType}
            className={clsx('flatten-item', {
              actived: !isEmptyBlock && activeType === itemType,
            })}>
            {iconMap[itemType]}
          </Menu.Item>
        ))}
      </div>
      <Divider className="divider" />
    </>
  );

  return (
    <>
      {isVoidBlock ? null : flattenMenuItems}
      <Item
        key="delete"
        icon={iconMap[IconType.Delete]}
        className="delete"
        title="删除"
      />
      <Divider className="divider" />

      <Menu.SubMenu
        key="add"
        className="item"
        title={
          <div className="item-content">
            {<div className="menu-icon">{iconMap[IconType.AddBelow]}</div>}
            <div className="menu-text">在下方添加</div>
          </div>
        }
        triggerProps={{
          duration: 0,
          className: [
            'blockMenu_dropdown-menu',
            'blockMenu_slide-right',
            'submenu',
          ],
          position: 'rb',
        }}>
        {getCreationMenuItems({ below: true })}
      </Menu.SubMenu>
    </>
  );
};

interface DropMenuProps {
  editorId: string;
  activeType?: string;
  isEmptyBlock?: boolean;
}
export const DropMenu: React.FC<DropMenuProps> = ({
  editorId,
  activeType,
  isEmptyBlock,
}) => {
  const { onClickMenuItem } = useMenuHandler(editorId);

  return (
    <Menu className="blockMenu_dropdown-menu" onClickMenuItem={onClickMenuItem}>
      {isEmptyBlock
        ? getCreationMenuItems({})
        : getEditingMenuItems({ activeType, isEmptyBlock })}
    </Menu>
  );
};
