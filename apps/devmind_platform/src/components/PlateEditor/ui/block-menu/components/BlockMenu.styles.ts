import { createStyles } from '@udecode/plate';
import { css } from 'styled-components';

import { BlockMenuProps } from './BlockMenu.types';

const rootCss = css`
  z-index: 80;
  position: fixed;
  transition: left 0.04s, top 0.2s, right 0.04s, bottom 0.2s, max-height 0.2s,
    width 0.2s, height 0.2s;
  transition-timing-function: cubic-bezier(0.34, 0.69, 0.1, 1);
`;

const triggerCss = css`
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ccc;
  padding: 1px;
  background: #fff;
  border-radius: 4px;

  .icon {
    width: 22px;
    height: 22px;
    line-height: 22px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;

    path,
    rect {
      fill: #4171f3;
    }
  }
`;

export const getBlockMenuStyles = (props: BlockMenuProps) => {
  return createStyles({ prefixClassNames: 'BlockMenu', ...props }, [
    {
      root: rootCss,
      trigger: triggerCss,
    },
  ]);
};
