import * as React from 'react';

import { Dropdown } from '@arco-design/web-react';
import { IconDragDotVertical, IconPlus } from '@arco-design/web-react/icon';

import {
  useBlockMenuStoreSelectors,
  blockMenuStoreActions,
} from '../blockMenu.store';
import { iconMap, invertedMenuItemKey } from '../constants';
import { useBlockChangeResponsiveListener } from '../hooks/useBlockChangeResponsiveListener';
import { useMenuVisible } from '../hooks/useMenuVisible';
import { useViewport } from '../hooks/useViewport';
import { getNodeType, isEmptyBlock } from '../utils/getNodeType';
import { getPositionInfo } from '../utils/getPositionInfo';

import { getBlockMenuStyles } from './BlockMenu.styles';
import { DropMenu } from './DropMenu';

import './styles.scss';

const getTriggerContent = (type: string, empty: boolean) => {
  if (empty) {
    return <IconPlus />;
  }

  if (type in invertedMenuItemKey) {
    return (
      <>
        <span className="icon">{iconMap[type]}</span>
        <IconDragDotVertical />
      </>
    );
  }
  return null;
};

export const BlockMenu: React.FC<{ id: string; readOnly: boolean }> = ({
  id: editorId,
  readOnly,
}) => {
  const triggerRef = React.useRef<HTMLDivElement>(null);
  const rawNode = useBlockMenuStoreSelectors.activeRawNode();
  const activeSlateNode = useBlockMenuStoreSelectors.activeSlateNode();
  const visible = useBlockMenuStoreSelectors.visible();
  const { onVisibleChange } = useMenuVisible();

  useBlockChangeResponsiveListener({ visible });
  React.useEffect(() => {
    blockMenuStoreActions.setBlockMenuById({ id: editorId });
  }, [editorId]);

  const viewportInfo = useViewport(visible);

  React.useLayoutEffect(() => {
    const cb = () => {
      blockMenuStoreActions.trigger(true);
    };
    triggerRef.current?.addEventListener('mouseenter', cb);

    return () => {
      triggerRef.current?.removeEventListener('mouseenter', cb);
    };
  }, []);

  if (readOnly) {
    return null;
  }

  const positionInfo = getPositionInfo({
    blockDOM: rawNode,
    triggerDOM: triggerRef.current,
    viewportInfo,
    offsetInfo: [12, 0],
  });

  const { root, trigger } = getBlockMenuStyles({ visible, positionInfo });
  // 避免重复调用 styled-components 的 css 构造方法
  const positionStyle: React.CSSProperties = {
    top: `${positionInfo?.top}px`,
    right: `${positionInfo?.right}px`,
    opacity: `${visible ? 1 : 0}`,
    // 调试样式可以打开下面的注释
    // opacity: 1,
  };

  const { type: nodeType } = getNodeType(activeSlateNode);
  const emptyBlock = isEmptyBlock(activeSlateNode);

  return (
    <div css={root.css} style={positionStyle} className={root.className}>
      <Dropdown
        // popupVisible={true}
        onVisibleChange={onVisibleChange}
        triggerProps={{
          className: 'blockMenu_slide-left',
          mouseEnterDelay: 50,
          mouseLeaveDelay: 50,
          duration: 0,
        }}
        droplist={DropMenu({
          editorId,
          activeType: nodeType as string,
          isEmptyBlock: emptyBlock,
        })}
        position={'left' as any}>
        <div ref={triggerRef} css={trigger?.css} className={trigger?.className}>
          {getTriggerContent(nodeType as string, emptyBlock)}
        </div>
      </Dropdown>
    </div>
  );
};
