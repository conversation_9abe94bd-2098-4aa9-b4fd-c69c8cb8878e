import { invert } from 'lodash';

import { ReactComponent as AddBelowSVG } from '@/assets/svg/toolbar/add-below.svg';
import { ReactComponent as MetricCardSVG } from '@/assets/svg/toolbar/chat-card.svg';
import { ReactComponent as DeleteSVG } from '@/assets/svg/toolbar/delete.svg';
import { ReactComponent as H1SVG } from '@/assets/svg/toolbar/h1.svg';
import { ReactComponent as H2SVG } from '@/assets/svg/toolbar/h2.svg';
import { ReactComponent as H3SVG } from '@/assets/svg/toolbar/h3.svg';
import { ReactComponent as QuoteSVG } from '@/assets/svg/toolbar/quote.svg';
import { ReactComponent as RichtextSVG } from '@/assets/svg/toolbar/richtext.svg';
import { ReactComponent as TextSVG } from '@/assets/svg/toolbar/text.svg';

import { ELEMENT_OVERVIEW } from '../../plugins';
import { ELEMENT_METRIC_CARD_AREA } from '../metric-card-area/constants';
import { ELEMENT_RICHTEXT } from '../rich-text/constants';

export const ADD_BELOW_SIGNAL = 'add-below';

export const MainEditorNodeType = {
  Text: 'p',
  H1: 'h1',
  H2: 'h2',
  H3: 'h3',
  Quote: 'blockquote',
  MetricCardArea: ELEMENT_METRIC_CARD_AREA,
  RichText: ELEMENT_RICHTEXT,
  Overview: ELEMENT_OVERVIEW,
};

export const VoidNodeType = {
  MetricCardArea: MainEditorNodeType.MetricCardArea,
  Overview: MainEditorNodeType.Overview,
};

export const MenuItemKey = {
  Text: MainEditorNodeType.Text,
  H1: MainEditorNodeType.H1,
  H2: MainEditorNodeType.H2,
  H3: MainEditorNodeType.H3,
  Quote: MainEditorNodeType.Quote,
  MetricCardArea: MainEditorNodeType.MetricCardArea,

  RichText: MainEditorNodeType.RichText,

  Overview: MainEditorNodeType.Overview,
  Delete: 'delete',
};

export const IconType = {
  ...MenuItemKey,
  AddBelow: ADD_BELOW_SIGNAL,
};

export const iconMap = {
  [IconType.H1]: <H1SVG />,
  [IconType.H2]: <H2SVG />,
  [IconType.H3]: <H3SVG />,
  [IconType.Quote]: <QuoteSVG />,
  [IconType.AddBelow]: <AddBelowSVG />,
  [IconType.MetricCardArea]: <MetricCardSVG />,
  [IconType.Delete]: <DeleteSVG />,
  [IconType.Text]: <TextSVG />,
  [IconType.RichText]: <RichtextSVG />,
  // FIXME: 概览已独立于编辑器主体内容，这里做菜单上的展示兼容
  [IconType.Overview]: <TextSVG />,
};

export const flattenItems = [
  MenuItemKey.Text,
  MenuItemKey.H1,
  MenuItemKey.H2,
  MenuItemKey.H3,
  MenuItemKey.Quote,
];

export const invertedMenuItemKey = invert(MenuItemKey);
export const invertedVoidNodeType = invert(VoidNodeType);
