import { useEffect, useState } from 'react';

import { isEqual } from 'lodash';

import { getViewportSize } from '../utils/getViewportSize';

export function useViewport(bindEvent: boolean) {
  const [viewportInfo, setViewportInfo] = useState(getViewportSize());

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  useEffect(() => {
    if (bindEvent) {
      const updateViewportInfo = () => {
        setViewportInfo(viewportInfo => {
          const currentViewportInfo = getViewportSize();
          if (!isEqual(currentViewportInfo, viewportInfo)) {
            return currentViewportInfo;
          }
          return viewportInfo;
        });
      };

      window.addEventListener('resize', updateViewportInfo);

      return () => {
        window.removeEventListener('resize', updateViewportInfo);
      };
    }
  }, [bindEvent]);

  return viewportInfo;
}
