import { useEffect } from 'react';

import { blockMenuStoreActions } from '../blockMenu.store';

export const useBlockChangeResponsiveListener = ({
  visible,
}: {
  visible: boolean;
}) => {
  useEffect(() => {
    if (visible) {
      const inVisibleWhenScroll = () => {
        blockMenuStoreActions.visible(false);
      };

      window.addEventListener('wheel', inVisibleWhenScroll);

      return () => {
        window.removeEventListener('wheel', inVisibleWhenScroll);
      };
    }
    return () => undefined;
  }, [visible]);
};
