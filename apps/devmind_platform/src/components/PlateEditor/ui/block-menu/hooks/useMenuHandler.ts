import {
  getPlateEditorRef,
  findN<PERSON><PERSON><PERSON>,
  PlateEditor,
  getPointBefore,
  getPointAfter,
  focusEditor,
  removeNodes,
  insertNodes,
  isVoid,
  setNodes,
  select,
  TNode,
  ELEMENT_DEFAULT,
  hasNode,
} from '@udecode/plate';
import { Path } from 'slate';

import { createSlateElement } from '@/components/PlateEditor/utils';

import {
  blockMenuStoreSelectors,
  blockMenuStoreActions,
} from '../blockMenu.store';
import { MenuItemKey } from '../constants';
import { getMenuKey } from '../utils/menuKeyHelper';

const deleteBlock = (editor: PlateEditor | null, tNode: TNode | null) => {
  if (!editor || !tNode) {
    return;
  }

  blockMenuStoreActions.visible(false);

  const path = findNodePath(editor, tNode)!;
  const before = getPointBefore(editor, path);
  const after = getPointAfter(editor, path);
  removeNodes(editor, { at: path });

  if (!before && !after) {
    insertN<PERSON>(editor, createSlateElement({ type: ELEMENT_DEFAULT }), {
      at: path,
    });
  }

  focusEditor(editor);
  before && select(editor, { anchor: before, focus: before });
};

interface ISetBlock {
  editor: PlateEditor | null;
  type: string;
  tNode: TNode | null;
  below?: boolean;
}
const setBlock = ({ editor, type, tNode, below = false }: ISetBlock) => {
  if (!editor || !tNode) {
    return;
  }

  blockMenuStoreActions.visible(false);
  const path = findNodePath(editor, tNode)!;

  if (below) {
    let nextPath = Path.next(path);
    const element = createSlateElement({ type });
    const hasNextNode = hasNode(editor, nextPath);
    insertNodes(editor, element, { at: nextPath });

    if (!hasNextNode && isVoid(editor, element)) {
      nextPath = Path.next(nextPath);
      insertNodes(editor, createSlateElement(), {
        at: nextPath,
      });
    }

    focusEditor(editor);
    select(editor, nextPath);
  } else {
    setNodes(
      editor,
      createSlateElement({
        type: tNode.type === type ? ELEMENT_DEFAULT : type,
      }),
      { at: path },
    );
  }
};

export const useMenuHandler = (editorId: string) => {
  const { activeSlateNode: slateNode } = blockMenuStoreSelectors.state();

  const editor = getPlateEditorRef(editorId);

  const onClickMenuItem = (menuKey: string) => {
    const { below, key } = getMenuKey(menuKey);
    switch (key) {
      case MenuItemKey.Delete: {
        deleteBlock(editor, slateNode);
        break;
      }
      case MenuItemKey.H1:
      case MenuItemKey.H2:
      case MenuItemKey.H3:
      case MenuItemKey.Text:
      case MenuItemKey.MetricCardArea:
      case MenuItemKey.RichText:
      case MenuItemKey.Quote: {
        setBlock({ editor, tNode: slateNode, below, type: key });
        break;
      }

      default: {
        break;
      }
    }
  };

  return {
    onClickMenuItem,
  };
};
