import {
  Value,
  PlateEditor,
  DOMHandlerReturnType,
  toSlateNode,
} from '@udecode/plate';

import { blockMenuStoreActions } from './blockMenu.store';

export const onMouseMoveBlockMenu = <
  V extends Value = Value,
  E extends PlateEditor<V> = PlateEditor<V>,
>(
  editor: E,
  { props: { editorID } }: any,
): DOMHandlerReturnType => {
  return event => {
    if (editorID === 'parent') {
      blockMenuStoreActions.visible(false);
      return;
    }

    const activeRawNode = (
      (event as MouseEvent).target as HTMLElement
    )?.closest?.('[data-slate-node=element]') as HTMLElement;

    if (activeRawNode) {
      if (activeRawNode.parentElement?.id !== editorID) {
        return;
      }
      blockMenuStoreActions.mergeState({
        activeRawNode,
        activeSlateNode: toSlateNode(editor, activeRawNode)!,
        visible: true,
      });
    }
  };
};
