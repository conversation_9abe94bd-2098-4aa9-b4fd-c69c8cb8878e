import { IOffsetInfo, IViewportInfo } from '../types';

import { getBoundaryRect } from './getBoundaryRect';

export type IOverlayPositionContext = {
  blockDOM: HTMLElement | null;
  triggerDOM: HTMLElement | null;
  viewportInfo: IViewportInfo;
  offsetInfo?: IOffsetInfo;
};

export const getPositionInfo = ({
  blockDOM,
  triggerDOM,
  viewportInfo: { width: viewportWidth },
  offsetInfo = [0, 0],
}: IOverlayPositionContext): {
  top: number;
  right: number;
} => {
  const [offsetX, offsetY] = offsetInfo;

  const blockRect = getBoundaryRect(blockDOM);
  const triggerRect = getBoundaryRect(triggerDOM);
  const isVoidBlock = blockDOM?.dataset.slateVoid;

  if (isVoidBlock) {
    return {
      top: blockRect.top + offsetY,
      right: viewportWidth - blockRect.left + offsetX,
    };
  }

  // FIXME，蠢办法，根据换行符计算单行高度
  const singleBlockHeight =
    blockRect.height / (blockDOM?.textContent?.split('\n').length || 1);

  return {
    top: blockRect.top + (singleBlockHeight - triggerRect.height) / 2 + offsetY,
    right: viewportWidth - blockRect.left + offsetX,
  };
};
