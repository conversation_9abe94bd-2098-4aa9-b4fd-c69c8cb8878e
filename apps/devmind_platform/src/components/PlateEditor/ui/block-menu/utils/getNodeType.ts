import { getNodeString, TNode } from '@udecode/plate';
import { isEmpty } from 'lodash';

import { invertedVoidNodeType, MainEditorNodeType } from '../constants';

export const getNodeType = (tNode: TNode | null) => {
  if (!tNode) {
    return {
      type: null,
      empty: true,
    };
  }
  const empty = isEmpty(getNodeString(tNode));
  return {
    type: tNode.type,
    empty,
  };
};

export const isEmptyBlock = (tNode: TNode | null) => {
  const { empty, type } = getNodeType(tNode);
  return empty && type === MainEditorNodeType.Text;
};

export const isVoidType = (type?: string) =>
  type && invertedVoidNodeType.hasOwnProperty(type);
