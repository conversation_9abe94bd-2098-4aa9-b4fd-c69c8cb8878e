import { createPluginFactory } from '@udecode/plate-core';

import { blockMenuStoreActions } from './blockMenu.store';
import { onMouseMoveBlockMenu } from './onMouseMoveBlockMenu';

export const KEY_BLOCK_MENU = 'block-menu';

export const createBlockMenuPlugin = createPluginFactory({
  key: KEY_BLOCK_MENU,

  handlers: {
    onMouseMove: onMouseMoveBlockMenu,
    onMouseLeave: () => () => {
      blockMenuStoreActions.trigger(false, true);
    },
    onKeyDown: () => () => {
      blockMenuStoreActions.trigger(false);
    },
    onBlur: () => () => {
      blockMenuStoreActions.trigger(false);
    },
  },
});
