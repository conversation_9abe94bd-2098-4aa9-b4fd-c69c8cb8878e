import { TNode } from '@udecode/plate';
import { createStore, StateActions, StoreApi } from '@udecode/zustood';

export type BlockMenuStateById = {
  id: string;
};

export type BlockMenuStoreById = StoreApi<
  string,
  BlockMenuStateById,
  StateActions<BlockMenuStateById>
>;
export type BlockMenuState = {
  byId: Record<string, BlockMenuStoreById>;
  visible: boolean;
  activeId: string | null;
  // slateNodeWeakCache: WeakMap<Element, TNode>;
  activeSlateNode: TNode | null;
  activeRawNode: HTMLElement | null;

  triggerTimer: any;
};

const createBlockMenuStore = (state: BlockMenuStateById) =>
  createStore(`block-menu-${state.id}`)(state);

/**
 * blockMenu 拥有一个全局单例用于存放全局状态，
 * 同时在 `byId` 中可以根据 editorID 创造自己的单例
 */
export const blockMenuStore = createStore('block-menu')<BlockMenuState>({
  byId: {},
  visible: false,
  activeId: null,
  // slateNodeWeakCache: new WeakMap(),
  activeSlateNode: null,
  activeRawNode: null,
  triggerTimer: null,
})
  .extendActions((set, get) => ({
    setBlockMenuById: (state: BlockMenuStateById) => {
      if (get.byId()[state.id]) {
        return;
      }

      set.state(draft => {
        draft.byId[state.id] = createBlockMenuStore(state);
      });
    },
    trigger: (visible: boolean, delay = false) => {
      if (delay) {
        set.triggerTimer(
          setTimeout(() => {
            set.visible(visible);
          }, 300),
        );
      } else {
        clearTimeout(get.triggerTimer());
        set.mergeState({
          visible,
          triggerTimer: null,
        });
      }
    },
  }))
  .extendSelectors(() => ({}));

export const useBlockMenuStoreSelectors = blockMenuStore.use;
export const blockMenuStoreSelectors = blockMenuStore.get;
export const blockMenuStoreActions = blockMenuStore.set;

export const getBlockMenuStoreById = (id: string | null) =>
  id ? blockMenuStoreSelectors.byId()[id] : null;

export const useActiveBlockMenuStore = () => {
  const activeId = blockMenuStoreSelectors.activeId();
  return activeId ? getBlockMenuStoreById(activeId) : null;
};
