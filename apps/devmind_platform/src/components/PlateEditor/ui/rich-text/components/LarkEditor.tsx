import * as React from 'react';
import { useState, useCallback } from 'react';

import {
  EditorComponent,
  IEditorComponentProps,
  IPluginRegister,
  Editor as EditorKit,
  EditorEventType,
} from '@editor-kit/core';
import { DeltaSet } from '@editor-kit/delta';
import { ToolbarItemEnum } from '@editor-kit/plugins';
import { isEditorReadOnly, useEditorRef } from '@udecode/plate';
import clsx from 'clsx';
import { useSelected } from 'slate-react';

import { log } from '@quality/common-utils';

import {
  reportDetailStoreUse,
  reportDetailStoreSet,
} from '@/components/ReportDetail/store';
import {
  getImagePlugin,
  getBasicPlugins,
  getToolbarPlugin,
} from '@/modules/editor-kit/plugins';
import { is } from '@/modules/editor-kit/utils';
import { EditorManager } from '@/modules/editor-kit/utils/createEditor';

import { LarkEditorWrapper } from './LarkEditor.style';
import { LarkEditorProps } from './LarkEditor.type';

export const LarkEditor: React.FC<LarkEditorProps> = props => {
  const { attributes, children, element } = props;
  const [isEmpty, setEmpty] = useState(true);
  const editorIns = useEditorRef();
  const editorKitIns = React.useRef<EditorKit | undefined>();
  const initialRichtextMap = reportDetailStoreUse.initialRichtextMap();

  const readOnly = isEditorReadOnly(editorIns);
  const [editorManager] = useState(new EditorManager('core'));
  const toolbarId = `toolbar__${element.id}`;
  const selected = useSelected();
  const register: IPluginRegister = useCallback(
    editor => [
      ...getImagePlugin(editor, { resizeable: true }),
      ...getBasicPlugins(editor),
      // ...getMentionPlugin(editor),
      ...getToolbarPlugin(editor, {
        toolbarId,
        items: [
          ToolbarItemEnum.HeadingDropdown,
          ToolbarItemEnum.ColorPicker,
          ToolbarItemEnum.DIVIDER,

          ToolbarItemEnum.Bold,
          ToolbarItemEnum.Strikethrough,
          ToolbarItemEnum.Italic,
          ToolbarItemEnum.Underline,
          ToolbarItemEnum.Hyperlink,
          ToolbarItemEnum.DIVIDER,

          ToolbarItemEnum.AlignLeft,
          ToolbarItemEnum.AlignCenter,
          ToolbarItemEnum.AlignRight,
          ToolbarItemEnum.UnorderedList,
          ToolbarItemEnum.OrderedList,
          ToolbarItemEnum.DIVIDER,

          ToolbarItemEnum.Blockquote,
          ToolbarItemEnum.Checkbox,
          ToolbarItemEnum.Image,
        ],
      }),
    ],
    [toolbarId],
  );

  const onInit: IEditorComponentProps['onInit'] = editorKit => {
    editorKitIns.current = editorKit;

    editorKit.on(EditorEventType.BLUR, () => {
      const deltaSet = editorKit.getContent();
      const deltas = deltaSet.deltas;

      reportDetailStoreSet.setRichtextContent(element.id as string, deltas);
      setEmpty(is.isEmpty(deltaSet));
    });
  };

  React.useEffect(() => {
    log.info(element.id, initialRichtextMap);

    if (element.id) {
      const reportComp = initialRichtextMap?.[element.id as string];

      try {
        const deltaSet = new DeltaSet(
          reportComp ? JSON.parse(reportComp.content) : undefined,
        );

        editorKitIns.current?.setContent(deltaSet);
        setEmpty(is.isEmpty(deltaSet));
      } catch (error) {
        log.error(error);
      }
    }
  }, [initialRichtextMap, element.id]);

  return (
    <div {...attributes}>
      <LarkEditorWrapper
        contentEditable={false}
        className={clsx(!readOnly && selected && 'selected')}>
        <div
          id={toolbarId}
          className={clsx(
            'editor-kit-toolbar-v2',
            readOnly && 'readonly',
          )}></div>
        <div
          className={clsx(
            'editor-wrapper',
            readOnly && 'readonly',
            isEmpty && 'empty',
          )}>
          <EditorComponent
            register={register}
            schema={editorManager.schema}
            modules={editorManager.modules}
            onError={editorManager.onError}
            businessKey={editorManager.businessKey}
            editable={!readOnly}
            onInit={onInit}
          />
        </div>
        {children}
      </LarkEditorWrapper>
    </div>
  );
};
