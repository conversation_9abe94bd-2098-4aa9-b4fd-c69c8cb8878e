import styled from 'styled-components';

export const LarkEditorWrapper = styled.div`
  margin: 8px 0;
  &.selected {
    box-shadow: 0 0 0 1px #3b82f9;
  }

  .editor-kit-toolbar-v2 {
    &.readonly {
      display: none;
    }

    .editor-kit-toolbar-v2-wrapper {
      position: initial;
      top: 0;

      border-bottom: none;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
    }
  }

  .editor-wrapper {
    border: 1px solid #dde2e9;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    // 编辑器文字区域样式
    .editor-kit-container {
      min-height: 145px;
      padding: 8px;
    }

    &.readonly {
      border: 0;

      .editor-kit-container {
        min-height: auto;
        padding: 0;
      }

      &.empty {
        .editor-kit-container {
          display: none;
        }
      }
    }
  }
`;
