import { useCallback, useEffect, useMemo, useRef } from 'react';

import { Button } from '@arco-design/web-react';
import { useModel } from '@jupiter/plugin-runtime/model';
import { useParams } from '@jupiter/plugin-runtime/router';
import {
  Value,
  ELEMENT_DEFAULT,
  useEventEditorSelectors,
  getNodeString,
  TNode,
} from '@udecode/plate';
import { useDebounceFn, useMemoizedFn } from 'ahooks';
import { set } from 'lodash';
import { ReactEditor, useSelected } from 'slate-react';

import {
  GetReportSummaryReq,
  reportTemplateServiceClient,
} from '@quality/common-apis/src/apis/api/insight_report/report';
import { API_V1 } from '@quality/common-components/src/utils/constant';
import { log } from '@quality/common-utils';

import { CommentButton } from '@/components/Layout/ReportLayout/CommentButton';
import { AUTO_REFRESH_SUMMARY_FLAG, OVERVIEW_EDITOR_ID } from '@/constants';
import { OverviewAutoGenerate } from '@/events/report';
import { useReportConfigUrlQuery } from '@/hooks/url';
import { reportModel } from '@/model/report';
import { ReportManager } from '@/modules/report';

import { UpdateEffect } from '../../components/UpdateEffect';
import {
  clearHistory,
  createSlateElement,
  deserialize,
  initOverviewDefaultValue,
  metricAstToOverviewAst,
  replaceAll,
  serialize,
} from '../../utils';

import { Action } from './components/Action';
import { Editor } from './components/Editor';
import { ReportH1Styled, StyledWapper } from './Overview.styles';

const SAVE_INTERVAL = 1000;

const genInitialValue = () => createSlateElement({ type: ELEMENT_DEFAULT });

export interface IOverviewElement {
  readOnly: boolean;
  freezed: boolean;

  switchVersion: () => void;
}

// TODO 已弃用，后续稳定时删除
export const OverviewElement = ({
  readOnly,
  freezed,
  switchVersion,
}: IOverviewElement) => {
  const { templateId } = useParams<{ templateId: string }>();
  const nodeRef = useRef(null);

  const [
    { instanceInfo, metricList, parentMetricList, metricAreaMap, editorRefs },
    { setReportSummary, setAutoRefreshSummaryFlag, setCommentMeta },
  ] = useModel([
    reportModel,
    state => ({
      instanceInfo: state.$instanceInfo,

      metricList: state.metricList,
      parentMetricList: state.parentMetricList,
      metricAreaMap: state.metricAreaMap,
      editorRefs: state.editorRefById,
    }),
  ]);

  // FIXME: 等概览接口单独抽离出来后再进行
  // const { saveReport } = useSaveReport();

  const selected = useSelected();
  const editorReference = editorRefs[OVERVIEW_EDITOR_ID];
  const focused = useEventEditorSelectors.focus() === OVERVIEW_EDITOR_ID;

  // FIXME: 引用暂时保留，之后会去掉
  const currentEditorReference = useRef(editorReference);
  const currentAstReference = useRef<Value>(
    deserialize(instanceInfo?.summary?.content, initOverviewDefaultValue()),
  );

  /** used for compare */
  const currentTextReference = useRef<string | undefined>(
    instanceInfo?.summary?.content,
  );

  const { run: debouncedSaver } = useDebounceFn(
    (newValue: Value) => {
      // DEBUG: console
      log(`overview newValue`, newValue);
      const newText = serialize(newValue);
      currentTextReference.current = newText;
    },
    {
      wait: SAVE_INTERVAL,
    },
  );

  const totalMetricList = useMemo(
    () => [...parentMetricList, ...metricList],
    [parentMetricList, metricList],
  );

  const {
    urlQuery: { range = [], reportId: report_id, granularity, freezeVersion },
    setUrlQuery,
  } = useReportConfigUrlQuery();

  const [start_time, end_time] = range || [];

  useEffect(() => {
    if (report_id && instanceInfo?.report_id === report_id && !freezeVersion) {
      return;
    }
    if (!start_time || !end_time) {
      return;
    }
    const params: GetReportSummaryReq = {
      template_id: templateId,
      version: API_V1,
      start_time,
      end_time,
      report_id,
      seal_version: freezeVersion,
    };

    reportTemplateServiceClient.GetReportSummary(params).then(res => {
      if (!res) {
        return;
      }

      // set initial value
      if (!res.data?.summary?.content) {
        if (!res.data) {
          res.data = {} as any;
        }

        set(res.data, 'summary.content', serialize(initOverviewDefaultValue()));
        // 默认设置概览自动生成
        set(
          res.data,
          'autorefresh_summary_flag',
          AUTO_REFRESH_SUMMARY_FLAG.YES,
        );
      }
      setCommentMeta({
        entity_token: templateId + start_time + end_time + granularity,
      });
      setReportSummary(res.data);
      setUrlQuery({ reportId: res.data?.report_id });
    });
  }, [start_time, end_time, report_id, freezeVersion]);

  const handleRefresh = () => {
    if (editorReference) {
      const value = metricAstToOverviewAst(
        editorRefs,
        metricAreaMap,
        totalMetricList,
      );
      replaceAll(editorReference, value as Value);
      // 手动刷新概览内容后，没有用户自定义编辑内容，后续走自动更新逻辑
      setAutoRefreshSummaryFlag(true);
    }
  };

  const autoGenerateHandle = useMemoizedFn(() => {
    if (instanceInfo?.autorefresh_summary_flag) {
      handleRefresh();
      // 自动生成概览后，保存当前最新概览信息
      // saveReport(true);
    }
  });

  // FIXME: 过滤无效按键
  const onKeyDown = () => {
    if (instanceInfo?.autorefresh_summary_flag) {
      setAutoRefreshSummaryFlag(false);
    }
  };

  const initializeSubscribe = useCallback(() => {
    const reportModule = ReportManager.getModule();

    const sub1 = reportModule?.subscribeDataMartFetch();

    const sub2 = reportModule
      ?.getReportEvents()
      .subscribe(OverviewAutoGenerate, () => {
        autoGenerateHandle();
      });

    return () => {
      sub1?.unsubscribe();
      sub2?.unsubscribe();
    };
  }, []);

  const handleChange = useCallback(
    (newValue: Value) => {
      // check isComposing and if just set_selection
      if (
        editorReference == null ||
        ReactEditor.isComposing(editorReference as ReactEditor)
      ) {
        return;
      }
      if (editorReference.operations.every(op => op.type === 'set_selection')) {
        return;
      }

      currentAstReference.current = newValue;
      debouncedSaver(newValue);
    },
    [editorReference, debouncedSaver],
  );

  const initializeValue = useCallback(() => {
    const newText = instanceInfo?.summary?.content;
    if (editorReference) {
      log('summary content changed');
      const newValue = deserialize(newText);
      currentAstReference.current = newValue;
      currentEditorReference.current = editorReference;

      log(`replaceAll(editorReference, newValue);`, newText);
      replaceAll(editorReference, newValue);
      clearHistory(editorReference);
    }
  }, [instanceInfo?.summary?.content, editorReference]);

  const isEmpty =
    editorReference && Boolean(getNodeString(editorReference as TNode));
  useEffect(() => {
    window.addEventListener(
      'message',
      e => {
        const event = e?.data?.event;
        if (event) {
          if (event === 'activateComment' && nodeRef?.current) {
            const current = nodeRef?.current as any;
            const position = e.data?.data?.position;
            if (position === 'reportsummary') {
              document.getElementById('report-content')?.scrollTo({
                left: 0,
                top: current?.offsetTop - 200,
                behavior: 'smooth',
              });
              setTimeout(() => {
                ReportManager.getModule()?.flash('overview');
              }, 500);
            }
          }
        }
      },
      false,
    );
  }, []);

  return (
    <>
      <div className="flex justify-between items-center">
        <ReportH1Styled>重点概览</ReportH1Styled>
        <Button type="text" size="mini" onClick={switchVersion}>
          切换为新版概览
        </Button>
      </div>
      <StyledWapper
        selected={selected}
        ref={nodeRef}
        focused={focused}
        freezed={freezed}>
        <UpdateEffect effect={initializeValue} />
        <UpdateEffect effect={initializeSubscribe} />

        {readOnly ? null : (
          <Action isEmpty={isEmpty} onRefresh={handleRefresh} />
        )}
        <Editor
          readOnly={readOnly || freezed}
          onChange={handleChange}
          initialValue={[genInitialValue()]}
          onKeyDown={onKeyDown}
        />
        {/* IFTRUE_prodEnv */}
        <CommentButton id="reportsummary" quote="摘要" />
        {/* FITRUE_prodEnv */}
      </StyledWapper>
    </>
  );
};
