import { Value, PlateProps } from '@udecode/plate-core';
import { StyledElementProps } from '@udecode/plate-styled-components';
import { CSSProp } from 'styled-components';

export interface EditorProps {
  readOnly: boolean;
  onChange?: PlateProps['onChange'];
  initialValue?: PlateProps['initialValue'];
  onKeyDown?: React.KeyboardEventHandler;
}

export interface MediaEmbedElementStyles {
  input: CSSProp;
}

export type OverviewElementProps<V extends Value> = StyledElementProps<V>;
