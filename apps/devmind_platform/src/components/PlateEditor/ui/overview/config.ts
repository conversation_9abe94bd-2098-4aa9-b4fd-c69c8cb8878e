import {
  createParagraphPlugin,
  create<PERSON>ist<PERSON>lugin,
  createIndentPlugin,
  createIndentListPlugin,
  createResetNodePlugin,
  createSoftBreakPlugin,
  createExitBreakPlugin,
  createAutoformatPlugin,
  createBoldPlugin,
  createItalicPlugin,
  createUnderlinePlugin,
  createStrikethroughPlugin,
  createFontColorPlugin,
  createFontBackgroundColorPlugin,
  createFontSizePlugin,
  createPlateUI,
  AutoformatPlugin,
  ELEMENT_BLOCKQUOTE,
  ELEMENT_LI,
  ELEMENT_LIC,
  ELEMENT_OL,
  ELEMENT_PARAGRAPH,
  ELEMENT_UL,
  ExitBreakPlugin,
  IndentListPlugin,
  IndentPlugin,
  isBlockAboveEmpty,
  isSelectionAtBlockStart,
  KEYS_HEADING,
  PlatePlugin,
  ResetNodePlugin,
  SoftBreakPlugin,
  ELEMENT_H1,
  ELEMENT_H2,
  ELEMENT_H3,
  StyledElement,
  with<PERSON>rops,
  createHeadingPlugin,
  ELEMENT_LINK,
  create<PERSON>inkPlugin,
  LinkPlugin,
} from '@udecode/plate';
import { css } from 'styled-components';

import { ReportManager } from '@/modules/report';

import { overviewAutoformatRules } from '../../config/autoformat/autoformatRules';

interface OverviewConfig {
  autoformat: Partial<PlatePlugin<AutoformatPlugin>>;
  exitBreak: Partial<PlatePlugin<ExitBreakPlugin>>;
  indent: Partial<PlatePlugin<IndentPlugin>>;
  link: Partial<PlatePlugin<LinkPlugin>>;
  indentList: Partial<PlatePlugin<IndentListPlugin>>;
  resetBlockType: Partial<PlatePlugin<ResetNodePlugin>>;
  softBreak?: Partial<PlatePlugin<SoftBreakPlugin>>;
  // selectOnBackspace: Partial<PlatePlugin<SelectOnBackspacePlugin>>;
  // trailingBlock: Partial<PlatePlugin<TrailingBlockPlugin>>;
}

export const ELEMENT_HEADING = [ELEMENT_H1, ELEMENT_H2, ELEMENT_H3];

const resetBlockTypesCommonRule = {
  types: [...ELEMENT_HEADING],
  defaultType: ELEMENT_PARAGRAPH,
};

export const OVERVIEW_CONFIG: OverviewConfig = {
  indent: {
    inject: {
      props: {
        validTypes: [ELEMENT_PARAGRAPH],
      },
    },
  },
  indentList: {
    inject: {
      props: {
        validTypes: [
          ELEMENT_PARAGRAPH,
          ELEMENT_UL,
          ELEMENT_OL,
          ELEMENT_LI,
          ELEMENT_LIC,
          ELEMENT_LINK,
        ],
      },
    },
  },
  link: {
    handlers: {
      onClick: () => e => {
        // without setTimeout method the baseURI value may not match with the event.target
        setTimeout(() => {
          const event = e as any;
          const anchor = event.target.baseURI.split('#')[1];
          ReportManager.getModule()?.flash(anchor);
        }, 0);
      },
    },
  },
  softBreak: {
    options: {
      rules: [
        { hotkey: 'shift+enter' },
        {
          hotkey: 'enter',
          query: {
            allow: [ELEMENT_BLOCKQUOTE],
          },
        },
      ],
    },
  },
  exitBreak: {
    options: {
      rules: [
        {
          hotkey: 'mod+enter',
        },
        {
          hotkey: 'mod+shift+enter',
          before: true,
        },
        {
          hotkey: 'enter',
          query: {
            start: true,
            end: true,
            allow: KEYS_HEADING,
          },
        },
      ],
    },
  },
  resetBlockType: {
    options: {
      rules: [
        {
          ...resetBlockTypesCommonRule,
          hotkey: 'Enter',
          predicate: isBlockAboveEmpty,
        },
        {
          ...resetBlockTypesCommonRule,
          hotkey: 'Backspace',
          predicate: isSelectionAtBlockStart,
        },
      ],
    },
  },
  autoformat: {
    options: {
      rules: overviewAutoformatRules,
    },
  },
};

// 字体大小与行高按照主编辑器 0.85 倍数换算
export const getOverviewComponents = () =>
  createPlateUI({
    [ELEMENT_H1]: withProps(StyledElement, {
      placeholder: 'H1',
      styles: {
        root: css`
          margin: 8px 0 8.5px;
          font-size: 1.38125rem;
          font-weight: 500;
          line-height: 1.53;
        `,
      },
    }),
    [ELEMENT_H2]: withProps(StyledElement, {
      styles: {
        root: css`
          margin: 8px 0 6.8px;
          font-size: 1.16875rem;
          font-weight: 500;
          line-height: 1.53;
        `,
      },
    }),
    [ELEMENT_H3]: withProps(StyledElement, {
      styles: {
        root: css`
          margin: 8px 0 6.8px;
          font-size: 1.0625rem;
          font-weight: 500;
          line-height: 1.53;
        `,
      },
    }),
  });

export const getOverviewEditableProps = () => ({
  spellCheck: false,
  autoFocus: false,
  placeholder: '💡 添加指标后点击“刷新”获取概览',
  style: {
    fontSize: '14px',
    fontWeight: 400,
  },
});

export const getOverviewPlugin = () => {
  const plugins = [
    createParagraphPlugin(),
    createListPlugin(), // 列表
    createIndentListPlugin(), // 列表支持缩进
    createIndentPlugin(OVERVIEW_CONFIG.indent), // 段落缩进
    createResetNodePlugin(OVERVIEW_CONFIG.resetBlockType),
    createSoftBreakPlugin(OVERVIEW_CONFIG.softBreak),
    createExitBreakPlugin(OVERVIEW_CONFIG.exitBreak),
    createAutoformatPlugin(OVERVIEW_CONFIG.autoformat),
    createLinkPlugin(OVERVIEW_CONFIG.link),
    createHeadingPlugin(), // heading elements
    createBoldPlugin(), // bold mark
    createItalicPlugin(), // italic mark
    createUnderlinePlugin(), // underline mark
    createStrikethroughPlugin(), // strikethrough mark
    createFontColorPlugin(),
    createFontBackgroundColorPlugin(),
    createFontSizePlugin(),
  ];
  return plugins;
};
