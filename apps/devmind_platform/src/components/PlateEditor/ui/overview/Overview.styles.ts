import { Space } from '@arco-design/web-react';
import styled from 'styled-components';

import { OVERVIEW_EDITOR_ID, EDITOR_ANCHOR_ATTR } from '@/constants';

interface WapperProps {
  focused: boolean;
  selected: boolean;
  freezed: boolean;
}

export const ReportH1Styled = styled.div`
  margin: 1em 0 10px;
  font-size: 1.625rem;
  font-weight: 500;
  line-height: 1.8;
`;

export const StyledWapper = styled.div.attrs({
  id: OVERVIEW_EDITOR_ID,
  [EDITOR_ANCHOR_ATTR]: OVERVIEW_EDITOR_ID,
  className: 'overviewEditor',
})`
  position: relative;
  width: 100%;
  background: ${(props: WapperProps) =>
    props.freezed
      ? 'rgba(242, 243, 245, 1)'
      : `linear-gradient(
    to right,
    rgba(229, 244, 255, 0.5),
    rgba(183, 221, 252, 0.5)
  )`};
  padding: 24px 32px;
  font-weight: 400;
  border-radius: 4px;
  margin: 16px 0px;
  box-shadow: ${(props: WapperProps) =>
    props.focused || props.selected ? 'rgb(59 130 249) 0px 0px 0px 1px;' : ''};

  ol {
    list-style-type: decimal;
    ol {
      list-style-type: lower-alpha;
      ol {
        list-style-type: lower-roman;
        ol {
          list-style-type: decimal;
          ol {
            list-style-type: lower-alpha;
            ol {
              list-style-type: lower-roman;
            }
          }
        }
      }
    }

    li {
      &::marker {
        @apply text-blue-500 font-medium;
        color: #5d87f7;
      }
    }
  }

  ul {
    list-style-type: disc;
    ul {
      list-style-type: circle;
      ul {
        list-style-type: square;
        ul {
          list-style-type: disc;
          ul {
            list-style-type: circle;
            ul {
              list-style-type: square;
            }
          }
        }
      }
    }

    li {
      &::marker {
        @apply text-blue-500 font-medium;
        color: #5d87f7;
      }
    }
  }

  .slate-ToolbarButton-active,
  .slate-ToolbarButton:hover {
    svg {
      color: #06c;
    }
  }
`;

export const StyledAction = styled(Space)`
  position: absolute;
  transform: translateY(-100%);
  top: -8px;
  right: 0;
`;

export const HeadingToolbar = styled.div`
  display: flex;
  align-items: center;
  user-select: none;
  min-height: 40px;
  flex-wrap: wrap;
  border-bottom: 1px solid;
  border-color: #c9cdd4;
  padding-bottom: 1em;
  margin-bottom: 1em;
`;
