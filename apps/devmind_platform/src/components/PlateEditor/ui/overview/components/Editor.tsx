import React from 'react';

import { createPlugins, Plate } from '@udecode/plate';

import { OVERVIEW_EDITOR_ID } from '@/constants';
// import { getOverviewPlugin } from '@/components/PlateEditor/config/plugins';

import { EditorRefEffect } from '../../../components/EditorRefEffect';
import {
  getOverviewComponents,
  getOverviewEditableProps,
  getOverviewPlugin,
} from '../config';
import { HeadingToolbar } from '../Overview.styles';
import { EditorProps } from '../Overview.types';

import { ToolbarButtons } from './Toolbar';

export const Editor = (props: EditorProps) => {
  const { initialValue, readOnly, onChange, onKeyDown } = props;

  const { plugins, editableProps } = React.useMemo(
    () => ({
      plugins: createPlugins(getOverviewPlugin(), {
        components: getOverviewComponents(),
      }),
      editableProps: getOverviewEditableProps(),
    }),
    [],
  );

  return (
    <Plate
      plugins={plugins}
      id={OVERVIEW_EDITOR_ID}
      editableProps={{
        ...editableProps,
        readOnly,
        className: 'overviewEditor',
        onKeyDown,
      }}
      initialValue={initialValue}
      onChange={onChange}>
      {readOnly ? null : (
        <HeadingToolbar>
          <ToolbarButtons />
        </HeadingToolbar>
      )}

      <EditorRefEffect id={OVERVIEW_EDITOR_ID} />
    </Plate>
  );
};
