import React, { FC } from 'react';

import {
  IconUnderline,
  IconItalic,
  IconBold,
  IconFontColors,
  IconBgColors,
  IconOrderedList,
  IconUnorderedList,
  IconProps,
  IconCheck,
  IconStrikethrough,
  IconToRight,
  IconToLeft,
} from '@arco-design/web-react/icon';
import { TippyProps } from '@tippyjs/react';
import {
  MarkToolbarButton,
  BalloonToolbar,
  getPluginType,
  usePlateEditorRef,
  MARK_BOLD,
  MARK_STRIKETHROUGH,
  MARK_ITALIC,
  MARK_UNDERLINE,
  MARK_COLOR,
  MARK_BG_COLOR,
  ColorPickerToolbarDropdown,
  ListToolbarButton,
  ELEMENT_OL,
  ELEMENT_UL,
  outdent,
  indent,
  // TODO: 增加 indentList/outdentList 逻辑
  getPreventDefaultHandler,
  ToolbarButton,
} from '@udecode/plate';

import { OVERVIEW_EDITOR_ID } from '@/constants';

type BaseToolbarConfig = {
  key: string;
  icon: React.ForwardRefExoticComponent<
    IconProps & React.RefAttributes<unknown>
  >;
  content: React.ReactNode;
};

const sanitizedHTMLContent = (title: string, description?: string) => (
  <>
    {title}
    {description ? (
      <>
        <br />
        {description}
      </>
    ) : null}
  </>
);

const TOOLBAR_LIST_CONFIG: BaseToolbarConfig[] = [
  {
    key: ELEMENT_OL,
    icon: IconOrderedList,
    content: sanitizedHTMLContent('有序列表', 'Markdown: 1. 空格'),
  },
  {
    key: ELEMENT_UL,
    icon: IconUnorderedList,
    content: sanitizedHTMLContent('无序列表', 'Markdown: - 空格 或 * 空格'),
  },
];

const TOOLBAR_INDENT_CONFIG: (BaseToolbarConfig & {
  handler: any;
})[] = [
  {
    key: 'indent',
    handler: indent,
    icon: IconToRight,
    content: sanitizedHTMLContent('增加缩进', 'Tab'),
  },
  {
    key: 'outdent',
    handler: outdent,
    icon: IconToLeft,
    content: sanitizedHTMLContent('减少缩进', 'Shift + Tab'),
  },
];

const TOOLBAR_MARK_CONFIG: BaseToolbarConfig[] = [
  {
    key: MARK_BOLD,
    icon: IconBold,
    content: sanitizedHTMLContent('粗体（⌘ + B）', 'Markdown: **文本** 空格'),
  },
  {
    key: MARK_STRIKETHROUGH,
    icon: IconStrikethrough,
    content: sanitizedHTMLContent('删除线', 'Markdown: ~~文本~~ 空格'),
  },
  {
    key: MARK_ITALIC,
    icon: IconItalic,
    content: sanitizedHTMLContent('斜体（⌘ + I）', 'Markdown: *文本* 空格'),
  },
  {
    key: MARK_UNDERLINE,
    icon: IconUnderline,
    content: sanitizedHTMLContent('下划线（⌘ + U）', 'Markdown: ~文本~ 空格'),
  },
];

const TOOLBAR_COLOR_PICKER_CONFIG: BaseToolbarConfig[] = [
  {
    key: MARK_COLOR,
    content: '字体颜色',
    icon: IconFontColors,
  },
  {
    key: MARK_BG_COLOR,
    content: '背景颜色',
    icon: IconBgColors,
  },
];

const getTooltipConfig = (content: React.ReactNode): TippyProps => ({
  arrow: true,
  delay: 0,
  duration: [200, 0],
  allowHTML: true,
  hideOnClick: false,
  offset: [0, 17],
  placement: 'top' as any,
  content,
});

// const iconStyles = { fontSize: 18, height: 24, width: 24 };

const ListToolbarButtons = () => {
  const editor = usePlateEditorRef(OVERVIEW_EDITOR_ID)!;

  return (
    <>
      {TOOLBAR_LIST_CONFIG.map(item => {
        const Icon = item.icon;
        return (
          <ListToolbarButton
            key={item.key}
            icon={<Icon />}
            tooltip={getTooltipConfig(item.content)}
            type={getPluginType(editor, item.key)}
          />
        );
      })}
    </>
  );
};

const IndentToolbarButtons = () => {
  const editor = usePlateEditorRef(OVERVIEW_EDITOR_ID)!;

  return (
    <>
      {TOOLBAR_INDENT_CONFIG.map(item => {
        const Icon = item.icon;

        return (
          <ToolbarButton
            key={item.key}
            tooltip={getTooltipConfig(item.content)}
            onMouseDown={
              editor && getPreventDefaultHandler(item.handler, editor)
            }
            icon={<Icon />}
          />
        );
      })}
    </>
  );
};

const ColorPickerToolbarButtons: FC<{ ballon?: boolean }> = ({
  ballon = false,
}) => {
  const editor = usePlateEditorRef(OVERVIEW_EDITOR_ID)!;

  return (
    <>
      {TOOLBAR_COLOR_PICKER_CONFIG.map(item => {
        const Icon = item.icon;
        return ballon ? (
          <MarkToolbarButton
            key={item.key}
            icon={
              <ColorPickerToolbarDropdown
                key={item.key}
                pluginKey={item.key}
                icon={<Icon />}
                selectedIcon={<IconCheck />}
              />
            }
            tooltip={getTooltipConfig(item.content)}
            type={getPluginType(editor, item.key)}
          />
        ) : (
          <ColorPickerToolbarDropdown
            key={item.key}
            pluginKey={item.key}
            icon={<Icon />}
            selectedIcon={<IconCheck />}
            tooltip={getTooltipConfig(item.content)}
          />
        );
      })}
    </>
  );
};

const BasicMarkToolbarButtons = () => {
  const editor = usePlateEditorRef(OVERVIEW_EDITOR_ID)!;

  return (
    <>
      {TOOLBAR_MARK_CONFIG.map(item => {
        const Icon = item.icon;
        return (
          <MarkToolbarButton
            key={item.key}
            icon={<Icon />}
            tooltip={getTooltipConfig(item.content)}
            type={getPluginType(editor, item.key)}
          />
        );
      })}
    </>
  );
};

export const MarkBallonToolbar = () => {
  return (
    <BalloonToolbar theme={'light'} arrow={false}>
      <BasicMarkToolbarButtons />
      <ColorPickerToolbarButtons ballon={true} />
    </BalloonToolbar>
  );
};

export const ToolbarButtons = () => {
  return (
    <>
      <ListToolbarButtons />
      <IndentToolbarButtons />
      <BasicMarkToolbarButtons />
      <ColorPickerToolbarButtons />
    </>
  );
};
