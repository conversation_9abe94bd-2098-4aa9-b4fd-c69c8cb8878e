import React from 'react';

import { But<PERSON>, Popconfirm, Tooltip } from '@arco-design/web-react';
import { IconRefresh } from '@arco-design/web-react/icon';

import { StyledAction } from '../Overview.styles';

export const Action = ({
  onRefresh,
  isEmpty,
}: {
  onRefresh?: () => void;
  isEmpty: boolean | null;
}) => {
  return (
    <StyledAction>
      {isEmpty ? (
        <Popconfirm
          title="刷新后，当前已编辑概览内容会被覆盖，请确认是否要继续刷新?"
          onOk={onRefresh}>
          <Tooltip content="根据指标卡自动生成结论">
            <Button
              type="secondary"
              icon={<IconRefresh color="#4E5969" />}
              size="mini"
            />
          </Tooltip>
        </Popconfirm>
      ) : (
        <Button
          type="secondary"
          icon={<IconRefresh color="#4E5969" />}
          onClick={onRefresh}
          size="mini"
        />
      )}
    </StyledAction>
  );
};
