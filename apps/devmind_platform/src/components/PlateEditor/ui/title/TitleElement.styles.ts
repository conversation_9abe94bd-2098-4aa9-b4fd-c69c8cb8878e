import styled from 'styled-components';

import { Status, StatusColor } from './types';

export const StyledPlaceholder = styled.span`
  color: #c9cdd4;
  font-size: 32px;
  font-weight: 500;
  letter-spacing: 0em;
  text-align: left;
  pointer-events: none;
  user-select: none;
`;

export const StyledSuffix = styled.span`
  user-select: none;
  pointer-events: none;
  color: ${(props: { focused: boolean }) =>
    props?.focused ? '#C9CDD4' : '#222'};
  padding-left: 8px;
`;

export const StyledTitle = styled.div`
  color: #1d2129;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const StyledFlex = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const StyledTitleLine = styled.div`
  border-bottom: 1px solid #e5e6eb;
  padding-bottom: 32px;
`;

export const StyledMessage = styled.div`
  font-size: 13px;
  font-weight: 400;
  color: ${(props: { status?: Status }) =>
    StatusColor[props.status || Status.Info]};
`;

export const StyledPopup = styled.div`
  padding: 0;
  text-align: left;
  background-color: var(--color-bg-popup);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
`;

export const StyledPopupTitle = styled.div`
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  padding: 21px 20px 0;
  /* identical to box height, or 150% */

  /* Text/05-1D2129-正文 标题 */
  text-align: left;
  color: #1d2129;
`;
