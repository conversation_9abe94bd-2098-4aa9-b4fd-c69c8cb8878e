export interface TitlePlugin {
  /**
   * An optional method that will upload the image to a server.
   * The method receives the base64 dataUrl of the uploaded image, and should return the URL of the uploaded image.
   */
}

export enum Status {
  Error = 'error',
  Info = 'info',
  Warning = 'warning',
  Tips = 'tips',
}

export const StatusColor = {
  [Status.Error]: '#F53F3F',
  [Status.Info]: '#86909C',
  [Status.Warning]: '#FF7D00',
  [Status.Tips]: '#86909C',
};
