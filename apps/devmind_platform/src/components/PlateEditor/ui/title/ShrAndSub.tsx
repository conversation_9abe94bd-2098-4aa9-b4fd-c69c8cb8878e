import { useEffect } from 'react';

import { Button, Message, Space, Tooltip } from '@arco-design/web-react';
import { IconSubscribeAdd, IconSubscribed } from '@arco-design/web-react/icon';
import { useBoolean, useRequest } from '@byted/hooks';
import { useModel } from '@jupiter/plugin-runtime/model';

import {
  reportTemplateServiceClient,
  SubscribeReportReq,
  SubscribeReportRes,
} from '@quality/common-apis/src/apis/api/insight_report/report';

import { API_V1 } from '@/constants';
import { useReportConfigUrlQuery } from '@/hooks/url';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import globalModel from '@/model/global';
import { inDashboardUrl } from '@/utils/url';

interface IProps {
  templateId?: string;
  reportName?: string;
  hasEditAuthority?: boolean;
}
export default (props: IProps) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { templateId, hasEditAuthority, reportName } = props;
  const { virtualSpaceKey } = useVirtualSpaceType();
  const {
    urlQuery: { nodeId },
  } = useReportConfigUrlQuery();
  const [{ userInfo }] = useModel([
    globalModel,
    state => ({
      userInfo: state.userInfo,
    }),
  ]);

  const {
    run: getSubscribe,
    loading: subcribeLoading,
    data: subscribeId,
  } = useRequest(async () => {
    // 如果是仪表盘不需要订阅功能
    if (!templateId) {
      return;
    }
    const res = reportTemplateServiceClient.GetSubscribe({
      version: API_V1,
      template_id: templateId,
      subscriber: userInfo?.username || '',
    });
    return (await res).data;
  });

  useEffect(() => {
    getSubscribe();
  }, []);

  const handleSubscribe = () => {
    // 如果是仪表盘不需要订阅功能
    if (!templateId) {
      return;
    }
    const params: SubscribeReportReq = {
      subscriber: userInfo?.username || '',
      /** 模版id */
      template_id: templateId,
      /** 订阅id */
      subscribe_id: subscribeId || '',
      version: API_V1,
      space_type: virtualSpaceKey,
      node_id: nodeId,
    };
    reportTemplateServiceClient
      .SubscribeReport(params)
      .then((res: SubscribeReportRes) => {
        if (res.code !== 200) {
          Message.error(res.msg);
        } else {
          if (subscribeId) {
            Message.success(`已取消订阅`);
          } else {
            Message.success(`订阅成功，可在我的工作台查看已订阅报告`);
          }
          getSubscribe();
        }
      });
  };

  const {
    // state: hover,
    setTrue: setHover,
    setFalse: setNone,
  } = useBoolean(false);

  return (
    <Space>
      {!inDashboardUrl() && (
        <Tooltip
          content={
            subscribeId ? '取消订阅' : '订阅后，可在我的工作台查看已订阅报告'
          }>
          <Button
            icon={
              subscribeId ? (
                <IconSubscribed style={{ fontSize: 16 }} />
              ) : (
                <IconSubscribeAdd style={{ fontSize: 16 }} />
              )
            }
            loading={subcribeLoading}
            onMouseEnter={setHover}
            onMouseLeave={setNone}
            onClick={handleSubscribe}
            // disabled={!hasEditAuthority}
          />
        </Tooltip>
      )}
    </Space>
  );
};
