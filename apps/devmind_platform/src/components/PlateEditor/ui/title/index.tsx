import { removeNodes } from '@udecode/plate';
import {
  createPluginFactory,
  deleteText,
  getLastChildPath,
  getNodeString,
  PlateEditor,
  TNode,
  TNodeEntry,
  Value,
} from '@udecode/plate-core';

import { TITLE_LIMIT } from './TitleElement';
import { TitlePlugin } from './types';

export const ELEMENT_TITLE = 'title';

/**
 * Enables support for images.
 */
export const createTitlePlugin = createPluginFactory<TitlePlugin>({
  key: ELEMENT_TITLE,
  isElement: true,
  isVoid: false,
  withOverrides: <
    V extends Value = Value,
    E extends PlateEditor<V> = PlateEditor<V>,
  >(
    editor: E,
    { type },
  ) => {
    const { normalizeNode } = editor;
    editor.normalizeNode = <N extends TNode>(entry: TNodeEntry<N>) => {
      const [node] = entry;
      if (editor.children.length > 1) {
        removeNodes(editor, {
          at: [],
          mode: 'highest',
          match: (_node, path) => path[0] > 0,
        });
      }
      if (node.type === type) {
        const length = getNodeString(node).length;
        const path = getLastChildPath(entry);
        if (length > TITLE_LIMIT) {
          try {
            deleteText(editor, {
              at: { path, offset: TITLE_LIMIT },
              unit: 'character',
              distance: length - TITLE_LIMIT,
            });
          } catch (error) {
            console.log(error);
          }
        }
      }

      return normalizeNode(entry);
    };

    return editor;
  },
});
