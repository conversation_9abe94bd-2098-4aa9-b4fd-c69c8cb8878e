import {
  PlateRenderElementProps,
  getRootProps,
  TElement,
  focusEditor,
  getEndPoint,
  getStartPoint,
  setSelection,
} from '@udecode/plate';
import {
  getNodeString,
  isElementEmpty,
  useEditorState,
  Value,
} from '@udecode/plate-core';
import { useFocused } from 'slate-react';

import FreezeList from '@/components/FreezeList';
import {
  getReportNameSuffix,
  TimeCycleText,
  TimeGranularityText,
} from '@/components/TimeFilter';
import {
  getTimeCycle,
  getTimeGranularity,
} from '@/components/TimeFilter/utils';
import { EDITOR_MODE } from '@/constants';
import { useReportConfigUrlQuery } from '@/hooks/url';

import {
  StyledFlex,
  StyledMessage,
  StyledPlaceholder,
  StyledSuffix,
} from './TitleElement.styles';
import { Status } from './types';

export const TITLE_LIMIT = 50;

const getMessage = (text: string) => {
  try {
    if (text?.length <= 0) {
      return { text: '标题不能为空', status: Status.Error };
    } else if (text?.length < TITLE_LIMIT) {
      return {
        text: `已经输入${text.length} / ${TITLE_LIMIT}字`,
        status: Status.Info,
      };
    } else if (text?.length === TITLE_LIMIT) {
      return {
        text: `最多可以输入${TITLE_LIMIT}字`,
        status: Status.Warning,
      };
    }
  } catch (error) {}
  return {
    text: `标题字数在0-${TITLE_LIMIT}个字`,
    status: Status.Info,
  };
};

export const TitleElement = <
  V extends Value = Value,
  N extends TElement = TElement,
>(
  props: PlateRenderElementProps<V, N>,
) => {
  // const { templateId } = useParams<{ templateId: string }>();

  const { urlQuery } = useReportConfigUrlQuery();

  const { mode, range = [] } = urlQuery;

  const freezed = Boolean(urlQuery.freezeVersion);

  const readOnly = mode !== EDITOR_MODE.Write || freezed;

  const { attributes, children, nodeProps, element } = props;

  const granularity = getTimeGranularity(
    urlQuery.granularity,
    urlQuery.granularity,
  );
  const cycle = getTimeCycle(urlQuery.cycle, urlQuery.cycle);

  // const { run: fetchSubTree } = useRequest(
  //   async () => {
  //     const res = await reportTemplateServiceClient.GetSubReport({
  //       version: API_V1,
  //       template_id: templateId,
  //       space_type: spaceType,
  //       time_range: { start_time: range[0], end_time: range[1] },
  //     });
  //     return res.data ?? [];
  //   },
  //   { manual: true },
  // );
  // useEffect(() => {
  //   fetchSubTree();
  // }, [templateId]);

  const editor = useEditorState();
  const rootProps = getRootProps(props);
  const isEmptyBlock = isElementEmpty(editor, element);
  const focused = useFocused();

  const content = getNodeString(element);

  const msg = getMessage(content);

  return (
    <div {...attributes} {...rootProps} id="report-title">
      <StyledFlex>
        <span
          style={{
            marginRight: 8,
            fontSize: 32,
            fontWeight: 500,
          }}>
          <span {...nodeProps}>
            <div
              style={{
                display: isEmptyBlock ? 'inline-block' : 'inline',
                minWidth: 1,
              }}>
              {children}
            </div>
          </span>
          <span
            contentEditable={false}
            onDoubleClick={e => {
              focusEditor(editor);
              const end = getEndPoint(editor, [0]);
              const start = getStartPoint(editor, [0]);
              setSelection(editor, {
                anchor: start,
                focus: end,
              });
              e.preventDefault();
              e.stopPropagation();
            }}
            onClick={e => {
              focusEditor(editor);
              const point = getEndPoint(editor, [0]);
              setSelection(editor, {
                anchor: point,
                focus: point,
              });
              e.preventDefault();
              e.stopPropagation();
            }}>
            {isEmptyBlock && !focused && (
              <StyledPlaceholder contentEditable={false}>
                请输入标题
              </StyledPlaceholder>
            )}
            <StyledSuffix focused={!readOnly} contentEditable={false}>
              {getReportNameSuffix(
                TimeGranularityText[granularity],
                TimeCycleText[cycle],
                range,
              )}
            </StyledSuffix>

            <FreezeList />
          </span>
        </span>

        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
          contentEditable={false}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}>
          {/* <Space>
            {!isEmbededIframe() && (
              <DomainMeasureObjFilter
                value={{
                  measureObjId: domainMeasureObjId,
                  filter: domainMeasureObjFilter,
                }}
                nodeId={nodeId}
                spaceType={spaceType}
                timeRange={range}
                domainMeasureObjList={domainMeasureObjList}
                metricInfoList={[...metricList, ...parentMetricList].map(
                  ({ metaMetric, metaInfo, complexMetric }) => ({
                    metricId: metaInfo.metric_id,
                    metricType: metaInfo.metric_type,
                    modelId:
                      (metaMetric?.detail?.model_id ||
                        complexMetric?.detail?.id) ??
                      '',
                    domainMeasureObjs:
                      (metaMetric?.detail?.domain_measure_objs ||
                        complexMetric?.detail?.domain_measure_objs) ??
                      [],
                  }),
                )}
                getPopupContainer={() =>
                  document.getElementById('report-title') ?? document.body
                }
                onChange={handleDomainMeasureFilterChange}
              />
            )}
            {!toggle && !isEmbededIframe() && (
              <Space>
                <Tooltip content={freezed && '当前报告已封板，无法执行此操作'}>
                  <Button
                    disabled={freezed}
                    type="secondary"
                    onClick={() => {
                      handleTimeChange({
                        range: lastTime,
                        granularity,
                        cycle,
                      });
                    }}>
                    <IconLeft />上{TimeGranularityText[granularity]}
                  </Button>
                </Tooltip>
                <Tooltip content={freezed && '当前报告已封板，无法执行此操作'}>
                  <Button
                    disabled={isDisabledDM(nextTime) || freezed}
                    type="secondary"
                    onClick={() => {
                      handleTimeChange({
                        range: nextTime,
                        granularity,
                        cycle,
                      });
                    }}>
                    下{TimeGranularityText[granularity]} <IconRight />
                  </Button>
                </Tooltip>
              </Space>
            )}
            {!isEmbededIframe() && (
              <Button
                type="secondary"
                onClick={() => setTogggle(!toggle)}
                disabled={freezed}>
                {!toggle ? (
                  '自定义'
                ) : (
                  <span>
                    <IconLeft />
                    返回
                  </span>
                )}
              </Button>
            )}
            {toggle && (
              <TimeFilter
                disabled={freezed}
                hideTimeGranularity={true}
                value={{
                  granularity,
                  cycle,
                  range,
                }}
                tooltipContent={freezed ? '当前报告已封板，无法执行此操作' : ''}
                onChange={handleTimeChange}
              />
            )}
          </Space> */}
        </div>
      </StyledFlex>

      {!readOnly && (
        <StyledMessage contentEditable={false} status={msg.status}>
          {msg.text}
        </StyledMessage>
      )}
      {/* <Divider type="horizontal" /> */}
    </div>
  );
};
