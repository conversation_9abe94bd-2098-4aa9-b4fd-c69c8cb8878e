import * as React from 'react';

import {
  PlateEditor,
  ELEMENT_PARAGRAPH,
  getNodeString,
  isType,
  insertNodes,
  getEndPoint,
  focusEditor,
  setSelection,
  getStartPoint,
} from '@udecode/plate';
import clsx from 'clsx';

import { log } from '@quality/common-utils';

import { createSlateElement } from '@/components/PlateEditor/utils';
import { PARENT_EDITOR_ID } from '@/constants/editor';

interface BottomSpaceProps {
  id: string;
  editor: PlateEditor | null;
  readOnly: boolean;
}
export const BottomSpace: React.FC<BottomSpaceProps> = ({
  id,
  editor,
  readOnly = true,
}) => {
  if (!editor || id === PARENT_EDITOR_ID) {
    return null;
  }

  const onClick = event => {
    if (readOnly) {
      return;
    }

    const e = event as React.SyntheticEvent;
    e.preventDefault();
    e.stopPropagation();

    const end = editor.children[editor.children.length - 1];
    let endPoint = getEndPoint(editor, []);

    if (!end) {
      log.warn('bottom-space: end is null');
      return;
    }
    const isEmptyParagraph =
      isType(editor, end, ELEMENT_PARAGRAPH) && getNodeString(end) === '';

    if (!isEmptyParagraph) {
      insertNodes(editor, createSlateElement(), {
        at: endPoint,
      });
    }

    const startPoint = getStartPoint(editor, []);
    endPoint = getEndPoint(editor, []);
    focusEditor(editor);

    // FIXME: 这里故意先把光标移到其他地方（在此处是起始节点）再移到末尾，不然光标会消失，具体原因不明
    setSelection(editor, { anchor: startPoint, focus: startPoint });
    setSelection(editor, {
      anchor: endPoint,
      focus: endPoint,
    });
  };

  return (
    <div
      className={clsx('h-[200px]', {
        'cursor-text': !readOnly,
      })}
      onClick={onClick}
    />
  );
};
