import React from 'react';

import { StyledElementProps, TElement } from '@udecode/plate';

import { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { QueryRequest } from '@quality/common-apis/src/apis/api/query/query';
import { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';

import { MetricCardContainerChildrenProps } from './components/common/MetricCardContainer.type';

export { StatValue } from '@/utils/metrics';

export interface MetricContentProps {
  isMetaMetric: boolean;
  isComplexMetric: boolean;

  businessId: string;
  metricData: MetricMetaData | ComplexMetricMetaData;
  queryParams?: QueryRequest;
  queryData?: QueryData;
  loading: boolean;
  actions: React.ReactNode;
  refreshButton: React.ReactNode;
  thumbnailChart: React.ReactNode;

  supportDomainMeasureObjFilter?: boolean;
  hasPermission: boolean;

  expandDisabled?: boolean;
}

export interface DragItemNode {
  /**
   * Required to identify the node.
   */
  id: string;
  element: TElement;
  [key: string]: unknown;
}

export type DropLineDirection = '' | 'left' | 'right';

export type PlateStyledElementProps = Pick<
  StyledElementProps,
  'editor' | 'element'
>;

export interface MetricBaseProps
  extends Pick<
    MetricCardContainerChildrenProps,
    | 'metricInfo'
    | 'metricMeta'
    | 'metricQuery'
    | 'onDelete'
    | 'readonly'
    | 'fetchMetricMeta'
    | 'fetchMetricQuery'
    | 'grabber'
    | 'fullScreen'
  > {}
