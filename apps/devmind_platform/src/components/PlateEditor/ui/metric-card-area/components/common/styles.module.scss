$outline-color: #3b82f9;
$card-period-maxHeight: 429px;
$card-maxHeight: 337px;
$fill2: #f7f8fa;

@mixin cardContainerStyle {
  @apply max-h-[337px] p-6  border border-solid;
  border-color: #eaedf1;
  background-color: rgba($fill2, 0.4);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.cardContainer {
  @include cardContainerStyle;
  @apply flex flex-col;
  height: calc(#{$card-maxHeight} - 44px);
}

@mixin cardPeriodContainerStyle {
  @apply max-h-[429px] p-6  border border-solid;
  border-color: #eaedf1;
  background-color: rgba($fill2, 0.4);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.cardPeriodContainer {
  @include cardPeriodContainerStyle;
  @apply flex flex-col;
  height: calc(#{$card-period-maxHeight} - 44px);
}

.btnDelete {
  @apply cursor-pointer;
}

.grabber {
  cursor: grab;
}
.leftDropLine {
  position: relative;
  &::before {
    content: '';
    width: 1px;
    height: 100%;
    background-color: $outline-color;
    position: absolute;
    left: -5px;
    top: 0;
  }
}

.rightDropLine {
  position: relative;
  &::after {
    content: '';
    width: 1px;
    height: 100%;
    background-color: $outline-color;
    position: absolute;
    right: -5px;
    top: 0;
  }
}
