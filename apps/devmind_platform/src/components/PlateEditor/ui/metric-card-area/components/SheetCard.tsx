/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck

import { FC, useEffect, useState, useRef, useMemo, useCallback } from 'react';

import { Button, Empty, Select, Skeleton } from '@arco-design/web-react';
import { useRequest } from '@byted/hooks';
import { useModal } from '@ebay/nice-modal-react';
import { useModel } from '@jupiter/plugin-runtime/model';
import clsx from 'clsx';
import { noop } from 'lodash';

import { reportTemplateServiceClient } from '@quality/common-apis/src/apis/api/insight_report/report';
import { DrillWhere } from '@quality/common-apis/src/apis/api/query/query';
import { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import {
  MetricTypeMeta,
  MetricTypeComplexMeta,
} from '@quality/common-apis/src/apis/api/zepto/consts';
import { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import { CHART_TYPE, VisualPanel } from '@quality/common-components';

import { useLargeView } from '@/components/LargeViewModal';
import MetricDetailDrawer from '@/components/MetricDetailDrawer';
import DrillModal from '@/components/MetricDetailDrawer/components/DrillModal';
import { useQueryRequest } from '@/components/MetricDetailDrawer/hooks';
import { MetricData } from '@/components/MetricDetailDrawer/interface';
import {
  getAnalysisMetricIds,
  getMetricDrillConfig,
} from '@/components/MetricDetailDrawer/utils';
import MetricName from '@/components/MetricName';
import {
  getFormatTimeFilter,
  getTimeCycle,
  getTimeGranularity,
} from '@/components/TimeFilter';
import { API_V1, DND_DELIMITER, SELF_EDITOR_ID } from '@/constants';
import { FetchDataMart } from '@/events/report';
import useReportConfigUrlQuery from '@/hooks/url/useReportConfigUrlQuery';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { reportModel } from '@/model/report';
import { MetricInfo } from '@/model/report/types';
import { ReportManager } from '@/modules/report';
import { useEditorReadonly } from '@/pages/dashboard/[templateId]/hooks/useEditorReadonly';

import { useDndNode } from './hooks/useDndNode';
import { MetricContainer } from './MetricCard.styles';
import style from './SheetCard.module.scss';
import { CardActions } from './XYCard';

interface SheetCardProps {
  editorID: string;
  readOnly: boolean;
  metricInfo: MetricInfo;
  parentId: string;
  index: number;
}

export const SheetCard: FC<SheetCardProps> = props => {
  const {
    readOnly,
    editorID,
    metricInfo: { metric_id, metric_type, display_type },
    parentId,
    index,
  } = props;

  const { virtualItem, virtualSpaceKey } = useVirtualSpaceType();
  const spaceId = virtualItem?.SpaceId ?? '';

  const [hasPermission, setHasPermission] = useState(true);
  const [metaMetricData, setMetaMetricData] = useState<MetricMetaData | null>(
    null,
  );
  const [complexMetricData, setComplexMetricData] =
    useState<ComplexMetricMetaData | null>(null);
  const [loading, setLoading] = useState(true);

  const [dimensionId, setDimensionId] = useState<string>('');

  const [
    {
      timeFilterInfo: timeFilterInfoFromModel,
      loading: reportTemplateLoading,
      fullScreen,
    },
    { putMetricInfo },
  ] = useModel([
    reportModel,
    state => ({
      loading: state.loading,
      fullScreen: state.fullScreen,
      timeFilterInfo: state.$timeFilterValue,
    }),
  ]);

  const nodeRef = useRef<HTMLDivElement>(null);

  const dragOptions = useMemo(
    () => ({
      canDrag: !readOnly,
    }),
    [readOnly],
  );

  const dropOptions = useMemo(
    () => ({
      canDrop: () => !readOnly,
    }),
    [readOnly],
  );

  const { dropLine, isDragging, dragRef, onDragStart, onDragEnd } = useDndNode({
    id: [editorID, parentId, metric_id, display_type, index].join(
      DND_DELIMITER,
    ),
    nodeRef,
    drag: dragOptions,
    drop: dropOptions,
  });

  let {
    urlQuery: {
      nodeId,
      granularity,
      cycle,
      range = [],
      domainMeasureObjFilter = [],
      domainMeasureObjId,
    },
  } = useReportConfigUrlQuery();

  let formatTimeFilterInfo = useMemo(
    () => getFormatTimeFilter({ granularity, cycle, range }),
    [granularity, cycle, ...range],
  );
  // 优先从 url 拿，兜底从 model 拿
  let timeFilterInfo = useMemo(() => {
    timeFilterInfoFromModel ?? formatTimeFilterInfo;
    // 优先使用model内的周期设置
    const cycle =
      timeFilterInfoFromModel?.cycle ??
      getTimeCycle(formatTimeFilterInfo.cycle, formatTimeFilterInfo.cycle);
    const granularity =
      timeFilterInfoFromModel?.granularity ??
      getTimeGranularity(
        formatTimeFilterInfo.granularity,
        formatTimeFilterInfo.granularity,
      );
    return {
      cycle,
      granularity,
      range: formatTimeFilterInfo?.range,
    };
  }, [formatTimeFilterInfo, timeFilterInfoFromModel]);

  const selfEditorReadOnly = useEditorReadonly(SELF_EDITOR_ID);

  const isMetaMetric = metric_type === MetricTypeMeta;
  const isComplexMetric = metric_type === MetricTypeComplexMeta;
  const metricData = isMetaMetric
    ? metaMetricData
    : isComplexMetric
    ? complexMetricData
    : null;
  const metricName = isMetaMetric
    ? (metricData as MetricMetaData)?.display_name
    : (metricData as ComplexMetricMetaData)?.name;

  const analysisMetricIds = getAnalysisMetricIds(metricData as any);

  const { urlQuery } = useReportConfigUrlQuery();

  const [timeRange, setTimeRange] = useState<string[]>([]); // 用户选的时间范围
  useEffect(() => {
    // const range = getDefaultTimeRange(
    //   timeFilterInfo.range,
    //   timeFilterInfo.granularity,
    // );
    setTimeRange(timeFilterInfo.range);
  }, [...timeFilterInfo.range]);

  // 是否支持领域度量对象筛选
  const supportDomainMeasureObjFilter =
    !domainMeasureObjId ||
    Boolean(
      (metricData?.domain_measure_objs ?? [])?.find(
        ({ id }) => id === domainMeasureObjId,
      )?.dimension_id,
    );

  // 初始化默认选中第一个对比维度
  useEffect(() => {
    const initDimensionId =
      metricData?.analysis_info?.analysis_dim_ids?.[0]?.dim_id;
    initDimensionId && setDimensionId(initDimensionId);
  }, [metricData?.analysis_info?.analysis_dim_ids]);

  const { refresh: refreshMetricData } = useRequest(
    async () => {
      const res =
        await reportTemplateServiceClient.GetMetricInfoAndExpertAnalysis({
          metric_id,
          metric_type,
          version: API_V1,
          space_type: virtualSpaceKey,
          project_id: nodeId,
          seal_version: urlQuery.freezeVersion,
        });

      if (res.data) {
        if (isMetaMetric) {
          const metricData = res.data.meta_metric_data!;
          setMetaMetricData(metricData);
          putMetricInfo({
            type: 'update',
            index,
            metricInfo: {
              editorID,
              metric_id,
              parent_id: parentId,
              metric_type,
              metaMetric: { detail: metricData },
            },
          });
        } else if (isComplexMetric) {
          const metricData = res.data.complex_metric_data!;
          setComplexMetricData(res.data.complex_metric_data!);
          putMetricInfo({
            type: 'update',
            index,
            metricInfo: {
              editorID,
              metric_id,
              parent_id: parentId,
              metric_type,
              complexMetric: { detail: metricData },
            },
          });
        }

        setHasPermission(Boolean(res.data.has_permission ?? true));
        setLoading(false);
      } else {
        setLoading(false);
      }
      ReportManager.getModule()!.metricReadyCount -= 1;
    },
    {
      ready: !reportTemplateLoading,
      auto: true,
      refreshDeps: [metric_id, metric_type, parentId, nodeId],
    },
  );

  const queryResponse = useQueryRequest(metricData as MetricData, {
    freezeVersion: urlQuery.freezeVersion,
  });
  const {
    run,
    dataSource: queryData,
    cancel,
    loading: queryLoading,
    params: queryParams,
    displayConfig,
    queryInfo,
  } = queryResponse;

  const fetchQueryInfo = useCallback(() => {
    if (
      hasPermission &&
      metricData &&
      timeFilterInfo &&
      timeRange.length &&
      dimensionId
    ) {
      // FIXME: 这个接口既臃肿又杂乱，需要重新设计

      const reportEvents = ReportManager.getModule()?.getReportEvents();

      reportEvents?.publish(new FetchDataMart(false));

      // 不支持领域度量对象的指标卡数据直接置空
      if (!supportDomainMeasureObjFilter) {
        putMetricInfo({
          type: 'update',
          index,
          metricInfo: {
            editorID,
            metric_id,
            parent_id: parentId,
            metric_type,
            [metric_type === MetricTypeMeta ? 'metaMetric' : 'complexMetric']: {
              queryData: {
                chart_data: null,
              },
            },
          },
        });
        reportEvents?.publish(new FetchDataMart(true));
        return;
      }

      run({
        chartType: CHART_TYPE.SHEET,
        nodeId,
        measureDimensionId: dimensionId,
        timeFilterInfo: {
          ...timeFilterInfo,
          range: timeRange,
        },
        domainMeasureObjFilter: {
          measureObjId: domainMeasureObjId,
          filter: domainMeasureObjFilter,
        },
        spaceId,
      })
        .then(res => {
          const queryData = res?.dataSource;
          /* 更新分析结论 */
          if (queryData) {
            putMetricInfo({
              type: 'update',
              index,
              metricInfo: {
                editorID,
                metric_id,
                parent_id: parentId,
                metric_type,
                [metric_type === MetricTypeMeta
                  ? 'metaMetric'
                  : 'complexMetric']: {
                  queryData,
                },
              },
            });
          }
        })
        .finally(() => {
          reportEvents?.publish(new FetchDataMart(true));
        });
    }

    return () => cancel();
  }, [
    parentId,
    metricData,
    hasPermission,
    timeFilterInfo,
    supportDomainMeasureObjFilter,
    domainMeasureObjId,
    domainMeasureObjFilter?.sort()?.join(),
    dimensionId,
    timeRange,
    spaceId,
  ]);

  useEffect(fetchQueryInfo, [fetchQueryInfo]);

  const onDelete = e => {
    e.stopPropagation();
    putMetricInfo({
      type: 'delete',
      metricInfo: props.metricInfo,
      index,
    });
  };

  const drillModal = useModal(DrillModal);

  // 查看明细下钻
  const handleDrill = (drillWhere: DrillWhere) => {
    drillModal.show({
      nodeId,
      queryParams,
      drillWhere,
      drillConfig: getMetricDrillConfig(metricData as any),
    });
  };

  const { open: openDrawer } = MetricDetailDrawer.useToggle();

  const { expandButton } = useLargeView({
    title: (
      <MetricName
        name={metricName}
        owners={
          isMetaMetric
            ? (metricData as MetricMetaData)?.owners ?? []
            : (metricData as ComplexMetricMetaData)?.common_data.owner
            ? [(metricData as ComplexMetricMetaData).common_data.owner!]
            : []
        }
        description={metricData?.description ?? ''}
        metricInfo={metricData?.metric_info}
        metricAttribute={metricData?.metric_attribute}
        iconSize={20}
      />
    ),
    content: (
      <div className={clsx(style.content, { fullScreenTable: fullScreen })}>
        <VisualPanel
          loading={queryLoading}
          drillWhere={[]}
          onDrillBack={noop}
          hasMenuMetrics={[]}
          timeGranularitys={[]}
          dimensionConf={{}}
          displayConfig={displayConfig}
          dataSource={queryData}
          query_info={queryInfo}
          onDrill={noop}
        />
      </div>
    ),
  });

  return (
    <div
      id={`${display_type}-${metric_id}`}
      ref={nodeRef}
      style={{
        opacity: isDragging ? 0.5 : 1,
        ...(fullScreen
          ? {
              cursor: 'default',
              border: 0,
              backgroundColor: 'rgba(255, 255, 255, 0.04)',
            }
          : {}),
      }}
      className={clsx(style.container, {
        [style.MetricCardLeftDropLine]: dropLine === 'left',
        [style.MetricCardRightDropLine]: dropLine === 'right',
      })}
      onClick={() =>
        hasPermission &&
        supportDomainMeasureObjFilter &&
        metricData &&
        !fullScreen &&
        openDrawer({
          initialMetric: {
            id: metricData.id,
            name: metricName,
            type: isMetaMetric ? MetricTypeMeta : MetricTypeComplexMeta,
          },
          initialNodeId: nodeId,
          initialTime: timeFilterInfo,
          initialDomainMeasureObjFilter: {
            measureObjId: domainMeasureObjId,
            filter: domainMeasureObjFilter,
          },
        })
      }>
      <Skeleton text={{ rows: 5 }} loading={loading} animation>
        {(isMetaMetric && metaMetricData) ||
        (isComplexMetric && complexMetricData) ? (
          <>
            <div className={style.header}>
              <div className={style.title}>
                <MetricName
                  className="max-w-[180px] text-sm font-medium"
                  ellipsis
                  name={metricName}
                  owners={
                    isMetaMetric
                      ? (metricData as MetricMetaData)?.owners ?? []
                      : (metricData as ComplexMetricMetaData)?.common_data.owner
                      ? [
                          (metricData as ComplexMetricMetaData).common_data
                            .owner!,
                        ]
                      : []
                  }
                  description={metricData?.description ?? ''}
                  metricInfo={metricData?.metric_info}
                  metricAttribute={metricData?.metric_attribute}
                />
              </div>
              {readOnly && expandButton}
              <CardActions
                onDelete={onDelete}
                readOnly={readOnly}
                dragRef={dragRef}
                onDragEnd={onDragEnd}
                onDragStart={onDragStart}
                parentId={parentId}
                editorId={editorID}
                style={style}
              />
            </div>
            <div
              className={style.action}
              style={fullScreen ? { display: 'none' } : {}}
              onClick={e => e.stopPropagation()}>
              <Select
                className="w-32"
                value={dimensionId}
                onChange={setDimensionId}>
                {metricData?.analysis_info?.analysis_dim_ids?.map(
                  ({ dim_id, display_name }) => (
                    <Select.Option value={dim_id} key={dim_id}>
                      {display_name}
                    </Select.Option>
                  ),
                )}
              </Select>
            </div>
            <div
              className={clsx([
                queryLoading && 'h-40',
                fullScreen && 'fullScreenTable',
              ])}
              onClick={e => e.stopPropagation()}>
              <VisualPanel
                autoHeight
                loading={queryLoading}
                drillWhere={[]}
                onDrillBack={noop}
                hasMenuMetrics={[]}
                hideMenus={['指标详情分析']}
                timeGranularitys={[]}
                dimensionConf={{}}
                displayConfig={displayConfig}
                dataSource={queryData}
                query_info={queryInfo}
                analysisMetricIds={analysisMetricIds}
                chart_detail_info={
                  {
                    meta_drill_config: getMetricDrillConfig(metricData as any),
                  } as any
                }
                onAnalysis={noop}
                onDrill={handleDrill}
                fullScreen={fullScreen}
              />
            </div>
          </>
        ) : (
          <MetricContainer className="justify-center">
            <Empty
              description={
                <>
                  指标数据查询失败
                  <Button
                    className="px-1"
                    type="text"
                    onClick={refreshMetricData}>
                    刷新
                  </Button>
                </>
              }
            />
          </MetricContainer>
        )}
      </Skeleton>
    </div>
  );
};
