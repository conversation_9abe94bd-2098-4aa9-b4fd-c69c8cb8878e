$outline-color: #3b82f9;

.container {
  @apply col-span-1 p-5 cursor-pointer;
  height: 300px;
  background: rgba(247, 248, 250, 0.4);
  border: 1px solid #eaedf1;
  border-radius: 8px;
  box-sizing: border-box;
  &.MetricCardLeftDropLine {
    position: relative;
    &::before {
      content: '';
      width: 1px;
      height: 100%;
      background-color: $outline-color;
      position: absolute;
      left: -5px;
      top: 0;
    }
  }

  &.MetricCardRightDropLine {
    position: relative;
    &::after {
      content: '';
      width: 1px;
      height: 100%;
      background-color: $outline-color;
      position: absolute;
      right: -5px;
      top: 0;
    }
  }
  .header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 24px;
      .titleText {
        margin-bottom: 0;
        font-weight: 500;
        font-size: 14px;
        color: #1d252f;
        max-width: 200px;
      }
    }
    .action {
      @apply flex items-center gap-3;
    }
    .cardAction {
      @apply absolute right-0 flex items-center flex-shrink-0 gap-2
    bg-white rounded px-2 text-gray-500;

      height: 26px;
      border: 1px solid #e5e6eb;
      opacity: 0;

      .btnDelete {
        @apply cursor-pointer;
      }

      .grabber {
        cursor: grab;
      }
    }
  }
  .content {
    height: calc(100% - 24px - 24px);
  }

  &:hover {
    .cardAction {
      opacity: 1;
    }
  }
}
