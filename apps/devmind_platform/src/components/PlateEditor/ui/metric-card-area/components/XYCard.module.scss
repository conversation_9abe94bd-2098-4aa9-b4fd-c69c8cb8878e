$outline-color: #3b82f9;

.container {
  @apply col-span-3 p-5 cursor-pointer;
  min-height: 400px;
  background: rgba(247, 248, 250, 0.4);
  border: 1px solid #eaedf1;
  border-radius: 8px;
  &.MetricCardLeftDropLine {
    position: relative;
    &::before {
      content: '';
      width: 1px;
      height: 100%;
      background-color: $outline-color;
      position: absolute;
      left: -5px;
      top: 0;
    }
  }

  &.MetricCardRightDropLine {
    position: relative;
    &::after {
      content: '';
      width: 1px;
      height: 100%;
      background-color: $outline-color;
      position: absolute;
      right: -5px;
      top: 0;
    }
  }
  .header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 24px;
      .titleText {
        margin-bottom: 0;
        font-weight: 500;
        font-size: 14px;
        color: #1d252f;
        max-width: 200px;
      }
      .titleLink {
        font-size: 12px;
        color: #1664ff;
        cursor: pointer;
      }
    }

    .cardAction {
      @apply absolute right-0 flex items-center flex-shrink-0 gap-2
    bg-white rounded px-2 text-gray-500;

      height: 24px;
      border: 1px solid #e5e6eb;
      opacity: 0;

      .btnDelete {
        @apply flex items-center;
        @apply cursor-pointer;
      }

      .grabber {
        @apply flex items-center;
        cursor: grab;
      }
    }
  }
  .action {
    @apply flex items-center gap-3 mb-3;
  }
  .content {
    height: 286px;
  }

  &:hover {
    .cardAction {
      opacity: 1;
    }
  }
}
