import * as React from 'react';
import { useMemo, useState } from 'react';

import { Input, List, Select, Spin, Message } from '@arco-design/web-react';
import { IconSearch } from '@arco-design/web-react/icon';
import { useModel } from '@jupiter/plugin-runtime/model';
import { useDebounce } from 'ahooks';
import clsx from 'clsx';

import { MetaAndComplexMetricInfo } from '@quality/common-apis/src/apis/api/zepto/meta_metric';

import { getSelfMetricList } from '@/components/PlateEditor/utils/compare';
import UserAvatar from '@/components/UserAvatar';
import globalModel from '@/model/global';
import { reportModel } from '@/model/report';
import './MetricFilterList.scss';

import { PlateStyledElementProps } from '../types';

const ALL = 'all';

interface MetricFilterListProps extends PlateStyledElementProps {
  parentID: string;
  onClose: () => void;
}
export const MetricFilterList: React.FC<MetricFilterListProps> = ({
  element,
  parentID,
  editor,
  onClose,
}) => {
  const [{ configEnumInfo }] = useModel(globalModel);
  const [
    { list, effectState, _metricList, editorRefById, parentMetricList },
    { syncMetrics },
  ] = useModel([
    reportModel,
    state => ({
      list: state.metaAndComplexMetricList,
      effectState: state.metaAndComplexMetricListState,
      _metricList: state.$metricList,
      editorRefById: state.editorRefById,

      parentMetricList: state.parentMetricList,
    }),
  ]);

  const tempTotalMetricEntries = useMemo(
    () => [
      ..._metricList.map(item => item.metric_id),
      ...parentMetricList.map(item => item.metaInfo.metric_id),
    ],
    [_metricList, parentMetricList],
  );

  const [filteredType, setFilteredType] = useState<string>(ALL);
  const [filteredName, setFilteredName] = useState('');
  const debouncedFilterName = useDebounce(filteredName, { wait: 300 });

  const filteredList = useMemo<MetaAndComplexMetricInfo[]>(() => {
    return list.filter(
      item =>
        (filteredType === ALL || item.problem_region === filteredType) &&
        item.display_name
          .toLowerCase()
          .includes(debouncedFilterName.toLowerCase()) &&
        configEnumInfo.activeProblemRegionMaps
          .map(o => o.display_name)
          .includes(item.problem_region),
    );
  }, [filteredType, debouncedFilterName, list]);

  const filterOptions = useMemo(
    () => [
      { label: '全部', value: ALL },
      ...Array.from(new Set(list.map(({ problem_region }) => problem_region)))
        .filter(
          problem_region =>
            problem_region &&
            configEnumInfo.activeProblemRegionMaps
              .map(o => o.display_name)
              .includes(problem_region),
        )
        .map(problemRegion => ({
          label: problemRegion,
          value: problemRegion,
        })),
    ],
    [list],
  );

  const onClick = (item: MetaAndComplexMetricInfo, selected: boolean) => {
    if (selected) {
      return;
    }
    const newMetricList = getSelfMetricList(editorRefById);
    const sum = newMetricList.length + parentMetricList.length;
    if (sum > 84) {
      onClose();
      Message.error('报告最多支持添加85个指标卡片');
      return;
    }

    syncMetrics({
      index: 0,
      editor,
      element,
      type: 'insert',
      metric: {
        alias: item.display_name,
        metric_id: item.id,
        metric_type: item.type,
        parent_id: parentID,
      },
    });

    onClose();
  };

  return (
    <Spin className="MetricFilterList" loading={effectState.loading}>
      <div className="containerM">
        <div className="header">
          <div className="title">指标</div>
          <div className="filter-container">
            <Select value={filteredType} onChange={setFilteredType}>
              {filterOptions.map(({ label, value }) => (
                <Select.Option key={value} value={value}>
                  {label}
                </Select.Option>
              ))}
            </Select>

            <Input
              value={filteredName}
              onChange={setFilteredName}
              placeholder="请搜索指标"
              allowClear
              suffix={<IconSearch />}
            />
          </div>
        </div>

        <List
          className="metric-content"
          virtualListProps={{ height: 320, itemHeight: 62 }}
          dataSource={filteredList ?? []}
          render={item => {
            const selected = tempTotalMetricEntries.includes(item.id);

            return (
              <List.Item key={item.id} style={{ all: 'unset' }}>
                <div
                  className={clsx(`item-container`, {
                    'bg-gray-100': selected,
                    '!cursor-not-allowed': selected,
                  })}
                  onClick={() => onClick(item, selected)}>
                  <div className="content">
                    <div className="text-sm font-medium truncate max-w-[250px]">
                      {item.display_name}
                    </div>
                    <div className="text-xs font-normal text-gray-500">
                      {item.problem_region}
                    </div>
                  </div>

                  <UserAvatar username={item.owners_cn?.[0]} />
                </div>
              </List.Item>
            );
          }}
        />
      </div>
    </Spin>
  );
};
