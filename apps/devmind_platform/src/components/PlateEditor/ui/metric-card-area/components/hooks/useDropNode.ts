import { useModel } from '@jupiter/plugin-runtime/model';
import { PlateEditor, TElement } from '@udecode/plate';
import { DropTargetHookSpec, useDrop } from 'react-dnd';

import { reportModel } from '@/model/report';

import { DragItemNode, DropLineDirection } from '../../types';
import { getHoverDirection, getNewDirection } from '../../utils';

export interface UseDropNodeOptions
  extends DropTargetHookSpec<DragItemNode, unknown, { isOver: boolean }> {
  /**
   * The reference to the node being dragged.
   */
  nodeRef: any;

  /**
   * Id of the node.
   */
  id: string;

  /**
   * Current value of dropLine.
   */
  dropLine: string;

  /**
   * Callback called on dropLine change.
   */
  onChangeDropLine: (newValue: DropLineDirection) => void;
}

export const useDropNode = (
  editor: PlateEditor,
  element: TElement,

  {
    accept,
    id,
    nodeRef,
    dropLine,
    onChangeDropLine,
    ...options
  }: UseDropNodeOptions,
) => {
  const [_, { moveMetric }] = useModel([reportModel, _ => ({})]);

  return useDrop<DragItemNode, { isDropNode: boolean }, { isOver: boolean }>(
    {
      ...options,
      drop: (dragItem, monitor) => {
        const direction = getHoverDirection({ dragItem, monitor, nodeRef, id });
        if (!direction) {
          return;
        }

        moveMetric({ direction, dragItem, editor, dropItem: { id, element } });

        // return isDropNode so that we can catch it in useDropContainer and stop the drop
        return {
          isDropNode: true,
        };
      },
      collect: monitor => ({
        isOver: monitor.isOver(),
      }),
      hover(item, monitor) {
        if (!monitor.canDrop()) {
          return;
        }

        const direction = getHoverDirection({
          dragItem: item,
          monitor,
          nodeRef,
          id,
        });
        const dropLineDir = getNewDirection(dropLine, direction);
        if (dropLineDir?.length) {
          onChangeDropLine(dropLineDir);
        }
      },
      accept,
    },
    [id, options, nodeRef],
  );
};
