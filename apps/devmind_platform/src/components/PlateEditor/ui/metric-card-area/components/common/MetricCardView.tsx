import * as React from 'react';

import { Empty } from '@arco-design/web-react';
import { IconDelete } from '@arco-design/web-react/icon';
import clsx from 'clsx';
import styled from 'styled-components';

import { reportDetailStoreUse } from '@/components/ReportDetail/store';

import styles from './styles.module.scss';

const iconSizeProps = {
  width: 18,
  height: 18,
};

interface CardContainerProps {
  className?: string;
  style?: object;
}

export const CardContainer: React.FC<CardContainerProps> = props => {
  const cycleComparisonState = reportDetailStoreUse.cycleComparisonState();
  return (
    <div
      className={clsx(
        cycleComparisonState
          ? styles.cardPeriodContainer
          : styles.cardContainer,
        props.className,
      )}
      style={props.style}>
      {props.children}
    </div>
  );
};

export const DeleteButton: React.FC<{ onClick: () => void }> = ({
  onClick,
}) => (
  <div
    className={styles.btnDelete}
    onClick={e => {
      e.stopPropagation();
      onClick();
    }}>
    <IconDelete style={iconSizeProps} />
  </div>
);

export const SCEmpty = styled(Empty)`
  &.arco-empty {
    padding: 0;
  }
  &.arco-empty .arco-empty-wrapper .arco-empty-image {
    font-size: 48px;
  }
`;
