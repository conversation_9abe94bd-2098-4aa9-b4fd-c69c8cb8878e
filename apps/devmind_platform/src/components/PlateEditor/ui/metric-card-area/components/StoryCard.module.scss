@import './common/styles.module.scss';

.btnExpand {
  :global(.arco-btn-secondary:not(.arco-btn-disabled)) {
    @apply h-11 rounded-b rounded-t-none border border-solid border-t-0;
    border-color: #eaedf1;
    background-color: rgba($fill2, 0.4)!important;
    min-width: 380px;

    &:hover {
      border-color: transparent;
      background-color: $fill2;
    }
  }
}

.expand {
  @apply max-h-[656px];
  height: calc(100% - 44px);
}

.noExpandBtn {
  height: 388px;
  border-radius: 8px !important;
}

.noExpandBtnFullscreen {
  height: 298px;
  border-radius: 8px !important;
}

.noPermission {
  height: 298px;
  border-radius: 8px !important;
}

.cardViewWrapper {
  :global(.actions-wrapper) {
    @apply absolute right-0 flex items-center flex-shrink-0 gap-2
    bg-white rounded px-2 text-gray-500;

    height: 26px;
    border: 1px solid #e5e6eb;
    opacity: 0;
  }

  &:hover {
    :global(.actions-wrapper) {
      opacity: 1;
    }
  }
}

.fullscreenContainer {
  @apply cursor-default border-0;
  background-color: rgba(255, 255, 255, 0.04);
  .headerTitle {
    @apply text-[#66FFF6];
    font-size: 24px;
    line-height: 32px;
  }
  .mainContent {
    @apply cursor-default;
  }
  .statDisplayContainer {
    .stateDisplayValue {
      font-size: 32px;
      @apply text-white;
    }

    .statDisplayUnit {
      font-size: 32px;
      @apply text-[#9DA3AF];
    }
  }
}

.mainContent {
  @apply flex flex-col cursor-pointer;
}

.header {
  @apply flex relative items-start text-sm font-medium break-all;
}

.headerTitle {
  @apply truncate;
}

.titleNoPermission {
  color: var(--measure-text2);
}

.tagContent {
  @apply mb-5;
  margin-top: 6px;
}

.statDisplayContainer {
  @apply inline-flex gap-2 items-end;
}

.stateDisplayValue {
  @apply text-3xl leading-none;
  font-family: 'byte-number-bold';
  transform: translateY(3.5px);
}

.statDisplayUnit {
  @apply text-xs text-[#4E5969] leading-5;
}

.statNoPermission {
  font-weight: 500;
  font-size: 24px;
  color: var(--measure-text3);
}

.advanced {
  @apply relative flex-1;
}
