$outline-color: #3b82f9;
$card-maxHeight: 337px;
$fill2: #f7f8fa;

@mixin cardContainerStyle {
  @apply max-h-[337px] p-[20px]  border border-solid;
  border-color: #eaedf1;
  background-color: rgba($fill2, 0.4);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

@mixin statDisplayContainerStyle {
  @apply inline-flex gap-2 items-end;
}

@mixin statDisplayValueStyle {
  @apply text-3xl leading-none;
  font-family: 'byte-number-bold';
  transform: translateY(3.5px);
}

.MetricCardArea {
  @apply flex flex-col mb-4 border-[#DEE0E3] border-solid;

  &.selected,
  &.hovered {
    box-shadow: 0 0 0 1px $outline-color;
  }

  .title-wrapper {
    @apply flex items-center text-base font-medium leading-[24px];
  }

  .content-wrap {
    @apply grid grid-cols-3 gap-4;
  }

  .iPhone-content-wrap {
    @apply grid grid-cols-2 gap-4;
  }

  .btn-add {
    @include cardContainerStyle;
    height: 300px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    background: #F6F8FA;
border: 1px dashed #DDE2E9;
border-radius: 8px;

  }
}

/* 指标卡相关样式 */
.metricCard {
  @apply col-span-1;
  .metricContainer {
    @include cardContainerStyle;
    @apply flex flex-col;
    min-width: 380px;
    height: calc(#{$card-maxHeight} - 44px);
    &.no-expand-btn,
    &.no-permission {
      height: 298px;
      border-radius: 8px !important;
    }

    &.expand {
      @apply max-h-[656px];
      height: calc(100% - 44px);
    }

    .header--title {
      @apply truncate;
    }

    &.no-permission {
      .header--title {
        color: var(--measure-text2);
      }
    }
  }


  &.MetricCardLeftDropLine {
    position: relative;
    &::before {
      content: '';
      width: 1px;
      height: 100%;
      background-color: $outline-color;
      position: absolute;
      left: -5px;
      top: 0;
    }
  }

  &.MetricCardRightDropLine {
    position: relative;
    &::after {
      content: '';
      width: 1px;
      height: 100%;
      background-color: $outline-color;
      position: absolute;
      right: -5px;
      top: 0;
    }
  }

  .actions-wrapper {
    @apply absolute right-0 flex items-center flex-shrink-0 gap-2
    bg-white rounded px-2 text-gray-500;

    height: 26px;
    border: 1px solid #e5e6eb;
    opacity: 0;

    .btn-delete {
      @apply cursor-pointer;
    }

    .grabber {
      cursor: grab;
    }
  }

  &:hover {
    .actions-wrapper {
      opacity: 1;
    }
  }

  .main-content {
    @apply flex flex-col cursor-pointer;
  }

  .tag-content {
    @apply mb-5;
    margin-top: 6px;
  }

  .advanced {
    @apply relative flex-1;
  }

  .stat-display-container {
    @include statDisplayContainerStyle;
    .stat-display-value {
      @include statDisplayValueStyle;
    }
    .no-permission {
      font-weight: 500;
      font-size: 24px;
      color: var(--measure-text3);
    }

    .stat-display-unit {
      @apply text-xs text-[#4E5969] leading-5;
    }
  }

  .btn-expand {
    @apply h-11 rounded-b rounded-t-none border border-solid border-t-0;
    border-color: #eaedf1;
    box-shadow: none !important;
    background-color: rgba($fill2, 0.4) !important;
    min-width: 380px;
  }
  .arco-btn.arco-btn-secondary:not(.arco-btn-disabled):not(.arco-btn-not-loading):hover {
    border-color: #eaedf1;
    box-shadow: none;
    background-color: var(--color-secondary-hover) !important;
  }
}
