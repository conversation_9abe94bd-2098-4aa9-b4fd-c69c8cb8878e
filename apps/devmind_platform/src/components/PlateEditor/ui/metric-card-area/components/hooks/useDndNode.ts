import { useState } from 'react';

import { PlateEditor, TElement } from '@udecode/plate-core';

import { DropLineDirection } from '../../types';

import { TYPE } from './constants';
import { useDragNode, UseDragNodeOptions } from './useDragNode';
import { useDropNode, UseDropNodeOptions } from './useDropNode';

export interface UseDndNodeOptions
  extends Pick<UseDropNodeOptions, 'id' | 'nodeRef'> {
  editor: PlateEditor;
  element: TElement;

  drag?: Partial<UseDragNodeOptions>;
  drop?: Partial<UseDropNodeOptions>;
  preview?: {
    /**
     * Whether to disable the preview.
     */
    disable?: boolean;

    /**
     * The reference to the preview element.
     */
    ref?: any;
  };
}

export const useDndNode = ({
  id,
  editor,
  element,

  nodeRef,
  drag: dragOptions,
  drop: dropOptions,
}: UseDndNodeOptions) => {
  const [dropLine, setDropLine] = useState<DropLineDirection>('');

  const [{ isDragging, onDragStart, onDragEnd }, dragRef, preview] =
    useDragNode(editor, element, {
      id,
      type: TYPE,
      ...dragOptions,
    });

  const [{ isOver }, drop] = useDropNode(editor, element, {
    accept: TYPE,
    id,
    nodeRef,
    dropLine,
    onChangeDropLine: setDropLine,
    ...dropOptions,
  });

  preview(drop(nodeRef));

  if (!isOver && dropLine) {
    setDropLine('');
  }

  return {
    isDragging,
    isOver,
    dropLine,
    dragRef,

    onDragStart,
    onDragEnd,
  };
};
