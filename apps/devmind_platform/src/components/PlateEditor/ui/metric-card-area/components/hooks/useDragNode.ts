import React from 'react';

import { PlateEditor, TElement } from '@udecode/plate';
import { DragSourceHookSpec, useDrag } from 'react-dnd';

import { METRIC_CARD_GRAGGING_SIGNAL } from '../../constants';
import { DragItemNode } from '../../types';

export interface UseDragNodeOptions
  extends DragSourceHookSpec<
    DragItemNode,
    unknown,
    {
      isDragging: boolean;
      onDragStart: React.DragEventHandler;
      onDragEnd: React.DragEventHandler;
    }
  > {
  id: string;
}

export const useDragNode = (
  editor: PlateEditor,
  element: TElement,
  { type, id, ...options }: UseDragNodeOptions,
) => {
  const onDragStart = React.useCallback(() => {
    editor[METRIC_CARD_GRAGGING_SIGNAL] = true;
  }, []);
  const onDragEnd = React.useCallback(() => {
    editor[METRIC_CARD_GRAGGING_SIGNAL] = false;
  }, []);

  return useDrag<
    DragItemNode,
    unknown,
    {
      isDragging: boolean;
      onDragStart: React.DragEventHandler;
      onDragEnd: React.DragEventHandler;
    }
  >(
    () => ({
      type,
      item(_monitor) {
        editor[METRIC_CARD_GRAGGING_SIGNAL] = true;
        return { id, element };
      },
      collect: monitor => ({
        isDragging: monitor.isDragging(),
        onDragStart,
        onDragEnd,
      }),
      end: () => {
        editor[METRIC_CARD_GRAGGING_SIGNAL] = false;
      },
      ...options,
    }),
    [id, options],
  );
};
