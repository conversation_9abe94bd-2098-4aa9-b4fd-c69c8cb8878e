import * as React from 'react';
import { useEffect, useCallback, useMemo, useRef, useState } from 'react';

import { IconDragDotVertical } from '@arco-design/web-react/icon';
import { useModel } from '@jupiter/plugin-runtime/model';
import { useUpdateEffect } from 'ahooks';
import clsx from 'clsx';
import { isEqual } from 'lodash';

import { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { DisplayTypeStory } from '@quality/common-apis/src/apis/api/base/display_config';
import { MetricInfoAndExpertAnalysisInfo } from '@quality/common-apis/src/apis/api/insight_report/report';
import { LoadingState } from '@quality/common-utils';

import { genMetricAnchorId } from '@/components/ReportCatalogue/helper';
import {
  DND_DELIMITER,
  EDITOR_ANCHOR_ATTR,
  METRIC_AREA_META_FIELD_NAME,
} from '@/constants';
import {
  MetricQueryFetchEvent,
  MetricSerialFetchEvent,
  QueryDataExtra,
} from '@/events/metric';
import { useReportConfigUrlQuery } from '@/hooks/url';
import { reportModel } from '@/model/report';
import { MetricInfoLite } from '@/model/report/types';
import { ReportManager } from '@/modules';
import { isMetaMetric } from '@/utils/metrics';

import { useDndNode } from '../hooks/useDndNode';

import {
  MetricCardContainerChildrenProps,
  MetricCardContainerProps,
} from './MetricCardContainer.type';
import styles from './styles.module.scss';

const iconSizeProps = {
  width: 18,
  height: 18,
};

export const MetricCardContainer: React.FC<MetricCardContainerProps> = ({
  editor,
  element,
  index,

  metric,
  long = false,
  readonly,
  metricSubject,
  children,
}) => {
  const { metric_id, display_type = DisplayTypeStory } = metric;
  let { urlQuery } = useReportConfigUrlQuery();

  const {
    range,
    cycle,
    nodeId,
    granularity,
    freezeVersion,
    domainMeasureObjFilter = [],
    domainMeasureObjId = '',
  } = urlQuery;

  const reportModule = ReportManager.getModule();
  const [{ metrics, templateInfo, fullScreen }, { syncMetrics }] = useModel([
    reportModel,
    state => ({
      templateInfo: state.$templateInfo,
      instanceInfo: state.$instanceInfo,

      metrics: state.metrics,
      fullScreen: state.fullScreen,
      // timeFilterInfo: state.$timeFilterValue,
    }),
  ]);
  const nodeRef = useRef<HTMLDivElement | null>(null);

  const [metricMeta, setMetricMeta] = useState({
    data: metrics?.get.metricModelMap().get(metric_id)?.data,
    loading:
      metrics?.get.metricModelMap().get(metric_id)?.loadingState ||
      LoadingState.NotStarted,
  });

  const [metricQuery, setMetricQuery] = useState(() => {
    const configSourece = metrics?.get
      .metricModelMap()
      ?.get(metric_id)
      ?.queryModel?.getConfigSource(display_type);

    return {
      data: configSourece?.data,
      loading: configSourece?.loadingState || LoadingState.NotStarted,
      extra: {
        metric,
        msg: configSourece?.msg,
        requestParams: configSourece?.requestPrams,
        timeFilterValue: configSourece?.timeFilterValue,
      } as QueryDataExtra | undefined,
    };
  });

  const onDelete = useCallback(() => {
    syncMetrics({ index, editor, element, type: 'delete', metric });
  }, [index, editor, element, metric]);

  const fetchMetricMeta: MetricCardContainerChildrenProps['fetchMetricMeta'] =
    () => {
      reportModule?.metricSerialFetchEvents.publish<MetricSerialFetchEvent>({
        type: MetricSerialFetchEvent.type,
        payload: { metric, urlQuery },
      });
    };

  const fetchMetricQuery: MetricCardContainerChildrenProps['fetchMetricQuery'] =
    (payload = {}) => {
      reportModule?.metricQueryFetchEvents.publish<MetricQueryFetchEvent>({
        type: MetricQueryFetchEvent.type,
        payload: { metric, urlQuery, ...payload },
      });
    };

  const dragOptions = useMemo(
    () => ({
      canDrag: !readonly,
    }),
    [readonly],
  );

  const dropOptions = useMemo(
    () => ({
      canDrop: () => !readonly,
    }),
    [readonly],
  );

  const { dropLine, isDragging, dragRef, onDragStart, onDragEnd } = useDndNode({
    editor,
    element,
    id: [metric_id, display_type, index].join(DND_DELIMITER),
    nodeRef,
    drag: dragOptions,
    drop: dropOptions,
  });

  const grabber = (
    <div
      className={styles.grabber}
      ref={dragRef}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}>
      <IconDragDotVertical style={{ ...iconSizeProps }} />
    </div>
  );

  /* 订阅 metric 数据更新 */
  useEffect(() => {
    let subject = metricSubject;

    const sub = subject?.subscribe(({ payload }) => {
      if (payload.dataType === 'metaData') {
        setMetricMeta({
          data: payload.data as MetricInfoAndExpertAnalysisInfo | undefined,
          loading: payload.loadingState,
        });
      } else if (payload.dataType === 'queryData') {
        /* 在仪表盘中，允许相同指标不同展示类型，因此还需要判断展示类型、展示维度是否一致 */
        const dashboard_display_type = (payload.extra?.requestParams as any)
          ?.dashboard_display_type;
        const existDisplayType = dashboard_display_type && display_type;
        const dashboard_dim_id = payload.extra?.metric.default_dim_id;
        const existDimId = dashboard_dim_id && metric.default_dim_id;

        if (
          (existDisplayType && dashboard_display_type !== display_type) ||
          (existDimId && dashboard_dim_id !== metric.default_dim_id)
        ) {
          return;
        }

        setMetricQuery({
          data: payload.data as QueryData | undefined,
          loading: payload.loadingState,
          extra: payload.extra,
        });
      }
    });

    return () => sub?.unsubscribe();
  }, [metricSubject]);

  /* *********************数据改变后，通知 plate 更新*********************** */

  useUpdateEffect(() => {
    const metaData = metricMeta.data as
      | MetricInfoAndExpertAnalysisInfo
      | undefined;
    const isMetaType = isMetaMetric(metaData);
    const metrics = [
      ...((element[METRIC_AREA_META_FIELD_NAME] || []) as MetricInfoLite[]),
    ];
    if (metaData) {
      const _metric = {
        ...metric,
        alias:
          (isMetaType
            ? metaData.meta_metric_data?.display_name
            : metaData.complex_metric_data?.name) ?? metric.alias,
        aliasID:
          (isMetaType
            ? metaData.meta_metric_data?.id
            : metaData.complex_metric_data?.id) ?? metric.aliasID,
        dataUnit:
          (isMetaType
            ? metaData.meta_metric_data?.data_unit
            : metaData.complex_metric_data?.complex_metric_list?.[0]
                .data_unit) ?? metric.dataUnit,
      };
      if (isEqual(metrics[index], _metric)) {
        return;
      }
      syncMetrics({
        index,
        editor,
        element,
        type: 'update',
        metric: _metric,
      });
    }
  }, [metricMeta]);

  useUpdateEffect(() => {
    const queryData = metricQuery.data as QueryData | undefined;
    const metrics = [
      ...((element[METRIC_AREA_META_FIELD_NAME] || []) as MetricInfoLite[]),
    ];

    if (queryData?.chart_data?.rule_conclusion?.tag) {
      const _metric = {
        ...metric,
        alias: queryData.chart_data?.series[0]?.name ?? metric.alias,
        tag: queryData.chart_data?.rule_conclusion?.tag,
      };
      if (isEqual(metrics[index], _metric)) {
        return;
      }
      syncMetrics({
        index,
        editor,
        element,
        type: 'update',
        metric: _metric,
      });
    }
  }, [metricQuery]);
  /* *********************数据改变后，通知 plate 更新*********************** */

  /* 条件变化，重新触发请求 */

  useUpdateEffect(() => {
    fetchMetricQuery();
  }, [
    range?.[0],
    range?.[1],
    cycle,
    nodeId,
    granularity,
    freezeVersion,
    domainMeasureObjId,
    JSON.stringify(domainMeasureObjFilter),
  ]);

  const editorAnchorAttr = {
    [EDITOR_ANCHOR_ATTR]: genMetricAnchorId(display_type, metric_id),
  };

  return (
    <div
      ref={nodeRef}
      {...editorAnchorAttr}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      className={clsx(
        long && 'col-span-3',
        dropLine === 'left' && styles.leftDropLine,
        dropLine === 'right' && styles.rightDropLine,
      )}>
      {/* <LazyContainer style={{ minHeight: long ? 380 : 300 }}> */}
      {useMemo(
        () =>
          children({
            grabber,

            metrics: metrics!,
            metricInfo: metric,
            onDelete,
            fullScreen,
            readonly,
            metricMeta,
            metricQuery,
            templateInfo,
            fetchMetricMeta,
            fetchMetricQuery,
          }),
        [readonly, templateInfo, metricMeta, metricQuery, onDelete, fullScreen],
      )}
      {/* </LazyContainer> */}
    </div>
  );
};
