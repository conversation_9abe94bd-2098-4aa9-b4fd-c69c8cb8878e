import * as React from 'react';

import { <PERSON><PERSON>, Divider, Empty, Modal, Space } from '@arco-design/web-react';
import { IconEmpty } from '@arco-design/web-react/icon';
import { useModel } from '@jupiter/plugin-runtime/model';

import {
  MetricTypeComplexMeta,
  MetricTypeMeta,
} from '@quality/common-apis/src/apis/api/zepto/consts';

import MetricDetailDrawer from '@/components/MetricDetailDrawer';
import { useEditorReadonly } from '@/components/ReportDetail/hooks/useEditorReadonly';
import { summaryStoreGet } from '@/components/ReportSummary/summary-v2/store';
import { SELF_EDITOR_ID } from '@/constants';
import { noSupportErrMsg } from '@/hooks/metric-editor/helper';
import { useReportConfigUrlQuery } from '@/hooks/url';
import { reportModel } from '@/model/report';
import { getMetricName, isMetaMetric } from '@/utils/metrics';
import { inDashboardUrl } from '@/utils/url';

import { MetricBaseProps } from '../types';

import { DeleteButton } from './common/MetricCardView';
import { StoryCardView } from './StoryCardView';

export const StoryCardContainer: React.FC<MetricBaseProps> = ({
  grabber,

  // metricInfo: metric,
  metricMeta,
  metricQuery,
  // draggable = true,
  readonly,

  onDelete,
  fetchMetricMeta,
  fetchMetricQuery,
  fullScreen,
}) => {
  let { urlQuery } = useReportConfigUrlQuery();
  const { nodeId, freezeVersion, domainMeasureObjId, domainMeasureObjFilter } =
    urlQuery;
  // 当前报告（非继承部分）是否处于只读
  const reportReadonly = useEditorReadonly(SELF_EDITOR_ID);
  const [{ tasks, templateInfo }] = useModel([
    reportModel,
    state => ({
      tasks: state.reportAttachedInfo.improvement_task_info,
      templateInfo: state.$templateInfo,
    }),
  ]);
  const { open: openDrawer } = MetricDetailDrawer.useToggle();

  const freeze = Boolean(freezeVersion);
  const metricQueryErrorMsg = metricQuery.extra?.msg;
  const isDashboard = inDashboardUrl();

  const renderActions =
    readonly || freeze ? null : (
      <div className="actions-wrapper">
        <DeleteButton
          onClick={() => {
            // 如果当前的卡片里面有富文本
            const summaryData = summaryStoreGet.summaryData();
            const metricData = isMetaMetric(metricMeta.data)
              ? metricMeta.data?.meta_metric_data
              : metricMeta.data?.complex_metric_data;

            if (
              metricData &&
              summaryData?.block_map?.[metricData.id]?.has_rich_text
            ) {
              Modal.confirm({
                title: '删除指标',
                content: '删除指标将同步删除概览下的富文本内容，是否确认？',
                okText: '是',
                cancelText: '否',
                okButtonProps: { status: 'danger' },
                onOk: () => {
                  onDelete();
                },
              });
            } else {
              onDelete();
            }
          }}
        />
        <Divider type="vertical" style={{ margin: 0 }} />
        {grabber}
      </div>
    );

  const MetricQueryRefresh = (
    <div className="text-xs text-[#80838A] flex items-center justify-center">
      {metricQueryErrorMsg ? (
        <Space>
          <span title={metricQueryErrorMsg}>
            {metricQueryErrorMsg !== noSupportErrMsg
              ? `${metricQueryErrorMsg.slice(0, 40)}...`
              : noSupportErrMsg}
          </span>
          {!fullScreen && metricQueryErrorMsg !== noSupportErrMsg && (
            <Button
              className="px-1"
              type="text"
              onClick={() => fetchMetricQuery()}>
              刷新
            </Button>
          )}
        </Space>
      ) : (
        <Space>
          <IconEmpty style={{ fontSize: 36 }} />
          <span>暂无结论</span>
          {!fullScreen && (
            <Button
              className="px-1"
              type="text"
              onClick={() => fetchMetricQuery()}>
              刷新
            </Button>
          )}
        </Space>
      )}
    </div>
  );

  const metricEmptyFullback = (
    <Empty
      description={
        <>
          指标数据查询失败
          <Button className="px-1" type="text" onClick={fetchMetricMeta}>
            刷新
          </Button>
        </>
      }
    />
  );

  const onClickMainContent = () => {
    const hasPermission = metricMeta.data?.has_permission ?? true;
    const metricData = isMetaMetric(metricMeta.data)
      ? metricMeta.data?.meta_metric_data
      : metricMeta.data?.complex_metric_data;
    const metricName = getMetricName(metricMeta.data);
    const domainMeasureObjFilterValue = {
      measureObjId: domainMeasureObjId!,
      filter: domainMeasureObjFilter!,
    };

    // 是否支持领域度量对象筛选
    const supportDomainMeasureObjFilter =
      !domainMeasureObjId ||
      Boolean(
        (metricData?.domain_measure_objs ?? [])?.find(
          ({ id }) => id === domainMeasureObjId,
        )?.dimension_id,
      );

    hasPermission &&
      supportDomainMeasureObjFilter &&
      !fullScreen &&
      openDrawer({
        initialMetric: {
          id: metricData!.id,
          name: metricName!,
          type: isMetaMetric(metricMeta.data)
            ? MetricTypeMeta
            : MetricTypeComplexMeta,
        },
        initialNodeId: nodeId,
        initialTime: {
          cycle: urlQuery.cycle,
          range: urlQuery.range || [],
          granularity: urlQuery.granularity,
        },

        initialDomainMeasureObjFilter: domainMeasureObjFilterValue,
        freezeVersion,
      });
  };

  return (
    <div>
      <StoryCardView
        tasks={tasks}
        urlQuery={urlQuery}
        businessId={nodeId}
        actions={renderActions}
        metricMeta={metricMeta}
        metricQuery={metricQuery}
        templateInfo={templateInfo}
        reportReadonly={reportReadonly}
        conclusionEmpty={MetricQueryRefresh}
        onClickMainContent={onClickMainContent}
        fullScreen={fullScreen}
        metricEmptyFullback={metricEmptyFullback}
        expandDisabled={isDashboard}
      />
    </div>
  );
};
