.MetricFilterList {
  .containerM {
    @apply bg-white rounded border border-[#E5E6EB] border-solid shadow-2xl w-[400px];

    .header {
      @apply flex flex-col p-4;

      .title {
        @apply text-sm text-[#86909C];
      }

      .filter-container {
        @apply flex gap-3 justify-between;
      }
    }
    .arco-list-item-main {
      box-shadow: none !important;
    }
    .metric-content {
      @apply border-0 rounded-none border-t border-solid border-t-[#E5E6EB];

      /* arco 样式覆盖 */
      > .arco-list-content {
        @apply pt-5 px-4;
      }

      .item-container {
        @apply flex justify-between items-center p-3
                  rounded border border-[#E5E6EB] border-solid
                  mb-2 hover:bg-gray-100 cursor-pointer;

        .content {
          @apply flex flex-col;
        }
      }
    }
  }
}
