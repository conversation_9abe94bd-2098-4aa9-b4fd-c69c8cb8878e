import * as React from 'react';
import { useEffect, useMemo, useState } from 'react';

import {
  Button,
  Divider,
  Empty,
  Select,
  Skeleton,
} from '@arco-design/web-react';
import {
  MetricTypeMeta,
  MetricTypeComplexMeta,
} from '@devmind/server/app/constants';
import { useModal } from '@ebay/nice-modal-react';
import { useUpdateEffect } from 'ahooks';
import clsx from 'clsx';
import { noop } from 'lodash';

import { DrillWhere } from '@quality/common-apis/src/apis/api/query/query';
import { CHART_TYPE, VisualPanel } from '@quality/common-components';
import { LoadingState } from '@quality/common-utils';

import { useLargeView } from '@/components/LargeViewModal';
import MetricDetailDrawer from '@/components/MetricDetailDrawer';
import DrillModal from '@/components/MetricDetailDrawer/components/DrillModal';
import {
  getAnalysisMetricIds,
  getMetricDrillConfig,
  isMetaMetricData,
} from '@/components/MetricDetailDrawer/utils';
import MetricName from '@/components/MetricName';
import { useEditorReadonly } from '@/components/ReportDetail/hooks/useEditorReadonly';
import { SELF_EDITOR_ID } from '@/constants';
import { LogEventMap, LogSceneMap } from '@/constants/log';
import { noSupportErrMsg } from '@/hooks/metric-editor/helper';
import { useReportConfigUrlQuery } from '@/hooks/url';
import { getMetricName, isMetaMetric } from '@/utils/metrics';
import { XLog } from '@/utils/utils';

import { MetricBaseProps } from '../types';

import { CardContainer, DeleteButton } from './common/MetricCardView';
import style from './SheetCard.module.scss';

interface SheetCardProps extends MetricBaseProps {}
export const SheetCard: React.FC<SheetCardProps> = ({
  readonly,
  grabber,

  // metricInfo: metric,
  metricMeta,
  metricQuery,

  onDelete,
  fetchMetricMeta,
  fetchMetricQuery,
  fullScreen,
}) => {
  let {
    urlQuery: {
      range,
      cycle,
      granularity,
      nodeId,
      domainMeasureObjFilter = [],
      domainMeasureObjId = '',
    },
  } = useReportConfigUrlQuery();

  const [dimensionId, setDimensionId] = useState<string>('');
  const [timeRange] = useState<string[]>(range!); // 用户选的时间范围

  const metricMetaLoading =
    metricMeta.loading === LoadingState.Loading ||
    metricMeta.loading === LoadingState.NotStarted;
  const metricQueryLoading =
    metricQuery.loading === LoadingState.Loading ||
    metricQuery.loading === LoadingState.NotStarted;

  const metricData = isMetaMetric(metricMeta.data)
    ? metricMeta.data?.meta_metric_data
    : metricMeta.data?.complex_metric_data;

  const { owners, metricName, metric_info, description, metric_attribute } =
    useMemo(() => {
      const isMeta = isMetaMetric(metricMeta.data);

      const {
        description = '',
        metric_info,
        metric_attribute,
        metric_expand_attribute,
      } = (isMeta
        ? metricMeta.data?.meta_metric_data
        : metricMeta.data?.complex_metric_data) || {};

      const metricName = getMetricName(metricMeta.data) || '';
      const owners = isMeta
        ? (metricMeta.data?.meta_metric_data?.owners as string[])
        : [metricMeta.data?.complex_metric_data?.common_data.owner as string];

      return {
        owners,
        metricName,
        metric_info,
        description,
        metric_attribute,
        metric_expand_attribute,
      };
    }, [metricMeta]);

  const analysisMetricIds = getAnalysisMetricIds(metricData);

  const { expandButton } = useLargeView({
    title: (
      <MetricName
        name={metricName}
        owners={owners}
        description={description}
        metricInfo={metric_info}
        metricAttribute={metric_attribute}
      />
    ),
    content: (
      <div className={clsx(style.content, { fullScreenTable: fullScreen })}>
        <VisualPanel
          loading={metricQueryLoading}
          drillWhere={[]}
          onDrillBack={noop}
          hasMenuMetrics={[]}
          timeGranularitys={[]}
          dimensionConf={{}}
          displayConfig={
            metricQuery.extra?.requestParams?.display_config || {
              display_type: CHART_TYPE.DOUBLE_AXIS,
            }
          }
          dataSource={metricQuery.data}
          query_info={metricQuery.extra?.requestParams?.query_info}
          onDrill={noop}
        />
      </div>
    ),
  });

  const drillModal = useModal(DrillModal);

  // 查看明细下钻
  const handleDrill = (drillWhere: DrillWhere) => {
    XLog({
      id: 'webclick',
      page_id: LogSceneMap.metric.value,
      page_title: LogSceneMap.metric.type,
      btn_title: {
        metric: isMetaMetricData(metricData!)
          ? metricData.display_name
          : metricData?.name,
        event: LogEventMap.metric.drillModal,
      },
    });
    drillModal.show({
      nodeId,
      queryParams: metricQuery.extra?.requestParams,
      drillWhere,
      drillConfig: getMetricDrillConfig(metricData as any),
    });
  };

  // 初始化默认选中第一个对比维度
  useEffect(() => {
    const initDimensionId =
      metricData?.analysis_info?.analysis_dim_ids?.[0]?.dim_id;
    initDimensionId && setDimensionId(initDimensionId);
  }, [metricData?.analysis_info?.analysis_dim_ids]);

  /* 改变基线或时间范围时重新触发请求 */
  useUpdateEffect(() => {
    if (metricQuery.extra?.timeFilterValue) {
      fetchMetricQuery({
        queryInfo: {
          measureDimensionId: dimensionId,
          timeFilterInfo: {
            ...metricQuery.extra.timeFilterValue,
            range: timeRange,
          },
        },
      });
    }
  }, [dimensionId, timeRange]);

  const { open: openDrawer } = MetricDetailDrawer.useToggle();
  // 当前报告（非继承部分）是否处于只读
  const reportReadonly = useEditorReadonly(SELF_EDITOR_ID);

  const onClickMainContent = () => {
    const hasPermission = metricMeta.data?.has_permission ?? true;
    const metricData = isMetaMetric(metricMeta.data)
      ? metricMeta.data?.meta_metric_data
      : metricMeta.data?.complex_metric_data;
    const metricName = getMetricName(metricMeta.data);
    const domainMeasureObjFilterValue = {
      measureObjId: domainMeasureObjId!,
      filter: domainMeasureObjFilter!,
    };

    // 是否支持领域度量对象筛选
    const supportDomainMeasureObjFilter =
      !domainMeasureObjId ||
      Boolean(
        (metricData?.domain_measure_objs ?? [])?.find(
          ({ id }) => id === domainMeasureObjId,
        )?.dimension_id,
      );

    hasPermission &&
      supportDomainMeasureObjFilter &&
      metricData &&
      !fullScreen &&
      openDrawer({
        initialMetric: {
          id: metricData!.id,
          name: metricName!,
          type: isMetaMetric(metricMeta.data)
            ? MetricTypeMeta
            : MetricTypeComplexMeta,
        },
        initialNodeId: nodeId,
        initialTime: {
          cycle,
          range: range || [],
          granularity,
        },
        initialDomainMeasureObjFilter: domainMeasureObjFilterValue,
      });
  };

  const renderActions = readonly ? null : (
    <div className={style.cardAction}>
      <DeleteButton onClick={onDelete} />
      <Divider type="vertical" style={{ margin: 0 }} />
      {grabber}
    </div>
  );

  return (
    <div
      className={clsx(style.container)}
      onClick={onClickMainContent}
      style={{
        ...(fullScreen
          ? {
              cursor: 'default',
              border: 0,
              backgroundColor: 'rgba(255, 255, 255, 0.04)',
            }
          : {}),
      }}>
      <Skeleton text={{ rows: 5 }} loading={metricMetaLoading}>
        {metricData ? (
          <>
            <div className={style.header}>
              <div className={style.title}>
                <MetricName
                  className="max-w-[180px] text-sm font-medium"
                  ellipsis
                  name={metricName}
                  owners={owners}
                  description={description}
                  metricInfo={metric_info}
                  metricAttribute={metric_attribute}
                />
              </div>
              {readonly && expandButton}
              {renderActions}
            </div>
            <div
              className={style.action}
              style={fullScreen ? { display: 'none' } : {}}
              onClick={e => e.stopPropagation()}>
              <Select
                className="w-32"
                value={dimensionId}
                onChange={setDimensionId}>
                {metricData?.analysis_info?.analysis_dim_ids?.map(
                  ({ dim_id, display_name }) => (
                    <Select.Option value={dim_id} key={dim_id}>
                      {display_name}
                    </Select.Option>
                  ),
                )}
              </Select>
            </div>
            <div
              className={clsx([
                metricQueryLoading && 'h-40',
                fullScreen && 'fullScreenTable',
              ])}
              onClick={e => e.stopPropagation()}>
              <VisualPanel
                autoHeight
                loading={metricQueryLoading}
                drillWhere={[]}
                onDrillBack={noop}
                hasMenuMetrics={[]}
                hideMenus={['指标详情分析']}
                timeGranularitys={[]}
                dimensionConf={{}}
                displayConfig={
                  metricQuery.extra?.requestParams?.display_config || {
                    display_type: CHART_TYPE.DOUBLE_AXIS,
                  }
                }
                showSupportErrMsg={metricQuery.extra?.msg === noSupportErrMsg}
                fullScreen={fullScreen}
                dataSource={metricQuery.data}
                query_info={metricQuery.extra?.requestParams?.query_info}
                analysisMetricIds={analysisMetricIds}
                chart_detail_info={
                  {
                    meta_drill_config: getMetricDrillConfig(metricData as any),
                  } as any
                }
                onAnalysis={noop}
                onDrill={handleDrill}
              />
            </div>
          </>
        ) : (
          <CardContainer className="justify-center">
            <Empty
              description={
                <>
                  指标数据查询失败
                  <Button
                    className="px-1"
                    type="text"
                    onClick={fetchMetricMeta}>
                    刷新
                  </Button>
                </>
              }
            />
          </CardContainer>
        )}
      </Skeleton>
    </div>
  );
};
