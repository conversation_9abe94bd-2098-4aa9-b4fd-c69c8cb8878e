import React, { useState, FC, useMemo } from 'react';

import {
  Divider,
  Skeleton,
  Tag,
  SkeletonProps,
  Button,
  Empty,
  Link,
} from '@arco-design/web-react';
import { IconDown, IconUp } from '@arco-design/web-react/icon';
import { useModel } from '@jupiter/plugin-runtime/model';
import clsx from 'clsx';
import { MacScrollbar } from 'mac-scrollbar';

import { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import {
  MetricTypeMeta,
  MetricTypeComplexMeta,
  PeopleMeasureObjID,
  CodeRepoMeasureObjID,
  ProjectMeasureObjID,
  PSMMeasureObjID,
  APPMeasureObjID,
  ProductMeasureObjID,
} from '@quality/common-apis/src/apis/api/zepto/consts';
import { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import { TextStatusType } from '@quality/common-apis/src/apis/rule_engine/rule_text';

import {
  AnalysisConclusion as Conlusion,
  AnalysisReason as Reason,
  Improvement,
} from '@/components/Analysis';
import MetricDetailDrawer from '@/components/MetricDetailDrawer';
import MetricName from '@/components/MetricName';
import { useEditorReadonly } from '@/components/ReportDetail/hooks/useEditorReadonly';
import { getFormatTimeFilter, getReportName } from '@/components/TimeFilter';
import UserAvatar from '@/components/UserAvatar';
import { SELF_EDITOR_ID } from '@/constants/editor';
import { useReportConfigUrlQuery } from '@/hooks/url';
import { reportModel } from '@/model/report';

import { MetricContentProps, StatValue } from '../types';
import {
  genStatDisplayValue,
  genStatValue,
  getRenderTagsProps,
} from '../utils';

const Header: React.FC = props => (
  <div
    {...props}
    className="flex relative items-start text-sm font-medium break-all">
    {props.children}
  </div>
);

export const StatDisplay: React.FC<{
  data: StatValue | null;
  loading: boolean;
  hasPermission: boolean;
}> = ({ data, loading, hasPermission }) => {
  const skeletonProps: SkeletonProps = {
    loading,
    text: { rows: 1 },
    animation: true,
  };

  const statDisplay = genStatDisplayValue(data);

  if (!hasPermission) {
    return (
      <Skeleton {...skeletonProps}>
        <div className="stat-display-container">
          <div className="no-permission">暂无权限查看</div>
        </div>
      </Skeleton>
    );
  }

  if (statDisplay == null) {
    return (
      <Skeleton {...skeletonProps}>
        <div className="stat-display-container">
          <div className="stat-display-value">NA</div>
        </div>
      </Skeleton>
    );
  }

  const { displayValue, unit } = statDisplay;

  return (
    <Skeleton {...skeletonProps}>
      <div className="stat-display-container">
        <span className="stat-display-value">{displayValue}</span>
        <span className="stat-display-unit">{unit}</span>
      </div>
    </Skeleton>
  );
};

const NoDomainMeasureObjEmpty: FC<{
  owner: string;
  domainMeasureObjName: string;
}> = ({ owner, domainMeasureObjName }) => (
  <Empty
    className="!p-0"
    description={
      <>
        <div className="text-text5">
          该指标未配置 {domainMeasureObjName} 领域度量对象
        </div>
        <div className="mt-1 !text-xs">
          请联系指标负责人
          <span className="text-brand5">
            @
            <UserAvatar username={owner} showAvatar={false} nameSize={12} />
          </span>
          配置
          <Link className="!text-xs" href={location.href}>
            刷新
          </Link>
        </div>
      </>
    }
  />
);

const metricNameClass = `text-sm text-gray-500 mb-3`;

export const MetricContainer: React.FC<{ className?: string }> = ({
  className,
  children,
}) => <div className={clsx('metricContainer', className)}>{children}</div>;

export const MetricContent: React.FC<MetricContentProps> = ({
  isMetaMetric,
  isComplexMetric,

  businessId,
  queryParams,
  metricData,
  queryData,
  loading,
  actions,
  refreshButton,
  thumbnailChart,

  supportDomainMeasureObjFilter = true,
  hasPermission,

  expandDisabled,
}) => {
  const [isExpanded, setExpand] = useState(false);
  /* 根据这个标志位来避免已展开过的信息的重复请求（display: none;） */
  const [hasBeenExpanded, setHasBeenExpanded] = useState(false);

  const [{ templateInfo, fullScreen }] = useModel([
    reportModel,
    state => ({
      instanceInfo: state.$instanceInfo,
      templateInfo: state.$templateInfo,
      fullScreen: state.fullScreen,
    }),
  ]);

  // 当前报告（非继承部分）是否处于只读
  const selfEditorReadOnly = useEditorReadonly(SELF_EDITOR_ID);

  const { description, metric_info, metric_attribute, name, owners } =
    React.useMemo(() => {
      const { description, metric_info, metric_attribute } = metricData || {};

      const name = isMetaMetric
        ? (metricData as MetricMetaData).display_name
        : isComplexMetric
        ? (metricData as ComplexMetricMetaData).name
        : '';

      const owners = isMetaMetric
        ? (metricData as MetricMetaData).owners
        : isComplexMetric
        ? [(metricData as ComplexMetricMetaData).common_data.owner!]
        : [];

      return {
        description,
        metric_info,
        metric_attribute,
        name,
        owners,
      };
    }, [metricData, isMetaMetric, isComplexMetric]);

  const {
    urlQuery: {
      nodeId,
      granularity,
      cycle,
      range = [],
      domainMeasureObjId = '',
      domainMeasureObjFilter = [],
      freezeVersion = '',
    },
  } = useReportConfigUrlQuery();

  const timeFilterInfo = getFormatTimeFilter({
    granularity,
    cycle,
    range,
  });

  const reportFullName = useMemo(() => {
    if (!templateInfo || !timeFilterInfo) {
      return '';
    }
    return getReportName({ ...templateInfo, ...timeFilterInfo });
  }, [templateInfo, timeFilterInfo]);

  const domainMeasureObjFilterValue = {
    measureObjId: domainMeasureObjId,
    filter: domainMeasureObjFilter,
  };

  const domainMeasureObjName = {
    [PeopleMeasureObjID]: '人员',
    [CodeRepoMeasureObjID]: '代码仓',
    [ProjectMeasureObjID]: '项目',
    [PSMMeasureObjID]: '服务',
    [APPMeasureObjID]: 'APP',
    [ProductMeasureObjID]: '产品',
  }[domainMeasureObjId];

  const [start_time, end_time] = timeFilterInfo?.range ?? [];

  const statValue = React.useMemo<StatValue | null>(
    () => genStatValue(metricData, queryData, isMetaMetric),
    [metricData, queryData, isMetaMetric],
  );

  const conlusion = queryData?.chart_data?.rule_conclusion;
  const hiddenExpandBtn = !conlusion?.phrase_list?.length || expandDisabled;

  const tagsProps = getRenderTagsProps(conlusion?.tag);

  const [{ improvement_task_info }] = useModel([
    reportModel,
    state => ({
      improvement_task_info: state.reportAttachedInfo.improvement_task_info,
    }),
  ]);
  const { open: openDrawer } = MetricDetailDrawer.useToggle();

  const onExpand = () => {
    const expanded = !isExpanded;
    if (expanded) {
      setHasBeenExpanded(true);
    }
    setExpand(expanded);
  };
  const metric_improvement_info =
    improvement_task_info?.metric_improvement_info;
  const improveTagProps = metric_improvement_info?.[metricData.id];

  return (
    <>
      <MetricContainer
        className={clsx(
          isExpanded ? 'expand' : false,
          hiddenExpandBtn ? 'no-expand-btn' : false,
          !hasPermission ? 'no-permission' : false,
          fullScreen ? 'fullscreenContainer' : false,
        )}>
        <div
          onClick={e => {
            e.stopPropagation();
            hasPermission &&
              supportDomainMeasureObjFilter &&
              !fullScreen &&
              openDrawer({
                initialMetric: {
                  id: metricData.id,
                  name,
                  type: isMetaMetric ? MetricTypeMeta : MetricTypeComplexMeta,
                },
                initialNodeId: nodeId,
                initialTime: timeFilterInfo,
                initialDomainMeasureObjFilter: domainMeasureObjFilterValue,
                freezeVersion,
              });
          }}
          className={clsx(
            'main-content',
            (!supportDomainMeasureObjFilter || !hasPermission) &&
              '!cursor-not-allowed',
          )}
          data-metric={metricData.id}>
          <Header>
            <div className="header--title" title={name}>
              {name}
            </div>
            {actions}
          </Header>

          <Skeleton loading={loading} text={{ rows: 1 }} animation={true}>
            <div
              className={clsx([
                'tag-content',
                !supportDomainMeasureObjFilter && 'invisible',
              ])}>
              {!hasPermission ? (
                <Tag size="small" className="!rounded-[32px]" color="red">
                  <span>敏感指标</span>
                </Tag>
              ) : tagsProps?.text ? (
                <Tag
                  size="small"
                  className="!rounded-[32px]"
                  color={tagsProps.color}>
                  <span>{tagsProps.text}</span>
                </Tag>
              ) : (
                <Tag size="small" className="!rounded-[32px]" color="gray">
                  <span>正常</span>
                </Tag>
              )}
              {improveTagProps && (
                <Tag
                  className="!rounded-[32px] ml-2"
                  size="small"
                  color={
                    improveTagProps.status === TextStatusType.Critical
                      ? 'red'
                      : 'green'
                  }>
                  <span>{improveTagProps.text}</span>
                </Tag>
              )}
            </div>
          </Skeleton>

          <div className="flex justify-between">
            <div className="flex flex-col max-w-[calc(100%-168px)]">
              <MetricName
                ellipsis={true}
                className={metricNameClass}
                name={name}
                owners={owners}
                description={description}
                metricInfo={metric_info}
                metricAttribute={metric_attribute}
                inMetricCard={true}
              />

              <StatDisplay
                hasPermission={hasPermission}
                loading={loading}
                data={statValue}
              />
            </div>

            <div className="w-[164px]">{thumbnailChart}</div>
          </div>

          <Divider className="!my-5" />
        </div>

        {/* 额外展开的内容 */}
        <MacScrollbar suppressScrollX={true}>
          <Skeleton loading={!hasPermission} text={{ rows: 3 }}>
            <div className="advanced">
              {supportDomainMeasureObjFilter ? (
                <Conlusion
                  loading={loading}
                  conlusion={conlusion}
                  dataUnit={statValue?.unit}
                  empty={refreshButton}
                />
              ) : (
                <NoDomainMeasureObjEmpty
                  domainMeasureObjName={domainMeasureObjName}
                  owner={
                    ((metricData as MetricMetaData).owners?.[0] ||
                      (metricData as ComplexMetricMetaData).common_data
                        ?.owner) ??
                    ''
                  }
                />
              )}
              {hasBeenExpanded ? (
                <div style={{ display: isExpanded ? 'block' : 'none' }}>
                  {/* 波动分析暂时不上 */}
                  {
                    <>
                      <Divider className="!my-2" />
                      <Reason
                        listMaxHeight={360}
                        queryParams={queryParams}
                        dataSource={queryData}
                        metricData={metricData}
                        nodeId={nodeId}
                        expertProps={{
                          template_id: templateInfo?.template_id,
                          editable: !selfEditorReadOnly,
                          business_id: businessId,
                          metric_id: metricData.id,
                          metricData,
                          start_time,
                          timeFilterInfo,
                          end_time,
                          metric_expand_attribute:
                            metricData.metric_expand_attribute,
                          metric_name:
                            (metricData as MetricMetaData).display_name ||
                            (metricData as ComplexMetricMetaData).name,
                          report_name: reportFullName,
                          report_url: location.href,
                        }}
                        timeFilterInfo={timeFilterInfo}
                      />
                    </>
                  }
                  {/* xxx IFTRUE_prodEnv */}

                  <Divider className="!my-2" />
                  <Improvement
                    analysisProps={{
                      template_id: templateInfo?.template_id,
                      editable: !selfEditorReadOnly,
                      business_id: businessId,
                      metric_id: metricData.id,
                      metricData,
                      start_time,
                      timeFilterInfo,
                      end_time,
                      metric_expand_attribute:
                        metricData.metric_expand_attribute,
                      metric_name:
                        (metricData as MetricMetaData).display_name ||
                        (metricData as ComplexMetricMetaData).name,
                      report_name: reportFullName,
                      report_url: location.href,
                    }}
                    freezeVersion={freezeVersion}
                  />
                  {/* xxx FITRUE_prodEnv */}
                </div>
              ) : null}
            </div>
          </Skeleton>
        </MacScrollbar>
      </MetricContainer>

      <Button
        // 没有结论/没有权限时隐藏展开/收起按钮
        className={clsx(
          'btn-expand',
          (!hasPermission || hiddenExpandBtn) && '!hidden',
        )}
        long={true}
        onClick={onExpand}
        icon={isExpanded ? <IconUp /> : <IconDown />}>
        {isExpanded ? '收起' : '展开'}
      </Button>
    </>
  );
};
