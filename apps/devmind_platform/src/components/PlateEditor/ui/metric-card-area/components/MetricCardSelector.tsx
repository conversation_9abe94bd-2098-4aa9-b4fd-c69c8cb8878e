import { FC, useEffect, useMemo, useState } from 'react';

import {
  Button,
  Form,
  Input,
  List,
  Message,
  Select,
  Space,
  Spin,
} from '@arco-design/web-react';
import { IconSearch } from '@arco-design/web-react/icon';
import { useModel } from '@jupiter/plugin-runtime/model';
import { useDebounce } from 'ahooks';
import clsx from 'clsx';
import styled from 'styled-components';

import {
  DisplayTypeBar,
  DisplayTypeDoubleAxis,
  DisplayTypePie,
  DisplayTypeSheet,
  DisplayTypeStory,
} from '@quality/common-apis/src/apis/api/base/display_config';
import { MetaAndComplexMetricInfo } from '@quality/common-apis/src/apis/api/zepto/meta_metric';

import { getSelfMetricList } from '@/components/PlateEditor/utils/compare';
import UserAvatar from '@/components/UserAvatar';
import globalModel from '@/model/global';
import { reportModel } from '@/model/report';
import { MetricInfo } from '@/model/report/types';
import { inEmployeeUrl } from '@/utils/url';

import { PlateStyledElementProps } from '../types';

import style from './MetricCardSelector.module.scss';

const ALL = 'all';

const MetricStyledList = styled(List)`
  .arco-list-item-content {
    padding: 0 !important;
  }
`;

const displayTypeOptions = [
  { label: '指标故事', value: DisplayTypeStory },
  { label: '表格', value: DisplayTypeSheet },
  { label: '折线图', value: DisplayTypeDoubleAxis },
  { label: '柱状图', value: DisplayTypeBar },
  { label: '饼图', value: DisplayTypePie },
];

const showDimensionDisplayTypeList = [DisplayTypePie, DisplayTypeBar];

interface MetricCardSelectorProps extends PlateStyledElementProps {
  editorID: string;
  parentID: string;
  onClose: () => void;
  edit?: boolean;
  index?: number;
}

export const MetricCardSelector: FC<MetricCardSelectorProps> = props => {
  const {
    editor,
    element,
    editorID,
    parentID,
    onClose,
    edit = false,
    index = 0,
  } = props;

  const [form] = Form.useForm();

  const [
    { effectState, metricList, editorRefById, parentMetricList },
    { syncMetrics },
  ] = useModel([
    reportModel,
    state => ({
      effectState: state.metaAndComplexMetricListState,
      metricList: state.metaAndComplexMetricList,
      editorRefById: state.editorRefById,
      parentMetricList: state.parentMetricList,
    }),
  ]);

  // 切换卡片类型 重置对比维度
  const displayType = Form.useWatch('displayType', form);
  const metricInfo: MetricInfo = Form.useWatch('metricInfo', form);
  useEffect(() => {
    form.resetFields(['dimensionId']);
  }, [displayType, metricInfo?.metric_id]);

  const handleSubmit = async () => {
    try {
      const data = await form.validate();
      const newMetricList = getSelfMetricList(editorRefById);
      const sum = newMetricList.length + parentMetricList.length;
      if (!edit) {
        if (sum > 79) {
          onClose();
          Message.error('仪表盘最多支持添加80个指标卡片');
          return;
        }
      }
      syncMetrics({
        index,
        editor,
        element,
        type: edit ? 'update' : 'insert',
        metric: {
          ...data.metricInfo,
          display_type: data.displayType,
          default_dim_id: data.dimensionId ?? '',
        },
      });
      onClose?.();
    } catch {}
  };

  return (
    <Spin loading={effectState.loading}>
      <div className={style.container}>
        <div className={style.title}>{`${edit ? '编辑' : '添加'}卡片`}</div>
        <Form
          form={form}
          layout="vertical"
          initialValues={{ displayType: DisplayTypeStory }}>
          <Form.Item
            label="指标配置"
            field="metricInfo"
            rules={[{ required: true, message: '请选择指标' }]}>
            <MetricList
              editor={editor}
              element={element}
              editorID={editorID}
              parentID={parentID}
            />
          </Form.Item>
          <Form.Item label="卡片形式" required field="displayType">
            <Select options={displayTypeOptions} />
          </Form.Item>
          <Form.Item noStyle shouldUpdate>
            {values => {
              let dimensionIdOptions = metricList
                ?.find(({ id }) => id === values.metricInfo?.metric_id)
                ?.analysis_info?.analysis_dim_ids?.map(
                  ({ dim_id, display_name }) => ({
                    label: display_name,
                    value: dim_id,
                  }),
                );

              if (inEmployeeUrl()) {
                dimensionIdOptions = dimensionIdOptions?.filter(
                  d => d.label !== '人员',
                );
              }
              return values.metricInfo?.metric_id &&
                showDimensionDisplayTypeList.includes(values.displayType) ? (
                <Form.Item
                  field="dimensionId"
                  label="对比维度"
                  rules={[{ required: true, message: '请选择对比维度' }]}>
                  <Select
                    placeholder="请选择维度"
                    options={dimensionIdOptions}
                  />
                </Form.Item>
              ) : null;
            }}
          </Form.Item>
          <Form.Item className="text-right">
            <Space>
              <Button onClick={onClose}>取消</Button>
              <Button type="primary" onClick={handleSubmit}>
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </div>
    </Spin>
  );
};

interface MetricListProps extends Omit<MetricCardSelectorProps, 'onClose'> {
  value?: MetricInfo;
  onChange?: (v: MetricInfo) => any;
}

const MetricList: FC<MetricListProps> = props => {
  const { editorID, parentID, value, onChange } = props;

  const [{ configEnumInfo }] = useModel(globalModel);
  const [{ list }] = useModel([
    reportModel,
    state => ({
      list: state.metaAndComplexMetricList,
    }),
  ]);

  // const tempTotalMetricEntries = useMemo(
  //   () =>
  //     Object.fromEntries(
  //       [...parentMetricList, ...metricList].map(item => [
  //         item.metaInfo.metric_id,
  //         item,
  //       ]),
  //     ),
  //   [metricList, parentMetricList],
  // );

  const [filteredType, setFilteredType] = useState<string>(ALL);
  const [filteredName, setFilteredName] = useState('');
  const debouncedFilterName = useDebounce(filteredName, { wait: 300 });

  const filteredList = useMemo<MetaAndComplexMetricInfo[]>(() => {
    return list.filter(
      item =>
        (filteredType === ALL || item.problem_region === filteredType) &&
        item.display_name
          .toLowerCase()
          .includes(debouncedFilterName.toLowerCase()) &&
        configEnumInfo.activeProblemRegionMaps
          .map(o => o.display_name)
          .includes(item.problem_region),
    );
  }, [filteredType, debouncedFilterName, list]);

  const filterOptions = useMemo(
    () => [
      { label: '全部', value: ALL },
      ...Array.from(new Set(list.map(({ problem_region }) => problem_region)))
        .filter(
          problem_region =>
            problem_region &&
            configEnumInfo.activeProblemRegionMaps
              .map(o => o.display_name)
              .includes(problem_region),
        )
        .map(problemRegion => ({
          label: problemRegion,
          value: problemRegion,
        })),
    ],
    [list],
  );

  const handleClick = (item: MetaAndComplexMetricInfo) => {
    // if (selected) {
    //   return;
    // }

    const metricInfo: MetricInfo = {
      parent_id: parentID,
      metric_id: item.id,
      metric_type: item.type,
      editorID,
      alias: item.display_name,
    };
    onChange?.(metricInfo);
  };
  return (
    <>
      <div className="flex items-center gap-3 mb-2">
        <Select
          className="flex-1"
          value={filteredType}
          onChange={setFilteredType}>
          {filterOptions.map(({ label, value }) => (
            <Select.Option key={value} value={value}>
              {label}
            </Select.Option>
          ))}
        </Select>

        <Input
          className="flex-1"
          value={filteredName}
          onChange={setFilteredName}
          placeholder="请搜索指标"
          allowClear
          suffix={<IconSearch />}
        />
      </div>
      <MetricStyledList
        className={style.metricList}
        virtualListProps={{ height: 342, itemHeight: 62 }}
        dataSource={filteredList ?? []}
        render={item => {
          // const selected = tempTotalMetricEntries.hasOwnProperty(item.id);

          /** 仪表盘不需要指标重复添加校验, 仪表盘和报告共用指标添加弹窗的时候需要把重复校验配置化 */
          return (
            <List.Item key={item.id} style={{ all: 'unset' }}>
              <div
                className={clsx(`${style.itemContainer}`, {
                  // 'bg-gray-100': selected,
                  // '!cursor-not-allowed': selected,
                  [style.active]: value?.metric_id === item.id,
                })}
                onClick={() => handleClick(item)}>
                <div className={style.itemContent}>
                  <div className="text-sm font-medium truncate max-w-[250px]">
                    {item.display_name}
                  </div>
                  <div className="text-xs font-normal text-gray-500">
                    {item.problem_region}
                  </div>
                </div>

                <UserAvatar username={item.owners_cn?.[0]} />
              </div>
            </List.Item>
          );
        }}
      />
    </>
  );
};
