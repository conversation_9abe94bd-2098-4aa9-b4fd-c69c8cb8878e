import { useRef } from 'react';

import { <PERSON><PERSON>, Trigger } from '@arco-design/web-react';
import { useModel } from '@jupiter/plugin-runtime/model';
import { Value } from '@udecode/plate';
import { useBoolean } from 'ahooks';
import clsx from 'clsx';
import { useSelected } from 'slate-react';

import {
  DisplayTypeBar,
  DisplayTypeDoubleAxis,
  DisplayTypePie,
  DisplayTypeSheet,
  DisplayTypeStory,
} from '@quality/common-apis/src/apis/api/base/display_config';

import { ReactComponent as IconPlus } from '@/assets/svg/toolbar/card-add-button.svg';
import { useEditorReadonly } from '@/components/ReportDetail/hooks/useEditorReadonly';
import { DND_DELIMITER } from '@/constants';
import { isMiniScreen } from '@/constants/adapt';
import { useReportConfigUrlQuery } from '@/hooks/url';
import { reportModel } from '@/model/report';
import { MetricInfoLite } from '@/model/report/types';
import { inDashboardUrl } from '@/utils/url';

import { BarCard } from './BarCard2';
import { MetricCardContainer } from './common/MetricCardContainer';
import { useDropContainer } from './hooks/useDropContainer';
import { MetricCardAreaProps } from './MetricCardArea.types';
import { MetricCardSelector } from './MetricCardSelector';
import { MetricFilterList } from './MetricFilterList';
import './styles.scss';
import { PieCard } from './PieCard2';
import { SheetCard } from './SheetCard2';
import { StoryCardContainer } from './StoryCardContainer';
import { XYCard } from './XYCard2';

type InnerMetricCardAreaProps<V extends Value = Value> =
  MetricCardAreaProps<V> & {
    id: string;
    editorID: string;
    list: MetricInfoLite[] | undefined;
  };

export const MetricCardArea = (props: MetricCardAreaProps<Value>) => {
  const { attributes, children, element, editorID } = props;

  // id 在添加时通过 uuid 生成
  const parentID = element.id as string;

  const metrics = element.metrics as MetricInfoLite[];

  return (
    // * 这里不能把 `attributes` 和 `contentEditable` 同时作用在一个元素上，否则选区会有问题
    <div {...attributes}>
      <div contentEditable={false} className="my-2">
        <InnerMetricCardArea
          {...props}
          id={parentID}
          list={metrics}
          editorID={editorID}
        />
        {children}
      </div>
    </div>
  );
};

const InnerMetricCardArea = (props: InnerMetricCardAreaProps) => {
  const { editor, element, id: parentId, list, editorID } = props;
  const isDashboard = inDashboardUrl();

  const readOnly = useEditorReadonly(editorID);

  const rootRef = useRef<HTMLDivElement>(null);
  const { isOver } = useDropContainer(editor, element, {
    id: [editorID, parentId].join(DND_DELIMITER),
    nodeRef: rootRef,
    readOnly,
  });

  const selected = useSelected();

  const { urlQuery } = useReportConfigUrlQuery();
  const freezed = Boolean(urlQuery.freezeVersion);
  const [{ metrics }] = useModel([reportModel]);

  const renderMetricCard = (metric: MetricInfoLite, index: number) => {
    const displayType = metric.display_type;
    const metricSubject = metrics?.get
      .metaOrQuerySubjectMap()
      .get(metric.metric_id);
    const long =
      displayType === DisplayTypeDoubleAxis || displayType === DisplayTypeSheet;

    return (
      <MetricCardContainer
        key={`${index}-${displayType}-${metric.metric_id}`}
        editor={editor}
        long={long}
        index={index}
        element={element}
        metric={metric}
        metricSubject={metricSubject}
        readonly={readOnly}>
        {({
          metricInfo,
          fetchMetricMeta,
          fetchMetricQuery,
          metricMeta,
          metricQuery,
          onDelete,
          grabber,
          fullScreen,
        }) => {
          switch (displayType) {
            case DisplayTypePie:
              return (
                <PieCard
                  metricInfo={metricInfo}
                  metricMeta={metricMeta}
                  metricQuery={metricQuery}
                  fetchMetricMeta={fetchMetricMeta}
                  fetchMetricQuery={fetchMetricQuery}
                  onDelete={onDelete}
                  readonly={readOnly}
                  grabber={grabber}
                  fullScreen={fullScreen}
                />
              );
            case DisplayTypeSheet:
              return (
                <SheetCard
                  metricInfo={metricInfo}
                  metricMeta={metricMeta}
                  metricQuery={metricQuery}
                  fetchMetricMeta={fetchMetricMeta}
                  fetchMetricQuery={fetchMetricQuery}
                  onDelete={onDelete}
                  readonly={readOnly}
                  grabber={grabber}
                  fullScreen={fullScreen}
                />
              );
            case DisplayTypeDoubleAxis:
              return (
                <XYCard
                  metricInfo={metricInfo}
                  metricMeta={metricMeta}
                  metricQuery={metricQuery}
                  fetchMetricMeta={fetchMetricMeta}
                  fetchMetricQuery={fetchMetricQuery}
                  onDelete={onDelete}
                  readonly={readOnly}
                  grabber={grabber}
                  fullScreen={fullScreen}
                />
              );
            case DisplayTypeBar:
              return (
                <BarCard
                  metricInfo={metricInfo}
                  metricMeta={metricMeta}
                  metricQuery={metricQuery}
                  fetchMetricMeta={fetchMetricMeta}
                  fetchMetricQuery={fetchMetricQuery}
                  onDelete={onDelete}
                  readonly={readOnly}
                  grabber={grabber}
                  fullScreen={fullScreen}
                />
              );
            case DisplayTypeStory:
            default:
              return (
                <StoryCardContainer
                  metricInfo={metricInfo}
                  metricMeta={metricMeta}
                  metricQuery={metricQuery}
                  fetchMetricMeta={fetchMetricMeta}
                  fetchMetricQuery={fetchMetricQuery}
                  onDelete={onDelete}
                  readonly={readOnly}
                  grabber={grabber}
                  fullScreen={fullScreen}
                />
              );
          }
        }}
      </MetricCardContainer>
    );
  };

  const [popupVisible, { setFalse, set: setPopupVisible }] = useBoolean();

  return (
    <div
      ref={rootRef}
      className={clsx(
        'MetricCardArea',
        selected && !readOnly && !freezed && 'selected',
        isOver && 'hovered',
      )}>
      <div className={!isMiniScreen ? 'content-wrap' : 'iPhone-content-wrap'}>
        {list?.map(renderMetricCard)}

        {readOnly || freezed ? null : (
          <div className="btn-add">
            <Trigger
              popupVisible={popupVisible}
              trigger="click"
              position="rt"
              popupAlign={{ right: 12 }}
              popup={() =>
                isDashboard ? (
                  <MetricCardSelector
                    editor={editor}
                    element={element}
                    editorID={editorID}
                    parentID={parentId}
                    onClose={setFalse}
                  />
                ) : (
                  <MetricFilterList
                    editor={editor}
                    element={element}
                    parentID={parentId}
                    onClose={setFalse}
                  />
                )
              }
              onVisibleChange={setPopupVisible}>
              <Button
                className="!flex !flex-col !justify-center !items-center !gap-1 !h-full !text-base"
                style={{
                  color: 'rgba(128, 131, 138, 1)',
                }}
                long={true}
                type="text"
                size="large">
                <IconPlus className="!text-black w-[14px] h-[14px] !pl-0" />
                添加卡片
              </Button>
            </Trigger>
          </div>
        )}
      </div>
    </div>
  );
};
