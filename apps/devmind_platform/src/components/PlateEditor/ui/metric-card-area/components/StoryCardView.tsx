import * as React from 'react';
import { useMemo, useState } from 'react';

import {
  Button,
  Divider,
  Empty,
  Link,
  Skeleton,
  SkeletonProps,
  Tag,
  TagProps,
  Typography,
} from '@arco-design/web-react';
import { IconDown, IconUp } from '@arco-design/web-react/icon';
import clsx from 'clsx';
import { MacScrollbar } from 'mac-scrollbar';

import { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { MetricInfoAndExpertAnalysisInfo } from '@quality/common-apis/src/apis/api/insight_report/report';
import {
  ReportImprovementTask,
  ReportTemplate,
} from '@quality/common-apis/src/apis/api/insight_report/report_model';
import {
  PeopleMeasureObjID,
  CodeRepoMeasureObjID,
  ProjectMeasureObjID,
  PSMMeasureObjID,
  APPMeasureObjID,
  ProductMeasureObjID,
} from '@quality/common-apis/src/apis/api/zepto/consts';
import { TextStatusType } from '@quality/common-apis/src/apis/rule_engine/rule_text';
import { LoadingState } from '@quality/common-utils';

import { Improvement } from '@/components/Analysis';
import Conlusion from '@/components/Analysis/Conclusion';
import Reason from '@/components/Analysis/Reason';
import LineThumbnail from '@/components/LineThumbnail';
import MetricName from '@/components/MetricName';
import {
  _getFormatTimeFilter,
  getReportName,
} from '@/components/TimeFilter/utils';
import UserAvatar from '@/components/UserAvatar';
import { QueryDataExtra } from '@/events/metric';
import { ReportConfigUrlQueryState } from '@/hooks/url/useReportConfigUrlQuery';
import type { StatValue } from '@/utils/metrics';
import {
  isMetaMetric,
  getMetricName,
  genStatDisplayValue,
  genStatValue,
} from '@/utils/metrics';

import { getRenderTagsProps } from '../utils';

import { CardContainer } from './common/MetricCardView';
import commonStyles from './common/styles.module.scss';
import styles from './StoryCard.module.scss';

const SmallTag: React.FC<TagProps> = ({ className, ...props }) => (
  <Tag className={clsx('!rounded-[32px]', className)} size="small" {...props} />
);

const StatDisplay: React.FC<{
  data: StatValue | null;
  loading: boolean;
  hasPermission: boolean;
  fullScreen: boolean;
}> = ({ data, loading, hasPermission, fullScreen }) => {
  const skeletonProps: SkeletonProps = {
    loading,
    text: { rows: 1 },
    animation: false,
  };

  const statDisplay = genStatDisplayValue(data);

  if (!hasPermission) {
    return (
      <Skeleton {...skeletonProps}>
        <div className={styles.statDisplayContainer}>
          <div className={styles.statNoPermission}>暂无权限查看</div>
        </div>
      </Skeleton>
    );
  }

  if (statDisplay == null) {
    return (
      <Skeleton {...skeletonProps}>
        <div className={styles.statDisplayContainer}>
          <div className={styles.stateDisplayValue}>NA</div>
        </div>
      </Skeleton>
    );
  }

  const { displayValue, unit } = statDisplay;

  return (
    <Skeleton {...skeletonProps}>
      <div className={styles.statDisplayContainer}>
        {fullScreen ? (
          <span className={styles.stateDisplayValue}>{displayValue}</span>
        ) : (
          <Typography.Text
            style={{ margin: 0 }}
            ellipsis={{
              rows: 1,
              cssEllipsis: false,
              showTooltip: { type: 'popover' },
            }}
            className={styles.stateDisplayValue}>
            {displayValue}
          </Typography.Text>
        )}
        <span className={styles.statDisplayUnit}>{unit}</span>
      </div>
    </Skeleton>
  );
};

const NoDomainMeasureObjEmpty: React.FC<{
  owner: string;
  domainMeasureObjName: string;
}> = ({ owner, domainMeasureObjName }) => (
  <Empty
    className="!p-0"
    description={
      <>
        <div className="text-text5">
          该指标未配置 {domainMeasureObjName} 领域度量对象
        </div>
        <div className="mt-1 !text-xs">
          请联系指标负责人
          <span className="text-brand5">
            @
            <UserAvatar username={owner} showAvatar={false} nameSize={12} />
          </span>
          配置
          <Link className="!text-xs" href={location.href}>
            刷新
          </Link>
        </div>
      </>
    }
  />
);

interface StoryCardViewProps {
  businessId: string;

  metricMeta: {
    data: MetricInfoAndExpertAnalysisInfo | undefined;
    loading: LoadingState;
  };
  metricQuery: {
    data: QueryData | undefined;
    loading: LoadingState;
    extra: QueryDataExtra | undefined;
  };

  templateInfo: ReportTemplate;

  expandDisabled?: boolean;
  fullScreen: boolean;
  supportDomainMeasureObjFilter?: boolean;

  tasks: ReportImprovementTask | undefined;

  actions: React.ReactNode;
  conclusionEmpty: React.ReactNode;

  reportReadonly: boolean;
  urlQuery: ReportConfigUrlQueryState;

  metricEmptyFullback: React.ReactNode;

  onClickMainContent: () => void;
}
// eslint-disable-next-line complexity
export const StoryCardView: React.FC<StoryCardViewProps> = props => {
  const {
    tasks,
    actions,
    urlQuery,
    metricMeta,
    businessId,
    metricQuery,
    templateInfo,
    reportReadonly,
    conclusionEmpty,
    expandDisabled = false,
    supportDomainMeasureObjFilter = true,
    onClickMainContent,
    metricEmptyFullback,
    fullScreen,
  } = props;

  const {
    nodeId,
    range = [],
    granularity,
    cycle,
    domainMeasureObjId = '',
    freezeVersion = '',
  } = urlQuery;
  const { extra } = metricQuery;
  const { requestParams: queryParams } = extra || {};
  const timeFilterInfo = _getFormatTimeFilter({
    granularity,
    cycle,
    range,
  });
  const hasPermission = metricMeta.data?.has_permission ?? true;
  const metricMetaLoading =
    metricMeta.loading === LoadingState.Loading ||
    metricMeta.loading === LoadingState.NotStarted;
  const metricQueryLoading =
    metricQuery.loading === LoadingState.Loading ||
    metricQuery.loading === LoadingState.NotStarted;

  const [isExpanded, setExpand] = useState(false);

  /* 根据这个标志位来避免已展开过的信息的重复请求（display: none;） */
  const [hasBeenExpanded, setHasBeenExpanded] = useState(false);

  const metricData = isMetaMetric(metricMeta.data)
    ? metricMeta.data?.meta_metric_data
    : metricMeta.data?.complex_metric_data;
  const conlusion = metricQuery.data?.chart_data?.rule_conclusion;
  const hiddenExpandBtn = !conlusion?.phrase_list?.length || expandDisabled;
  const tagsProps = getRenderTagsProps(conlusion?.tag);

  const improveTagProps =
    tasks?.metric_improvement_info?.[metricData?.id as string];

  const {
    name,
    owners,
    metric_info,
    description,
    metric_attribute,
    metric_expand_attribute,
  } = useMemo(() => {
    const isMeta = isMetaMetric(metricMeta.data);

    const {
      description = '',
      metric_info,
      metric_attribute,
      metric_expand_attribute,
    } = (isMeta
      ? metricMeta.data?.meta_metric_data
      : metricMeta.data?.complex_metric_data) || {};

    const name = getMetricName(metricMeta.data) || '';
    const owners = isMeta
      ? (metricMeta.data?.meta_metric_data?.owners as string[])
      : [metricMeta.data?.complex_metric_data?.common_data.owner as string];

    return {
      name,
      owners,
      metric_info,
      description,
      metric_attribute,
      metric_expand_attribute,
    };
  }, [metricMeta]);

  const statValue = useMemo<StatValue | null>(
    () =>
      genStatValue(metricData, metricQuery.data, isMetaMetric(metricMeta.data)),
    [metricMeta, metricQuery],
  );

  const thumbnailChart = useMemo(() => {
    if (
      metricQueryLoading ||
      !metricQuery.data ||
      !hasPermission ||
      !queryParams?.query_info ||
      !queryParams?.display_config ||
      !supportDomainMeasureObjFilter
    ) {
      return null;
    }
    return (
      <LineThumbnail
        dataSource={metricQuery.data}
        queryInfo={queryParams.query_info}
        displayConfig={queryParams.display_config}
        fullScreen={fullScreen}
      />
    );
  }, [
    metricQuery,
    hasPermission,
    queryParams?.query_info,
    queryParams?.display_config,
    metricQueryLoading,
    supportDomainMeasureObjFilter,
    fullScreen,
  ]);

  const reportFullName = useMemo(() => {
    if (!templateInfo) {
      return '';
    }
    return getReportName({ ...templateInfo });
  }, [templateInfo]);

  const expertAnalysisProps = {
    template_id: templateInfo.template_id,
    editable: !reportReadonly,
    business_id: businessId,
    metric_id: metricData?.id || '',
    metricData,
    start_time: range[0],
    timeFilterInfo,
    end_time: range[1],
    metric_expand_attribute,
    metric_name: name,
    report_name: reportFullName,
    report_url: location.href,
  };

  const domainMeasureObjName = {
    [PeopleMeasureObjID]: '人员',
    [CodeRepoMeasureObjID]: '代码仓',
    [ProjectMeasureObjID]: '项目',
    [PSMMeasureObjID]: '服务',
    [APPMeasureObjID]: 'APP',
    [ProductMeasureObjID]: '产品',
  }[domainMeasureObjId];

  const onExpand = () => {
    const expanded = !isExpanded;
    if (expanded) {
      setHasBeenExpanded(true);
    }
    setExpand(expanded);
  };

  return (
    <div className={styles.cardViewWrapper}>
      {(metricMetaLoading || !metricData) && (
        <div className="relative top-5 right-5">{actions}</div>
      )}

      <Skeleton
        className={commonStyles.cardContainer}
        loading={metricMetaLoading}
        text={{ rows: 3 }}>
        {metricData ? (
          <>
            <CardContainer
              className={clsx(
                isExpanded && styles.expand,
                hiddenExpandBtn &&
                  (fullScreen
                    ? styles.noExpandBtnFullscreen
                    : styles.noExpandBtn),
                !hasPermission && styles.noPermission,
                fullScreen && styles.fullscreenContainer,
              )}
              style={{
                ...(fullScreen
                  ? {
                      cursor: 'default',
                      border: 0,
                      backgroundColor: 'rgba(255, 255, 255, 0.04)',
                    }
                  : {}),
              }}>
              <div
                onClick={e => {
                  e.stopPropagation();
                  onClickMainContent();
                }}
                className={clsx(
                  styles.mainContent,
                  (!supportDomainMeasureObjFilter || !hasPermission) &&
                    '!cursor-not-allowed',
                )}
                data-metric={metricData?.id}>
                <div className={styles.header}>
                  <div
                    className={clsx(
                      styles.headerTitle,
                      !hasPermission && styles.titleNoPermission,
                    )}
                    title={name}>
                    {name}
                  </div>

                  {actions}
                </div>

                <Skeleton loading={metricQueryLoading} text={{ rows: 1 }}>
                  <div
                    className={clsx([
                      styles.tagContent,
                      !supportDomainMeasureObjFilter && 'invisible',
                    ])}>
                    {!hasPermission ? (
                      <SmallTag color="red">
                        <span>敏感指标</span>
                      </SmallTag>
                    ) : tagsProps?.text ? (
                      <SmallTag color={tagsProps.color}>
                        <span> {tagsProps.text}</span>
                      </SmallTag>
                    ) : (
                      <SmallTag color="gray">
                        <span>正常</span>
                      </SmallTag>
                    )}
                    {improveTagProps && (
                      <SmallTag
                        className="ml-2"
                        color={
                          improveTagProps.status === TextStatusType.Critical
                            ? 'red'
                            : 'green'
                        }>
                        <span>{improveTagProps.text}</span>
                      </SmallTag>
                    )}
                  </div>
                </Skeleton>

                <div className="flex justify-between">
                  <div className="flex flex-col max-w-[calc(100%-168px)]">
                    <MetricName
                      ellipsis={true}
                      className="mb-3 text-sm text-gray-500"
                      name={name}
                      owners={owners}
                      description={description}
                      metricInfo={metric_info}
                      metricAttribute={metric_attribute}
                      inMetricCard={true}
                    />

                    <StatDisplay
                      hasPermission={hasPermission}
                      loading={metricQueryLoading}
                      data={statValue}
                      fullScreen={fullScreen}
                    />
                  </div>

                  <div className="w-[164px]">{thumbnailChart}</div>
                </div>

                <Divider className="!my-5" />
              </div>

              {/* 额外展开的内容 */}
              <MacScrollbar suppressScrollX={true}>
                <Skeleton loading={!hasPermission} text={{ rows: 3 }}>
                  <div className={styles.advanced}>
                    {supportDomainMeasureObjFilter ? (
                      <Conlusion
                        loading={metricQueryLoading}
                        conlusion={conlusion}
                        dataUnit={statValue?.unit}
                        empty={conclusionEmpty}
                        isShowPeriodView={true}
                      />
                    ) : (
                      <NoDomainMeasureObjEmpty
                        domainMeasureObjName={domainMeasureObjName}
                        owner={owners[0]}
                      />
                    )}
                    {hasBeenExpanded ? (
                      <div style={{ display: isExpanded ? 'block' : 'none' }}>
                        {/* 波动分析暂时不上 */}
                        {
                          <>
                            <Divider className="!my-2" />
                            <Reason
                              listMaxHeight={360}
                              queryParams={queryParams}
                              dataSource={metricQuery.data!}
                              metricData={metricData}
                              nodeId={nodeId}
                              expertProps={expertAnalysisProps}
                              timeFilterInfo={
                                {
                                  cycle: urlQuery.cycle,
                                  range: urlQuery.range || [],
                                  granularity: urlQuery.granularity,
                                }!
                              }
                            />
                          </>
                        }

                        <Divider className="!my-2" />
                        <Improvement
                          analysisProps={expertAnalysisProps}
                          freezeVersion={freezeVersion}
                        />
                      </div>
                    ) : null}
                  </div>
                </Skeleton>
              </MacScrollbar>
            </CardContainer>

            <div
              // 没有结论/没有权限时隐藏展开/收起按钮
              className={clsx(
                styles.btnExpand,
                (!hasPermission || hiddenExpandBtn) && '!hidden',
              )}>
              <Button
                long={true}
                onClick={onExpand}
                icon={isExpanded ? <IconUp /> : <IconDown />}>
                {isExpanded ? '收起' : '展开'}
              </Button>
            </div>
          </>
        ) : (
          <CardContainer className="justify-center">
            {metricEmptyFullback}
          </CardContainer>
        )}
      </Skeleton>
    </div>
  );
};
