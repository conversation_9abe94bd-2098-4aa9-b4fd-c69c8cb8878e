.container {
  @apply bg-white rounded border border-[#E5E6EB] border-solid shadow-2xl w-[480px] py-5 px-5;
  .title {
    @apply text-lg font-medium mb-5;
  }
  .arco-list-item-main {
    box-shadow: none !important;
  }

  .metricList {
    @apply border-0 rounded-none ;

    /* arco 样式覆盖 */
    > .arco-list-content {
      @apply pt-5 px-4;
    }
    .itemContainer {
      @apply flex justify-between items-center p-3
                  rounded border border-[#E5E6EB] border-solid
                  mb-2 hover:bg-gray-100 cursor-pointer;

      &.active {
        @apply border border-[#1664ff] border-solid;
      }
      .itemContent {
        @apply flex flex-col;
      }
    }
  }
}
