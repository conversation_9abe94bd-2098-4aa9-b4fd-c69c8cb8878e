import { FC } from 'react';

import { Subject } from 'rxjs';

import { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { MetricInfoAndExpertAnalysisInfo } from '@quality/common-apis/src/apis/api/insight_report/report';
import { ReportTemplate } from '@quality/common-apis/src/apis/api/insight_report/report_model';
import { LoadingState } from '@quality/common-utils';

import {
  MetricQueryFetchPayload,
  MetricSubjectEvent,
  QueryDataExtra,
} from '@/events/metric';
import { MetricInfoLite } from '@/model/report/types';
import { MetricsModule } from '@/modules/metrics';

import { PlateStyledElementProps } from '../../types';

export interface MetricCardContainerChildrenProps {
  metrics: typeof MetricsModule;
  readonly: boolean;
  metricInfo: MetricInfoLite;
  templateInfo: ReportTemplate;
  metricMeta: {
    data: MetricInfoAndExpertAnalysisInfo | undefined;
    loading: LoadingState;
  };
  metricQuery: {
    data: QueryData | undefined;
    loading: LoadingState;
    extra: QueryDataExtra | undefined;
  };
  fullScreen: boolean;
  grabber: React.ReactNode;
  onDelete: () => void;
  fetchMetricMeta: () => void;
  fetchMetricQuery: (
    payload?: Omit<MetricQueryFetchPayload, 'metric' | 'urlQuery'>,
  ) => void;
}

export interface MetricCardContainerProps extends PlateStyledElementProps {
  long?: boolean;

  index: number;
  metricSubject: Subject<MetricSubjectEvent> | undefined;
  readonly: boolean;
  metric: MetricInfoLite;

  children: FC<MetricCardContainerChildrenProps>;
}
