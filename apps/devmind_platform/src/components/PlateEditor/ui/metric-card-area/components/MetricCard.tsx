/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck

import * as React from 'react';
import { useState, useRef, useMemo } from 'react';

import {
  Button,
  Divider,
  Empty,
  Skeleton,
  Modal,
  Space,
} from '@arco-design/web-react';
import {
  IconDelete,
  IconDragDotVertical,
  IconEmpty,
} from '@arco-design/web-react/icon';
import { useRequest } from '@byted/hooks';
import { useModel } from '@jupiter/plugin-runtime/model';
import { useDeepCompareEffect } from 'ahooks';
import clsx from 'clsx';

import { reportTemplateServiceClient } from '@quality/common-apis/src/apis/api/insight_report/report';
import { ComplexMetricMetaData } from '@quality/common-apis/src/apis/api/zepto/complex_metric';
import {
  MetricTypeMeta,
  MetricTypeComplexMeta,
} from '@quality/common-apis/src/apis/api/zepto/consts';
import { MetricMetaData } from '@quality/common-apis/src/apis/api/zepto/metric_dict';
import { CHART_TYPE } from '@quality/common-components';
import { LoadingState } from '@quality/common-utils';

import { CommentButton } from '@/components/Layout/ReportLayout/CommentButton';
import LineThumbnail from '@/components/LineThumbnail';
import { useQueryRequest } from '@/components/MetricDetailDrawer/hooks';
import { MetricData } from '@/components/MetricDetailDrawer/interface';
import { getDefaultTimeRange } from '@/components/MetricDetailDrawer/utils';
import { summaryStoreUse } from '@/components/ReportSummary/summary-v2/store';
import {
  getFormatTimeFilter,
  getTimeCycle,
  getTimeGranularity,
} from '@/components/TimeFilter';
import { API_V1, DND_DELIMITER } from '@/constants';
import useReportConfigUrlQuery from '@/hooks/url/useReportConfigUrlQuery';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { reportModel } from '@/model/report';
import { MetricInfo } from '@/model/report/types';
import { ReportManager } from '@/modules/report';

import { useDndNode } from './hooks/useDndNode';
import { MetricContainer, MetricContent } from './MetricCard.styles';

const iconSizeProps = {
  width: 18,
  height: 18,
};
interface MetricCardProps {
  editorID: string;
  readOnly: boolean;
  metricInfo: MetricInfo;
  parentId: string;
  index: number;
  expandDisabled?: boolean;
  freezed?: boolean;
}
export const MetricCard: React.FC<MetricCardProps> = props => {
  const {
    readOnly,
    editorID,
    metricInfo: { metric_id, metric_type, display_type },
    parentId,
    index,
    expandDisabled,
    freezed,
  } = props;
  const { virtualItem, virtualSpaceKey } = useVirtualSpaceType();
  const spaceId = virtualItem?.SpaceId ?? '';
  const summaryData = summaryStoreUse.summaryData();
  const blockMeta = summaryData?.block_map?.[metric_id];

  const [hasPermission, setHasPermission] = useState(false);
  const [metaMetricData, setMetaMetricData] = useState<MetricMetaData | null>(
    null,
  );
  const [complexMetricData, setComplexMetricData] =
    useState<ComplexMetricMetaData | null>(null);
  const [loading, setLoading] = useState(true);

  const [
    {
      metrics,
      timeFilterInfo: timeFilterInfoFromModel,
      templateInfo,
      instanceInfo,
      fullScreen,
      loading: reportTemplateLoading,
    },
    { putMetricInfo },
  ] = useModel([
    reportModel,
    state => ({
      templateInfo: state.$templateInfo,
      instanceInfo: state.$instanceInfo,
      loading: state.loading,
      fullScreen: state.fullScreen,
      metrics: state.metrics,
      timeFilterInfo: state.$timeFilterValue,
    }),
  ]);

  const nodeRef = useRef<HTMLDivElement>(null);

  const dragOptions = useMemo(
    () => ({
      canDrag: !readOnly,
    }),
    [readOnly],
  );

  const dropOptions = useMemo(
    () => ({
      canDrop: () => !readOnly,
    }),
    [readOnly],
  );

  const { dropLine, isDragging, dragRef, onDragStart, onDragEnd } = useDndNode({
    id: [editorID, parentId, metric_id, display_type, index].join(
      DND_DELIMITER,
    ),
    nodeRef,
    drag: dragOptions,
    drop: dropOptions,
  });

  let {
    urlQuery: {
      nodeId,
      granularity,
      cycle,
      range = [],
      domainMeasureObjFilter = [],
      domainMeasureObjId,
    },
  } = useReportConfigUrlQuery();

  let formatTimeFilterInfo = React.useMemo(
    () => getFormatTimeFilter({ granularity, cycle, range }),
    [granularity, cycle, ...range],
  );
  // 优先从 url 拿，兜底从 model 拿
  let timeFilterInfo = React.useMemo(() => {
    timeFilterInfoFromModel ?? formatTimeFilterInfo;
    // 优先使用model内的周期设置
    const cycle =
      timeFilterInfoFromModel?.cycle ??
      getTimeCycle(formatTimeFilterInfo.cycle, formatTimeFilterInfo.cycle);
    const granularity =
      timeFilterInfoFromModel?.granularity ??
      getTimeGranularity(
        formatTimeFilterInfo.granularity,
        formatTimeFilterInfo.granularity,
      );
    return {
      cycle,
      granularity,
      range: formatTimeFilterInfo?.range,
    };
  }, [formatTimeFilterInfo, timeFilterInfoFromModel]);

  const isMetaMetric = metric_type === MetricTypeMeta;
  const isComplexMetric = metric_type === MetricTypeComplexMeta;
  const metricData = isMetaMetric
    ? metaMetricData
    : isComplexMetric
    ? complexMetricData
    : null;

  const { urlQuery } = useReportConfigUrlQuery();

  // 是否支持领域度量对象筛选
  const supportDomainMeasureObjFilter =
    !domainMeasureObjId ||
    Boolean(
      (metricData?.domain_measure_objs ?? [])?.find(
        ({ id }) => id === domainMeasureObjId,
      )?.dimension_id,
    );

  const { refresh: refreshMetricData } = useRequest(
    async () => {
      // metrics?.set.publishMetricMetaDataEvent(metric_id, undefined, true);
      const res =
        await reportTemplateServiceClient.GetMetricInfoAndExpertAnalysis({
          metric_id,
          metric_type,
          version: API_V1,
          space_type: virtualSpaceKey,
          project_id: nodeId,
          seal_version: urlQuery.freezeVersion,
        });

      /* TODO: 后续会把指标详情请求前置，而不是在组件内部请求 */
      /* 触发指标详情返回事件 */
      // metrics?.set.publishMetricMetaDataEvent(metric_id, res.data);
      metrics?.set.setMetricMetaDataMapWithRef('set', metric_id, res.data);

      if (res.data) {
        if (isMetaMetric) {
          const metricData = res.data.meta_metric_data!;
          setMetaMetricData(metricData);
          putMetricInfo({
            type: 'update',
            index,
            metricInfo: {
              editorID,
              metric_id,
              parent_id: parentId,
              metric_type,
              metaMetric: { detail: metricData },
            },
          });
        } else if (isComplexMetric) {
          const metricData = res.data.complex_metric_data!;
          setComplexMetricData(res.data.complex_metric_data!);
          putMetricInfo({
            type: 'update',
            index,
            metricInfo: {
              editorID,
              metric_id,
              parent_id: parentId,
              metric_type,
              complexMetric: { detail: metricData },
            },
          });
        }

        setHasPermission(Boolean(res.data.has_permission ?? true));
        setLoading(false);
      } else {
        setLoading(false);
      }
      ReportManager.getModule()!.metricReadyCount -= 1;
    },
    {
      ready: !reportTemplateLoading,
      auto: true,
      refreshDeps: [metric_id, metric_type, parentId, nodeId],
    },
  );

  const queryResponse = useQueryRequest(metricData as MetricData, {
    freezeVersion: urlQuery.freezeVersion,
    hackTrendAnalysis: true,
  });
  const {
    run,
    dataSource: queryData,
    cancel,
    loading: conlusionLoading,
    params: queryParams,
    displayConfig,
    queryInfo,
  } = queryResponse;
  const msg = (queryResponse as any).msg;

  const fetchQueryInfo = React.useCallback(() => {
    if (hasPermission && metricData && timeFilterInfo) {
      // FIXME: 这个接口既臃肿又杂乱，需要重新设计

      // 不支持领域度量对象的指标卡数据直接置空
      if (!supportDomainMeasureObjFilter) {
        putMetricInfo({
          type: 'update',
          index,
          metricInfo: {
            editorID,
            metric_id,
            parent_id: parentId,
            metric_type,
            [metric_type === MetricTypeMeta ? 'metaMetric' : 'complexMetric']: {
              queryData: {
                chart_data: null,
              },
            },
          },
        });
        return;
      }
      metrics?.set.publishMetricQueryDataEvent(
        metric_id,
        undefined,
        LoadingState.Loading,
      );
      run({
        chartType: CHART_TYPE.DOUBLE_AXIS,
        nodeId,
        customParams: {
          baselines: [],
          gen_rule_text: true,
          template_id: templateInfo?.template_id,

          report_id: instanceInfo?.report_id,
        },
        timeFilterInfo: {
          ...timeFilterInfo,
          range: getDefaultTimeRange(
            timeFilterInfo.range,
            timeFilterInfo.granularity,
          ),
        },
        anchorStartTime: timeFilterInfo.range[0],
        domainMeasureObjFilter: {
          measureObjId: domainMeasureObjId,
          filter: domainMeasureObjFilter,
        },
        spaceId,
      }).then(res => {
        const queryData = res?.dataSource;
        /* TODO: 后续会把指标查询信息请求前置，而不是在组件内部请求 */
        /* 触发指标查询信息返回事件 */
        metrics?.set.publishMetricQueryDataEvent(
          metric_id,
          queryData,
          LoadingState.Done,
        );
        metrics?.set.setMetricQueryDataMapWithRef('set', metric_id, queryData);

        /* 更新分析结论 */
        if (queryData) {
          putMetricInfo({
            type: 'update',
            index,
            metricInfo: {
              editorID,
              metric_id,
              parent_id: parentId,
              metric_type,
              [metric_type === MetricTypeMeta ? 'metaMetric' : 'complexMetric']:
                {
                  queryData,
                },
            },
          });
        }
      });
    }

    return () => cancel();
  }, [
    parentId,
    metricData,
    hasPermission,
    timeFilterInfo,
    supportDomainMeasureObjFilter,
    domainMeasureObjId,
    domainMeasureObjFilter?.sort()?.join(),
    spaceId,
    urlQuery.freezeVersion,
    run,
  ]);

  const thumbnailChart = React.useMemo(() => {
    if (
      !queryData ||
      !queryInfo ||
      !hasPermission ||
      !displayConfig ||
      conlusionLoading ||
      !supportDomainMeasureObjFilter
    ) {
      return null;
    }
    return (
      <LineThumbnail
        dataSource={queryData}
        queryInfo={queryInfo}
        displayConfig={displayConfig}
        fullScreen={fullScreen}
      />
    );
  }, [
    queryData,
    queryInfo,
    hasPermission,
    displayConfig,
    conlusionLoading,
    supportDomainMeasureObjFilter,
    fullScreen,
  ]);

  useDeepCompareEffect(fetchQueryInfo, [
    parentId,
    metricData,
    hasPermission,
    timeFilterInfo,
    supportDomainMeasureObjFilter,
    domainMeasureObjId,
    domainMeasureObjFilter?.sort()?.join(),
    spaceId,
    urlQuery.freezeVersion,
  ]);

  // useEffect(fetchQueryInfo, [fetchQueryInfo]);

  const onDelete = e => {
    e.stopPropagation();
    // 如果当前的卡片里面有富文本
    if (blockMeta?.has_rich_text) {
      Modal.confirm({
        title: '删除指标',
        content: '删除指标将同步删除概览下的富文本内容，是否确认？',
        okText: '是',
        cancelText: '否',
        okButtonProps: { status: 'danger' },
        onOk: () => {
          putMetricInfo({
            type: 'delete',
            metricInfo: props.metricInfo,
            index,
          });
        },
      });
    } else {
      putMetricInfo({
        type: 'delete',
        metricInfo: props.metricInfo,
        index,
      });
    }
  };

  const refreshButton = (
    <div className="text-xs text-[#80838A] flex items-center justify-center">
      {msg ? (
        <Space>
          <span title={msg}>{msg.slice(0, 40)}...</span>
          {!fullScreen && (
            <Button className="px-1" type="text" onClick={fetchQueryInfo}>
              刷新
            </Button>
          )}
        </Space>
      ) : (
        <Space>
          <IconEmpty style={{ fontSize: 36 }} />
          <span>暂无结论</span>
          {!fullScreen && (
            <Button
              className="px-1"
              type="text"
              size="mini"
              onClick={fetchQueryInfo}>
              刷新
            </Button>
          )}
        </Space>
      )}
    </div>
  );

  const deleteButton = (
    <div className="btn-delete" onClick={onDelete}>
      <IconDelete style={{ ...iconSizeProps }} />
    </div>
  );

  const grabber = (
    <div
      className="grabber"
      ref={dragRef}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}>
      <IconDragDotVertical style={{ ...iconSizeProps }} />
    </div>
  );

  const renderActions =
    readOnly || freezed ? null : (
      <div className="actions-wrapper">
        {deleteButton}
        <Divider type="vertical" style={{ margin: 0 }} />
        {grabber}
      </div>
    );

  return (
    <div
      id={`${display_type}-${metric_id}`}
      ref={nodeRef}
      style={{
        opacity: isDragging ? 0.5 : 1,
        boxSizing: 'content-box',
        border: '1px solid transparent',
        borderRadius: '4px',
      }}
      className={clsx('metricCard', {
        MetricCardLeftDropLine: dropLine === 'left',
        MetricCardRightDropLine: dropLine === 'right',
      })}>
      <Skeleton
        className="metricContainer"
        loading={loading}
        text={{ rows: 3 }}
        animation={true}>
        {(isMetaMetric && metaMetricData) ||
        (isComplexMetric && complexMetricData) ? (
          <MetricContent
            hasPermission={hasPermission}
            queryParams={queryParams}
            businessId={nodeId}
            isMetaMetric={isMetaMetric}
            isComplexMetric={isComplexMetric}
            metricData={metricData!}
            queryData={supportDomainMeasureObjFilter ? queryData : undefined}
            loading={conlusionLoading}
            actions={renderActions}
            refreshButton={refreshButton}
            thumbnailChart={thumbnailChart}
            supportDomainMeasureObjFilter={supportDomainMeasureObjFilter}
            expandDisabled={expandDisabled}
          />
        ) : (
          <MetricContainer className="justify-center">
            <Empty
              description={
                <>
                  指标数据查询失败
                  <Button
                    className="px-1"
                    type="text"
                    onClick={refreshMetricData}>
                    刷新
                  </Button>
                </>
              }
            />
          </MetricContainer>
        )}
      </Skeleton>
      {/* IFTRUE_prodEnv */}
      <CommentButton
        top={24}
        right={24}
        id={metric_id}
        quote={
          (metricData as MetricMetaData)?.display_name ||
          (metricData as ComplexMetricMetaData)?.name
        }
      />
      {/* FITRUE_prodEnv */}
    </div>
  );
};
