import { useModel } from '@jupiter/plugin-runtime/model';
import { PlateEditor, TElement } from '@udecode/plate';
import { DropTargetHookSpec, useDrop } from 'react-dnd';

import { reportModel } from '@/model/report';

import { DragItemNode } from '../../types';

import { TYPE } from './constants';

interface UseDropContainerOptions
  extends Omit<
    DropTargetHookSpec<DragItemNode, unknown, { isOver: boolean }>,
    'accept'
  > {
  id: string;
  nodeRef: any;
  readOnly: boolean;
}

export const useDropContainer = (
  editor: PlateEditor,
  element: TElement,
  { id, nodeRef, readOnly }: UseDropContainerOptions,
) => {
  const [_, { moveMetric }] = useModel([reportModel, _ => ({})]);

  const [{ isOver }, drop] = useDrop<
    DragItemNode,
    unknown,
    { isOver: boolean }
  >({
    accept: TYPE,
    collect: monitor => ({
      isOver: monitor.isOver(),
    }),
    canDrop: () => !readOnly,
    drop(dragItem, monitor) {
      const dropResult = monitor.getDropResult<{ isDropNode: boolean }>();
      if (
        (dropResult !== null && dropResult?.isDropNode == null) ||
        dropResult?.isDropNode
      ) {
        return;
      }
      moveMetric({
        editor,
        dragItem,
        dropItem: { id, element },
        direction: '',
        isContainer: true,
      });
    },
  });

  drop(nodeRef);

  return {
    isOver,
  };
};
