import { TagProps } from '@arco-design/web-react';
import { DropTargetMonitor } from 'react-dnd';

import { Tag } from '@quality/common-apis/src/apis/rule_engine/rule_text';

import { CONCLUSION_TAG_COLOR_MAP } from '@/constants';

import { DragItemNode, DropLineDirection } from '../types';

export { genStatValue, genStatDisplayValue } from '@/utils/metrics';

export interface GetHoverDirectionOptions {
  dragItem: DragItemNode;

  monitor: DropTargetMonitor;

  /**
   * The node ref of the node being dragged.
   */
  nodeRef: React.MutableRefObject<HTMLDivElement>;

  /**
   * Hovering node id.
   */
  id: string;
}

export const getHoverDirection = ({
  dragItem,
  id,
  monitor,
  nodeRef,
}: GetHoverDirectionOptions): DropLineDirection | undefined => {
  if (!nodeRef.current) {
    return;
  }

  const dragId = dragItem.id;

  if (dragId === id) {
    return;
  }

  const hoverBoundingRect = nodeRef.current?.getBoundingClientRect();

  const hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;

  const clientOffset = monitor.getClientOffset();
  if (!clientOffset) {
    return;
  }

  const hoverClientX = clientOffset.x - hoverBoundingRect.left;

  if (hoverClientX < hoverMiddleX) {
    return 'left';
  }

  if (hoverClientX >= hoverMiddleX) {
    return 'right';
  }
  return '';
};

export const getNewDirection = (
  previousDir: string,
  dir?: string,
): DropLineDirection | undefined => {
  if (!dir && previousDir) {
    return '';
  }

  if (dir === 'left' && previousDir !== 'left') {
    return 'left';
  }

  if (dir === 'right' && previousDir !== 'right') {
    return 'right';
  }
  return '';
};

export const getRenderTagsProps = (
  tag?: Tag,
): (TagProps & { text?: string }) | null => {
  if (!tag) {
    return null;
  }

  return {
    text: tag.text,
    color: CONCLUSION_TAG_COLOR_MAP[tag.status],
  };
};
