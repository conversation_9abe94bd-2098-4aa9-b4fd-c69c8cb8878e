import { createPluginFactory } from '@udecode/plate';

import {
  ELEMENT_METRIC_CARD_AREA,
  METRIC_CARD_GRAGGING_SIGNAL,
} from './constants';

export const createMetricCardAreaPlugin = createPluginFactory({
  key: ELEMENT_METRIC_CARD_AREA,
  isElement: true,
  isVoid: true,
  handlers: {
    /**
     * 这里需要选择性地阻止 slate-react 的 Editable 组件内部的拖拽事件避免 block 销毁重建
     * @see https://docs.slatejs.org/libraries/slate-react#editable
     */
    onDrop: editor => () => editor[METRIC_CARD_GRAGGING_SIGNAL] as boolean,
    onDragStart: editor => () => {
      // 这里阻止 slate-react 内部在 dragStart 事件中对 void 类型节点进行选中而导致的焦点突变
      return editor[METRIC_CARD_GRAGGING_SIGNAL] as boolean;
    },
  },
});
