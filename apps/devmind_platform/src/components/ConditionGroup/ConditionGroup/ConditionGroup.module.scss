.wrapper {
  display: flex;
  flex-direction: row;
  flex-grow: 1;

  .container {
    border: 1px solid rgba(28, 28, 35, 0.08);
    border-radius: 6px;
    padding: 15px 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-grow: 1;

    .content {
      overflow: auto;
      .condition {
        display: flex;
        flex-direction: column;
        flex-grow: 1;

        & > * {
          margin-bottom: 16px;
          width: 100%;
        }
      }
    }

    .buttons {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }

  .deleteButtonWrapper {
    // flex-shrink: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}
