import {
  FC,
  useState,
  useRef,
  useMemo,
  useLayoutEffect,
  useCallback,
} from 'react';

import { Form } from '@arco-design/web-react';
import { useDeepCompareEffect } from 'ahooks';
import cs from 'classnames';

import { CalcCondition } from '@quality/common-apis/src/apis/api/metric_platform/model';

import { AddButton } from '../AddButton';
import { ClearButton } from '../ClearButton';
import { ConditionItem } from '../ConditionItem';
import { DeleteButton } from '../DeleteButton';
import { ConditionGroupFormProps, ConditionGroupProps } from '../interface';
import { LogicSwitch } from '../LogicSwitch';
import {
  getDefaultCondition,
  getDefaultConditionGroup,
  getRootParentPath,
} from '../utils';

import style from './ConditionGroup.module.scss';

const ConditionGroup: FC<ConditionGroupProps> = props => {
  const {
    value,
    onChange,
    onDelete,
    innerId,
    parentPath,
    index,
    isTop,
    fieldOptions,
    disabled,
  } = props;

  const { conjunction_word, conditions = [], children_cond = [] } = value ?? {};

  const pathDivider = '.';

  const [lastHeight, setLastHeight] = useState(0);
  const [selfHeight, setSelfHeight] = useState(0);

  const conditionsRef = useRef<HTMLDivElement>(null);
  const domObserverCallback = () => {
    // 由于闭包拿不到更新的数据， 这里不能用conditions和children_cond的length来判断最后一个元素
    const lastDom =
      document.getElementById(`${parentPath}-last-group`) ||
      document.getElementById(`${parentPath}-last-condition`);

    setLastHeight(lastDom?.offsetHeight ?? 0);
    setSelfHeight(conditionsRef.current?.offsetHeight ?? 0);
  };
  const conditionsDomObserverRef = useRef<MutationObserver>(
    new MutationObserver(domObserverCallback),
  );
  useLayoutEffect(() => {
    conditionsDomObserverRef.current.observe(conditionsRef.current!, {
      childList: true,
      subtree: true,
    });
    return () => {
      conditionsDomObserverRef.current.disconnect();
    };
  }, []);

  const logicSwitchMarginTop = 16;
  // logicSwitch高度 = 面板高度 - 最后一个元素高度 / 2 - 最后一个元素mb - logicSwitchMarginTop
  const logicSwitchHeight = useMemo(
    () => selfHeight - lastHeight / 2 - 16 - logicSwitchMarginTop,
    [lastHeight, logicSwitchMarginTop, selfHeight, isTop],
  );

  // 逻辑按钮
  const showLogicSwitch = useMemo(() => {
    const conditionNum = conditions.length;
    const groupNum = children_cond.length;
    return conditionNum + groupNum > 1;
  }, [conditions.length, children_cond.length]);
  const handleLogicSwitchChange = (logic: string) => {
    onChange?.({ ...value, conjunction_word: logic });
  };
  const renderLogicSwitch = () =>
    showLogicSwitch && (
      <LogicSwitch
        value={conjunction_word}
        marginTop={logicSwitchMarginTop}
        height={logicSwitchHeight}
        disabled={disabled}
        onChange={handleLogicSwitchChange}
      />
    );

  // 条件渲染
  const handleDeleteCondition = useCallback(
    (delIndex: number) => {
      onChange?.({
        ...value!,
        conditions: conditions.filter((_, i) => i !== delIndex),
      });
    },
    [value],
  );
  const renderConditions = useCallback(
    () =>
      conditions.map((_, i) => (
        <Form.Item
          key={`${parentPath + pathDivider}conditions[${i}]`}
          field={`${parentPath + pathDivider}conditions[${i}]`}>
          <ConditionItem
            index={i}
            parentPath={`${parentPath + pathDivider}conditions[${i}]`}
            innerId={
              i === conditions.length - 1
                ? `${parentPath}-last-condition`
                : undefined
            }
            disabled={disabled}
            fieldOptions={fieldOptions}
            onDelete={handleDeleteCondition}
          />
        </Form.Item>
      )),
    [conditions, parentPath, handleDeleteCondition, fieldOptions],
  );

  // 条件组渲染
  const handleDeleteConditionGroup = useCallback(
    (delIndex: number) => {
      onChange?.({
        ...value!,
        children_cond: children_cond.filter((_, i) => i !== delIndex),
      });
    },
    [value],
  );
  const renderConditionGroups = useCallback(
    () =>
      children_cond.map((v, i) => (
        <Form.Item
          noStyle
          field={`${parentPath + pathDivider}children_cond[${i}]`}
          key={`${parentPath + pathDivider}children_cond[${i}]`}>
          <ConditionGroup
            value={v}
            index={i}
            parentPath={`${parentPath + pathDivider}children_cond[${i}]`}
            innerId={
              i === children_cond.length - 1
                ? `${parentPath}-last-group`
                : undefined
            }
            fieldOptions={fieldOptions}
            onDelete={handleDeleteConditionGroup}
          />
        </Form.Item>
      )),
    [children_cond, parentPath],
  );

  // 添加条件/条件组按钮
  const handleAddCondition = useCallback(() => {
    onChange?.({
      ...value!,
      conditions: [...conditions, getDefaultCondition()],
    });
  }, [value]);
  const handleAddConditionGroup = useCallback(() => {
    onChange?.({
      ...value!,
      children_cond: [...children_cond, getDefaultConditionGroup()],
    });
  }, [value]);
  const renderAddButton = useCallback(() => {
    return (
      <AddButton
        disabled={disabled}
        onPrefixClick={handleAddCondition}
        onDropdownClick={handleAddConditionGroup}
      />
    );
  }, [disabled, handleAddCondition, handleAddConditionGroup]);

  // 清除全部按钮
  const handleClear = useCallback(() => {
    onChange?.(getDefaultConditionGroup());
  }, []);
  const renderClearButton = useCallback(() => {
    return isTop && <ClearButton disabled={disabled} onClick={handleClear} />;
  }, [handleClear, disabled, isTop]);

  // 删除条件组按钮
  const renderDeleteButton = useCallback(() => {
    return (
      !isTop &&
      !disabled && (
        <DeleteButton content="删除条件组" onClick={() => onDelete?.(index)} />
      )
    );
  }, [onDelete, index, isTop, disabled]);

  return (
    <div id={innerId} className={style.wrapper}>
      <div
        className={cs(style.container)}
        style={isTop ? { border: 'none' } : {}}>
        <div className={`${style.content} clear-both`}>
          {renderLogicSwitch()}
          <div className={style.condition} ref={conditionsRef}>
            {renderConditions()}
            {renderConditionGroups()}
          </div>
        </div>
        <div className={style.buttons}>
          {renderAddButton()}
          {renderClearButton()}
        </div>
      </div>
      {renderDeleteButton()}
    </div>
  );
};

export const ConditionGroupForm: FC<ConditionGroupFormProps> = props => {
  const { value, onChange, fieldOptions, disabled } = props;
  const [form] = Form.useForm<Record<string, CalcCondition>>();

  const rootPath = getRootParentPath();
  const defaultInitialValue = value ?? getDefaultConditionGroup();

  // 标识是否是组件本身触发的value change
  const selfChangeFlagRef = useRef<boolean>(false);
  const handleValuesChange = useCallback((_, values) => {
    selfChangeFlagRef.current = true;
    onChange?.(values[rootPath]);
  }, []);

  useDeepCompareEffect(() => {
    if (selfChangeFlagRef.current) {
      // 自身onChange导致的value不需要去setFieldsValue，然后重置标识
      selfChangeFlagRef.current = false;
      return;
    }
    // 外部form.setFieldsValue导致的value变化需要更新到组件内的Form上
    form.setFieldsValue({ [rootPath]: value ?? defaultInitialValue });
  }, [value]);

  return (
    <Form
      disabled={disabled}
      style={{ minWidth: 352, maxWidth: 1200, overflow: 'auto' }}
      form={form}
      initialValues={{ [rootPath]: defaultInitialValue }}
      wrapperCol={{ span: 24 }}
      onValuesChange={handleValuesChange}>
      {/*
          递归子组件的onChange不会触发父组件的render，
          导致父组件拿到的子组件value还是旧值，父组件onChange的时候就会用子组件的旧值覆盖新值，用shouldUpdate强制从根组件更新，
          后续卡顿就去掉shouldUpdate，在ConditionGroup组件onChange的时候通过form.getFieldValue(parentPath)拿到最新值
      */}
      <Form.Item field={rootPath} noStyle shouldUpdate>
        {() => (
          <ConditionGroup
            isTop
            disabled={disabled}
            parentPath={rootPath}
            index={0}
            fieldOptions={fieldOptions}
          />
        )}
      </Form.Item>
    </Form>
  );
};
