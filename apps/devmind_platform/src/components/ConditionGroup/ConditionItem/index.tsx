import { FC } from 'react';

import { Select } from '@arco-design/web-react';

import {
  ConditionOpEqual,
  ConditionOpNotEqual,
  ConditionOpIN,
  ConditionOpNotIN,
  ConditionOpGte,
  ConditionOpGteEqual,
  ConditionOpLess,
  ConditionOpLessOrEqual,
} from '@quality/common-apis/src/apis/api/metric_platform/consts';

import { DeleteButton } from '../DeleteButton';
import { ConditionItemProps } from '../interface';

import style from './ConditionItem.module.scss';

const ConditionItemOpList = [
  ConditionOpEqual,
  ConditionOpNotEqual,
  ConditionOpIN,
  ConditionOpNotIN,
  ConditionOpGte,
  ConditionOpGteEqual,
  ConditionOpLess,
  ConditionOpLessOrEqual,
];

export const ConditionItem: FC<ConditionItemProps> = props => {
  const { value, onChange, onDelete, innerId, index, fieldOptions, disabled } =
    props;

  const handleFieldChange = (field: string) => {
    onChange?.({ ...value!, field });
  };
  const handleOpChange = (op: string) => {
    onChange?.({ ...value!, op });
  };
  const handleValueChange = (v: string[]) => {
    onChange?.({ ...value!, value: v });
  };

  const handleDelete = () => {
    onDelete?.(index);
  };

  return (
    <div id={innerId} className={style.container}>
      <Select
        disabled={disabled}
        className={style.fieldKey}
        placeholder="请选择"
        value={value?.field || undefined}
        options={fieldOptions}
        onChange={handleFieldChange}
      />
      <Select
        disabled={disabled}
        className={style.operator}
        placeholder="请选择"
        options={ConditionItemOpList}
        value={value?.op || undefined}
        onChange={handleOpChange}
      />
      <Select
        disabled={disabled}
        value={value?.value}
        className={style.fieldValue}
        placeholder="请输入，按回车确认"
        allowCreate
        mode="multiple"
        allowClear
        options={value?.value ?? []}
        onChange={handleValueChange}
      />
      {!disabled && <DeleteButton content="删除条件" onClick={handleDelete} />}
    </div>
  );
};
