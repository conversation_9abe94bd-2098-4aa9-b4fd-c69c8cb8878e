import { FC } from 'react';

import { Button } from '@arco-design/web-react';

interface ClearButtonProps {
  disabled?: boolean;
  onClick?: () => void;
}

export const ClearButton: FC<ClearButtonProps> = props => {
  const { onClick, disabled } = props;

  return (
    <Button
      disabled={disabled}
      type="text"
      style={{ color: '#86909c' }}
      onClick={onClick}>
      清空全部条件
    </Button>
  );
};
