import { FC } from 'react';

import { But<PERSON>, Divider, Dropdown, Menu } from '@arco-design/web-react';
import { IconPlusCircleFill, IconCaretDown } from '@arco-design/web-react/icon';

interface AddButtonProps {
  disabled?: boolean;
  onPrefixClick?: () => void;
  onDropdownClick?: () => void;
}

export const AddButton: FC<AddButtonProps> = props => {
  const { disabled, onPrefixClick, onDropdownClick } = props;

  return (
    <div className="flex items-center">
      <Button
        disabled={disabled}
        type="text"
        icon={<IconPlusCircleFill />}
        onClick={onPrefixClick}>
        添加筛选条件
      </Button>
      <Divider type="vertical" className="!m-0" />
      <Dropdown
        disabled={disabled}
        droplist={
          <Menu onClickMenuItem={onDropdownClick}>
            <Menu.Item key="add">添加筛选组条件</Menu.Item>
          </Menu>
        }
        position="bl">
        <Button
          disabled={disabled}
          type="text"
          icon={<IconCaretDown />}
          iconOnly
        />
      </Dropdown>
    </div>
  );
};
