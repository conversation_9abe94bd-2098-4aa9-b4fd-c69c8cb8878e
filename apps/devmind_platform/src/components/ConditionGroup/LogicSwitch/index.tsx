import { FC } from 'react';

import {
  ConjunctionWorldAnd,
  ConjunctionWorldMap,
  ConjunctionWorldOR,
} from '@quality/common-apis/src/apis/api/metric_platform/consts';

import style from './LogicSwitch.module.scss';

export interface LogicSwitchProps {
  disabled?: boolean;
  value?: string;
  onChange?: (v: string) => void;
  marginTop?: number;
  height?: number;
}

export const LogicSwitch: FC<LogicSwitchProps> = props => {
  const {
    value = ConjunctionWorldAnd,
    onChange,
    marginTop = 0,
    height = 12,
    disabled,
  } = props;
  const onClick = () => {
    !disabled &&
      onChange?.(
        value === ConjunctionWorldAnd
          ? ConjunctionWorldOR
          : ConjunctionWorldAnd,
      );
  };

  const archStyle = { height: `${(height - (16 + 4 + 4)) / 2}px` };

  return (
    <div style={{ marginTop: `${marginTop}px` }} className={style.container}>
      <div style={archStyle} className={`${style.arch} ${style.archTop}`} />
      <div className={style.switch} onClick={onClick}>
        <p>{ConjunctionWorldMap[value]}</p>
        <iconpark-icon name="sort" />
      </div>
      <div style={archStyle} className={`${style.arch} ${style.archBottom}`} />
    </div>
  );
};
