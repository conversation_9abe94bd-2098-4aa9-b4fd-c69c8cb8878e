.container {
  float: left;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-right: 12px;
  width: 38px;

  // 防止文字被选中
  user-select: none;

  .arch {
    width: 24px;
    min-height: 12px;
    border: 2px solid #e5e6eb;
    border-right: none;
  }

  .archTop {
    margin-bottom: 4px;
    border-radius: 6px 0;
    border-bottom: none;
  }

  .archBottom {
    margin-top: 4px;
    border-radius: 0 0 0 6px;
    border-top: none;
  }

  .switch {
    width: 32px;
    height: 16px;
    border-radius: 3px;
    margin-right: 6px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;

    &:hover {
      background: rgba(46, 46, 56, 0.06);
      cursor: pointer;
    }

    p {
      font-weight: 500;
      line-height: 16px;
      color: black;
    }

    svg {
      color: black;
      margin-right: 0.38px;
    }
  }
}
