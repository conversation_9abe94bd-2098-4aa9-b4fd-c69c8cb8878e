import {
  CalcCondition,
  Condition,
} from '@quality/common-apis/src/apis/api/metric_platform/model';

interface BaseProps {
  disabled?: boolean;
  parentPath: string;
  index: number;
  innerId?: string;
  fieldOptions?: string[] | { label: string; value: any }[];
  onDelete?: (index: number) => any;
}

export interface ConditionGroupFormProps {
  value?: CalcCondition;
  disabled?: boolean;
  fieldOptions?: string[] | { label: string; value: any }[];
  onChange?: (value: CalcCondition) => any;
}

export interface ConditionGroupProps extends BaseProps {
  value?: CalcCondition;
  isTop?: boolean;
  onChange?: (value: CalcCondition) => any;
}

export interface ConditionItemProps extends BaseProps {
  value?: Condition;
  onChange?: (value: Condition) => any;
}
