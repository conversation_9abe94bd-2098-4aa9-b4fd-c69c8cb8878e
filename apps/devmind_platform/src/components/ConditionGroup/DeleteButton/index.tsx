import { FC } from 'react';

import { But<PERSON>, Tooltip } from '@arco-design/web-react';
import { IconDelete } from '@arco-design/web-react/icon';

interface DeleteButtonProps {
  content?: string;
  onClick?: () => void;
}

export const DeleteButton: FC<DeleteButtonProps> = props => {
  const { content, onClick } = props;
  return (
    <div className="flex items-center justify-center ml-3">
      <Tooltip content={content} disabled={!content}>
        <Button
          className="!text-text4"
          type="text"
          icon={<IconDelete />}
          iconOnly
          onClick={onClick}
        />
      </Tooltip>
    </div>
  );
};
