import { ConjunctionWorldAnd } from '@quality/common-apis/src/apis/api/metric_platform/consts';
import {
  CalcCondition,
  Condition,
} from '@quality/common-apis/src/apis/api/metric_platform/model';

/**
 * 返回一个默认的条件
 * @returns
 */
export const getDefaultCondition: () => Condition = () => ({
  field: '',
  op: '',
  value: [],
});
/**
 * 返回一个默认的条件组
 * @returns
 */
export const getDefaultConditionGroup: () => CalcCondition = () => ({
  conjunction_word: ConjunctionWorldAnd,
  conditions: [getDefaultCondition()],
  children_cond: [],
});
/**
 * 返回表单根路径Field
 * @returns
 */
export const getRootParentPath = () => 'root';
