import { FC, useState, useEffect, useMemo } from 'react';

import { noop } from 'lodash';

import { QueryData } from '@quality/common-apis/src/apis/api/base/base_data';
import { DisplayConfig } from '@quality/common-apis/src/apis/api/base/display_config';
import { QueryInfo } from '@quality/common-apis/src/apis/api/query/query';
import { VisualPanel } from '@quality/common-components';

interface Props {
  dataSource: QueryData | undefined;
  queryInfo?: QueryInfo;
  displayConfig?: DisplayConfig;
  width?: number;
  height?: number;
  showTooltips?: boolean;
  fullScreen?: boolean;
}

const LineThumbnail: FC<Props> = props => {
  const {
    dataSource,
    queryInfo,
    displayConfig,
    width,
    height,
    showTooltips = true,
    fullScreen = false,
  } = props;

  const [echartRef, setEchartRef] = useState<any>(null);

  const shrinkingEChart = () => {
    const options = echartRef.getOption();
    echartRef.setOption({
      xAxis: {
        show: false,
        axisLabel: {
          formatter() {
            return '';
          },
        },
      },
      yAxis: options?.yAxis?.map(() => ({
        show: false,
        axisLabel: {
          formatter() {
            return '';
          },
        },
      })),
      series: options?.series?.map(() => ({
        symbol: 'none',
        label: { show: false },
        smooth: true,
        emphasis: { label: { show: false } },
      })),
      color: fullScreen
        ? ['#66FFF6', '#FF706D']
        : ['#0057fe', 'rgba(0,87,254,0.3)'],
      axisPointer: options?.axisPointer?.map(() => ({
        lineStyle: { opacity: 0 },
      })),
      grid: [
        {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
        },
      ],
    });
    echartRef.resize({ width: width ?? 164, height: height ?? 62 });
  };

  // 不需要展示评分指标下的元指标相关信息，只保留第一个评分指标/元指标数据
  const newDataSource = useMemo<QueryData | undefined>(
    () =>
      dataSource?.chart_data?.series?.length
        ? {
            ...dataSource,
            chart_data: {
              ...dataSource.chart_data,
              series: dataSource.chart_data.series.slice(0, 1),
            },
            data_unit: dataSource?.data_unit?.slice(0, 1),
          }
        : undefined,
    [dataSource],
  );

  useEffect(() => {
    echartRef && newDataSource && shrinkingEChart();
  }, [echartRef, newDataSource, fullScreen]);

  // 没数据的情况
  if (!dataSource?.chart_data?.series?.length) {
    return null;
  }

  return (
    <VisualPanel
      getEchartRef={setEchartRef}
      loading={false}
      showLegend={false}
      drillWhere={[]}
      onDrillBack={noop}
      hasMenuMetrics={[]}
      timeGranularitys={[]}
      dimensionConf={{}}
      displayConfig={displayConfig!}
      dataSource={newDataSource}
      query_info={queryInfo}
      onDrill={noop}
      showTooltips={showTooltips}
    />
  );
};

export default LineThumbnail;
