import { FC } from 'react';

import {
  Select,
  Typography,
  Avatar,
  Spin,
  SelectProps,
} from '@arco-design/web-react';
import { IconUserGroup } from '@arco-design/web-react/icon';
import { useSearch } from '@byted/hooks';
import { useControllableValue } from 'ahooks';

import { projectPermissionServiceClient } from '@quality/common-apis/src/apis/api/insight/permission';

import { API_V1 } from '@/constants';

import UserTag from './UserTag';

interface Props extends SelectProps {
  value?: string[];
  onChange?: (v: string[]) => void;
}

const PeopleSelect: FC<Props> = props => {
  const { value, onChange, ...restArcoSelectProps } = props;

  const [state, setState] = useControllableValue<string[]>(
    { value, onChange },
    {
      defaultValue: [],
    },
  );

  // 搜索lark成员
  const {
    data: searchPeopleList,
    onChange: searchPeople,
    loading: searchLoading,
    cancel: cancelSearchPeople,
  } = useSearch(
    async (name: string) => {
      if (!name) {
        return;
      }
      const res =
        await projectPermissionServiceClient.SearchInsightPermissionCandidates({
          version: API_V1,
          identify_name: name,
          query_obj: 'User',
        });
      return res.data ?? [];
    },
    { debounceInterval: 600 },
  );

  return (
    <Select
      className="people-selector"
      value={state}
      mode="multiple"
      showSearch={true}
      getPopupContainer={() =>
        document.getElementsByClassName('people-selector')[0]
      }
      placeholder="请输入用户名称搜索"
      notFoundContent={
        searchLoading ? (
          <div className="flex items-center justify-center">
            <Spin className="m-3" />
          </div>
        ) : (
          <div className="m-3 text-text3 text-sm">未找到相关结果</div>
        )
      }
      filterOption={false}
      renderFormat={(_, value) => <UserTag value={String(value)} />}
      {...restArcoSelectProps}
      onSearch={searchPeople}
      onBlur={cancelSearchPeople}
      onChange={setState}>
      {searchPeopleList?.map(
        ({ avatar, identify_key, identify_name, identify_type }) => (
          <Select.Option
            style={{ padding: 10 }}
            value={identify_key}
            key={identify_key}
            extra={{ identify_type, identify_name }}>
            <div className="flex items-center">
              <Avatar size={33} className="bg-brand5">
                {identify_type === 'User' && avatar ? (
                  <img src={avatar} />
                ) : (
                  <IconUserGroup />
                )}
              </Avatar>
              <div className="ml-3 w-[calc(100%-45px)]">
                <Typography.Title
                  className="!m-0"
                  ellipsis={{
                    showTooltip: {type: 'popover'},
                  }}
                  heading={6}>{`${identify_name}${
                  identify_type === 'User' ? `(${identify_key})` : ''
                }`}</Typography.Title>
              </div>
            </div>
          </Select.Option>
        ),
      )}
    </Select>
  );
};

export default PeopleSelect;
