import { FC } from 'react';

import { Result, Button } from '@arco-design/web-react';
import styled from '@jupiter/plugin-runtime/styled';

import { getNodeAuthApplyUrl } from '@/utils/auth';

import noPermImg from '../img/no_perm.png';
import { CustomError } from '../types';

const SCResult = styled(Result)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
`;

interface Options {
  id: string;
  title?: string;
  subTitle?: string;
}

export const NoPermission: FC<Options> = props => {
  const { id, title = '暂无权限', subTitle = '你当前暂无访问权限' } = props;

  return (
    <SCResult
      status={null}
      icon={<img src={noPermImg} />}
      title={title}
      subTitle={subTitle}
      extra={
        <>
          {/* IFTRUE_prodEnv */}
          <Button type="primary" href={getNodeAuthApplyUrl(id)}>
            申请权限
          </Button>
          {/* FITRUE_prodEnv */}
        </>
      }
    />
  );
};

export class NoPermissionError extends CustomError {
  id: string;

  title?: string;

  subTitle?: string;

  constructor(options: Options) {
    super();
    const { id, title, subTitle } = options;
    this.id = id;
    this.title = title;
    this.subTitle = subTitle;
  }

  getReactNode() {
    return (
      <NoPermission id={this.id} title={this.title} subTitle={this.subTitle} />
    );
  }
}
