import React, { FC } from 'react';

import styled from 'styled-components';

import { log } from '@quality/common-utils';

import errorPageImg from './img/error_page.png';
import { CustomError } from './types';

interface ErrorFallbackProps {
  error: Error;
}

const SCErrorFallback = styled.div`
  width: 280px;
  margin: 200px auto 0;
  text-align: center;
`;

const SCErrorPageImg = styled.img`
  width: 240px;
  height: 240px;
  border-radius: 50%;
`;

const SCErrorPageText = styled.p`
  margin-top: 20px;
  font-size: 16px;
  color: var(--measure-text5);
`;

const ErrorFallback: FC<ErrorFallbackProps> = props => {
  const { error } = props;
  log(error);
  return error instanceof CustomError ? (
    <>{error.getReactNode()}</>
  ) : (
    <SCErrorFallback>
      <SCErrorPageImg src={errorPageImg} />
      <SCErrorPageText>页面出错，请联系度量OnCall</SCErrorPageText>
    </SCErrorFallback>
  );
};

export default ErrorFallback;
