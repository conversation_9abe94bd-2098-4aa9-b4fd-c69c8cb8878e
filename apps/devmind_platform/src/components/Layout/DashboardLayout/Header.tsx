import { FC, useEffect, useRef, useState } from 'react';

import {
  <PERSON><PERSON>,
  Divider,
  Dropdown,
  <PERSON>,
  Menu,
  Message,
  Modal,
  Radio,
  Space,
} from '@arco-design/web-react';
import { IconLeft, IconMore } from '@arco-design/web-react/icon';
import { useBoolean } from '@byted/hooks';
import { useModel } from '@reduck/core';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import { compile, match } from 'path-to-regexp';
import { useHistory } from 'react-router-dom';

import { reportTemplateServiceClient } from '@quality/common-apis/src/apis/api/insight_report/report';

import {
  ConfigMode,
  ReportConfigModal,
} from '@/components/Space/components/ReportConfigModal';
import {
  TimeCycleText,
  TimeGranularityText,
  getReportNameSuffix,
} from '@/components/TimeFilter';
import { API_V1, Authority, EDITOR_MODE } from '@/constants';
import { LogEventMap, LogSceneMap } from '@/constants/log';
import { useReportConfigUrlQuery } from '@/hooks/url';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import globalModel from '@/model/global';
import { reportModel } from '@/model/report';
import { NodeManager, transformNodeTree } from '@/modules/node';
import { useSaveReport } from '@/pages/dashboard/[templateId]/hooks/useSaveReport';
import { DevmindUrls, isSubModule, pathPrefix } from '@/routers/path';
import { hasAuthority } from '@/utils/auth';
import { downloadPDF } from '@/utils/downloadFile';
import { createUrlQuery, inDashboardUrl, sceneName } from '@/utils/url';
import { openURL, treeFlat, XLog } from '@/utils/utils';

import VirtualArea from '../BaseLayout/VirtualArea';

import style from './Header.module.scss';

export const HeaderLeftArea = ({ onClick }) => {
  const {
    urlQuery: {
      nodeId,
      range = [],
      granularity,
      cycle,
      from,
      domainMeasureObjId,
      domainMeasureObjFilter,
    },
  } = useReportConfigUrlQuery();
  const [_, { addNodeRecords }] = useModel([globalModel]);

  const spaceType = NodeManager.getModule()?.getSpaceType();

  const currentSpacePath = (() => {
    let path = inDashboardUrl()
      ? DevmindUrls.VirtualSpaceDashboardDetail
      : DevmindUrls.VirtualSpaceReportDetail;
    return path.replace('/:spaceType/', `/${spaceType}/`);
  })();

  const urlMatch = match(`${currentSpacePath}`)(
    location.pathname.replace(pathPrefix, ''),
  ) as any;
  const templateId = urlMatch?.params?.templateId;

  const handleNodeChange = (nodeId: string) => {
    const list = treeFlat(treeData, 'children');
    const { template_id, report_id } =
      list.find(({ node_id }) => node_id === nodeId) ?? {};
    if (!template_id) {
      Message.error('报告不存在');
      return;
    }
    openURL(
      `${pathPrefix}${compile<{ templateId: string }>(
        (() => {
          let path = inDashboardUrl()
            ? DevmindUrls.VirtualSpaceDashboardDetail
            : DevmindUrls.VirtualSpaceReportDetail;
          return path.replace('/:spaceType/', `/${spaceType}/`);
        })(),
      )({
        templateId: template_id,
      })}?${
        createUrlQuery({
          mode: EDITOR_MODE.Read,
          reportId: report_id,
          nodeId,
          granularity,
          cycle,
          range,
          from,
          freezeVersion: '',
          domainMeasureObjId,
          domainMeasureObjFilter,
        }).str
      }`,
    );
  };
  const { data: treeData = [], loading: treeDataLoading } = useRequest(
    async () => {
      const res = await reportTemplateServiceClient.GetSubReport({
        version: API_V1,
        template_id: templateId,
        space_type: spaceType!,
        time_range: { start_time: range[0], end_time: range[1] },
      });
      return res.data ?? [];
    },
    {
      refreshDeps: [templateId],
      ready: Boolean(templateId && spaceType && range.length),
    },
  );

  useEffect(() => {
    if (nodeId && treeData) {
      const { flatTree } = transformNodeTree(treeData);
      // 这里需要把 nodeIdRecord set到全局去，报告即可获取nodeName
      addNodeRecords(
        flatTree.map(item => item.node_id),
        flatTree,
      );
    }
  }, [nodeId, treeData]);

  return (
    <div className="flex gap-4 items-center">
      <span
        className="flex gap-3 items-center text-lg font-medium cursor-pointer"
        onClick={onClick}>
        <Link>
          <IconLeft className="text-sm text-black" />
        </Link>
        {sceneName()}详情
      </span>
      <Divider type="vertical" />
      <VirtualArea
        nodeId={nodeId}
        treeData={treeData ?? []}
        allowExpand={!treeDataLoading}
        logoSize={20}
        nameSize={16}
        onChange={handleNodeChange}
        isReportDetail={true}
      />
    </div>
  );
};

const Header: FC = () => {
  const {
    urlQuery: { nodeId, mode, granularity, cycle, range = [] },
    setUrlQuery,
  } = useReportConfigUrlQuery();
  const [{ nodeIdRecord }] = useModel([globalModel]);
  const { virtualSpaceKey } = useVirtualSpaceType();
  const history = useHistory();
  const spaceType = NodeManager.getModule()?.getSpaceType();

  const currentSpacePath = (() => {
    let path = DevmindUrls.VirtualSpaceDashboardDetail;
    return path.replace('/:spaceType/', `/${spaceType}/`);
  })();

  const urlMatch = match(`${currentSpacePath}`)(
    location.pathname.replace(pathPrefix, ''),
  ) as any;
  const templateId = urlMatch?.params?.templateId;

  const readOnly = mode === EDITOR_MODE.Read;

  const [
    { saving, reportDetail, fullScreen },
    { fetchTempAndInstance, setFullScreen },
  ] = useModel([
    reportModel,
    state => ({
      saving: state.saveing,
      reportDetail: state.reportDetail,
      fullScreen: state.fullScreen,
    }),
  ]);

  const { template_info } = reportDetail;

  const node_name = nodeIdRecord[nodeId]?.node_name ?? '无组织节点';

  // const hasAdminAuth = hasAuthority(
  //   template_info?.authorization_type,
  //   Authority.admin,
  // );
  const hasEditAuthority = hasAuthority(
    template_info?.authorization_type as string,
    Authority.write,
  );

  const handleModeChange = () => {
    if (readOnly) {
      XLog({
        id: 'webclick',
        page_id: LogSceneMap.dashboard.value,
        page_title: LogSceneMap.dashboard.type,
        btn_title: {
          dashboard: reportDetail?.template_info?.display_name,
          event: LogEventMap.dashboard.dashboardStartEdit,
        },
      });
    }
    setUrlQuery({
      mode: readOnly ? EDITOR_MODE.Write : EDITOR_MODE.Read,
    });
  };

  const goBack = () => {
    const pathname = (() => {
      return DevmindUrls.VirtualSpaceDashboardList.replace(
        '/:spaceType/',
        `/${virtualSpaceKey}/`,
      );
    })();

    history.push({
      pathname,
      search: createUrlQuery({
        nodeId,
      }).str,
    });
  };

  const handleGoBack = () => {
    if (readOnly) {
      goBack();
    } else {
      Modal.confirm({
        title: '离开提示',
        content: '系统可能不会保存您所做的更改。',
        onOk: goBack,
      });
    }
  };

  const { saveReport } = useSaveReport();
  const handleSubmit = async () => {
    await saveReport();
    handleModeChange();
  };

  const handleCancel = async () => {
    handleModeChange();
    fetchTempAndInstance(templateId, '', nodeId);
  };

  const isDeleteAllRef = useRef<boolean>(true);
  const { run: handleDeleteReport } = useRequest(
    async (templateId: string, business_id: string) => {
      const res = await reportTemplateServiceClient.DeleteTemplateAndInstance({
        version: API_V1,
        template_id: templateId,
        is_delete_all: isDeleteAllRef.current,
        business_id,
      });
      if (Number(res.code) !== 200) {
        return;
      }
      goBack();
      Message.success(`删除成功`);
    },
    { manual: true },
  );
  const handleDeleteButtonClick = () => {
    if (!hasEditAuthority) {
      Message.error({
        content: <>没有节点编辑者权限</>,
      });
      return;
    }
    if (template_info?.parent_template_name && template_info.parent_id) {
      Modal.confirm({
        title: `确定删除该仪表盘吗？`,
        content: (
          <>
            此仪表盘为继承 「{template_info.parent_template_name}」 仪表盘
            ，删除后， 仪表盘 在本节点下的相关历史也同步删除，且不再同步此仪表盘
            ，请确认是否要删除？
          </>
        ),
        okText: '删除',
        okButtonProps: { status: 'danger' },
        onOk: () =>
          handleDeleteReport(
            template_info.template_id || templateId,
            template_info.business_info.business_id || nodeId,
          ),
        afterClose: () => (isDeleteAllRef.current = false),
      });
      return;
    }
    Modal.confirm({
      title: `确定删除该仪表盘吗？`,
      content: (
        <>
          <div>删除后，当前仪表盘及相关历史也同步删除，请确认是否要删除？</div>
          <Radio.Group
            className="mt-1"
            direction="vertical"
            defaultValue={true}
            onChange={v => (isDeleteAllRef.current = v)}>
            <Radio value={true}>删除当前及下级引用的全部仪表盘</Radio>
            <Radio value={false}>仅删除当前仪表盘</Radio>
          </Radio.Group>
        </>
      ),
      okText: '删除',
      okButtonProps: { status: 'danger' },
      onOk: () =>
        handleDeleteReport(
          template_info.template_id || templateId,
          template_info.business_info.business_id || nodeId,
        ),
      afterClose: () => (isDeleteAllRef.current = false),
    });
  };

  const {
    state: configModalVisible,
    setTrue: openConfigModal,
    setFalse: closeConfigModal,
  } = useBoolean();
  const [reportConfigInfo, setReportConfigInfo] = useState<{
    mode: ConfigMode;
    templateId: string;
    reportId: '';
  }>({ mode: ConfigMode.Edit, templateId: '', reportId: '' });
  const handleOpenModal = (data: { mode: ConfigMode; templateId: string }) => {
    setReportConfigInfo({ ...data, reportId: '' });
    openConfigModal();
  };

  const goFullScreen = () => {
    setFullScreen(true);
    // 设置为暗黑主题
    document.body.setAttribute('arco-theme', 'dark');
    document.documentElement.requestFullscreen();
  };

  const handleFullscreenChange = () => {
    if (document.fullscreenElement) {
      setFullScreen(true);
      // 设置为暗黑主题
      document.body.setAttribute('arco-theme', 'dark');
    } else {
      // 恢复亮色主题
      document.body.removeAttribute('arco-theme');
      setFullScreen(false);
    }
  };
  useEffect(() => {
    // 监听全屏状态变动
    document.addEventListener('fullscreenchange', handleFullscreenChange, true);
    return () => {
      document.removeEventListener(
        'fullscreenchange',
        handleFullscreenChange,
        true,
      );
    };
  }, []);

  return (
    <div className={style.header} style={fullScreen ? { display: 'none' } : {}}>
      {isSubModule() ? <div></div> : <HeaderLeftArea onClick={handleGoBack} />}
      <Space>
        {mode === EDITOR_MODE.Write ? (
          <Space>
            <Button onClick={handleCancel}>取消</Button>
            <Button type="primary" loading={saving} onClick={handleSubmit}>
              保存
            </Button>
          </Space>
        ) : (
          <Button
            disabled={!hasEditAuthority}
            type="primary"
            onClick={handleModeChange}>
            编辑
          </Button>
        )}
        {!isSubModule() && (
          <Dropdown
            position="bl"
            droplist={
              <Menu>
                <Menu.Item
                  key="copy"
                  // disabled={!hasEditAuthority}
                  onClick={() =>
                    handleOpenModal({
                      mode: ConfigMode.Copy,
                      templateId,
                    })
                  }>
                  复制仪表盘
                </Menu.Item>
                <Menu.Item
                  key="download"
                  // disabled={!hasEditAuthority}
                  onClick={() => {
                    // 获取实时时间
                    const reportName = `${node_name}-${
                      template_info?.display_name
                    }${getReportNameSuffix(
                      TimeGranularityText[granularity],
                      TimeCycleText[cycle],
                      range,
                    )}-${dayjs().format('YYYYMMDDHHmmss')}`;
                    downloadPDF(
                      'report-content',
                      'dashboard',
                      `${reportName}.pdf`,
                    );
                  }}>
                  仪表盘下载
                </Menu.Item>
                <Menu.Item
                  key="view"
                  // 保持同删除权限一致
                  disabled={!hasEditAuthority}
                  onClick={() =>
                    handleOpenModal({
                      mode: ConfigMode.Edit,
                      templateId,
                    })
                  }>
                  仪表盘信息
                </Menu.Item>
                {mode === EDITOR_MODE.Read && (
                  <Menu.Item key="fullScreen" onClick={goFullScreen}>
                    大屏模式
                  </Menu.Item>
                )}
                <Menu.Item
                  key="del"
                  disabled={!hasEditAuthority}
                  onClick={handleDeleteButtonClick}
                  style={{ color: hasEditAuthority ? 'red' : '' }}>
                  删除
                </Menu.Item>
              </Menu>
            }>
            <Button iconOnly icon={<IconMore />} />
          </Dropdown>
        )}
      </Space>
      <ReportConfigModal
        readOnly={readOnly}
        visible={configModalVisible}
        {...reportConfigInfo}
        onCancel={closeConfigModal}
        nodeId={nodeId}
        timeFilterInfo={defaultTimeValue}
        onSuccess={() => fetchTempAndInstance(templateId, '', nodeId)}
      />
    </div>
  );
};

export default Header;
