import { FC, ReactChildren } from 'react';

import { Layout } from '@arco-design/web-react';
import { useModel } from '@reduck/core';
import cs from 'classnames';

import { reportModel } from '@/model/report';

import Header from './Header';
import '../ReportLayout/styles.scss';

interface DashboardLayoutProps {
  children?: ReactChildren;
}

const DashboardLayout: FC<DashboardLayoutProps> = props => {
  const [{ fullScreen }] = useModel([reportModel]);

  return (
    <Layout className={cs(['reportLayout', fullScreen && 'fullScreenLayout'])}>
      <Header />
      <div
        className={cs([
          'pr-6 pl-8 flex-1 overflow-y-auto h-0',
          fullScreen && 'pr-0 pl-0',
        ])}
        style={{ color: fullScreen ? 'white' : '#020814' }}>
        {props.children}
      </div>
    </Layout>
  );
};

export default DashboardLayout;
