import React, { FC } from 'react';

import { useLocation } from '@jupiter/plugin-runtime/router';
import { useModel } from '@reduck/core';

import VeThoughtHeader from '@/components/VeThoughtHeader';
import { useRequestSpaceTree } from '@/hooks/useSpaceType';
import globalModel from '@/model/global';
import { getRouteConfigByPath } from '@/routers';
import { isSubMenu } from '@/routers/path';

import BaseLayout from './BaseLayout';

const DevmindLayout: FC = props => {
  const { pathname } = useLocation();
  useRequestSpaceTree();
  const [{ newRoutes: routes }] = useModel([
    globalModel,
    ({ newRoutes }) => ({ newRoutes }),
  ]);

  const routeConfig = getRouteConfigByPath(pathname, routes || []);

  if (!routeConfig?.layout) {
    return (
      <>
        {/* 没有layout 说明是父级，需要头部 */}
        {!isSubMenu() && <VeThoughtHeader />}
        <BaseLayout>{props.children}</BaseLayout>
      </>
    );
  }

  const CustomLayout = React.cloneElement(routeConfig.layout, {
    children: props.children,
  });

  return CustomLayout;
};

export default props => {
  return <DevmindLayout {...props} />;
};
