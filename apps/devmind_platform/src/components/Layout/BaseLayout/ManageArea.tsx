import { FC, useEffect } from 'react';

import { useModel } from '@reduck/core';
import { useLocation } from 'react-router-dom';

import SpaceSelector from '@/components/SpaceSelector';
import { useManageUrlQuery } from '@/hooks/url';
import globalModel from '@/model/global';
import DataManageSpaceSelector from '@/pages/data-manage/components/SpaceSelector';
import MetricStorySpaceSelector from '@/pages/metric-story-manage/components/SpaceSelector';
import { DataManageUrls, MetricStoryManageUrls } from '@/routers/path';
import { MANAGE_NODE_ID_INIT } from '@/utils/constant';

const urlsDisableChangeSpace = [
  MetricStoryManageUrls.MetricStoryFormCreate,
  MetricStoryManageUrls.MetricStoryFormEdit,
  DataManageUrls.DataModelCreate,
  DataManageUrls.DataModelDetails,
  '/metric-story/edit',
];

const ManageArea: FC = () => {
  const { pathname } = useLocation();
  const { urlQuery, setUrlQuery } = useManageUrlQuery();
  const [{ authData }] = useModel([globalModel]);

  // 判断用户是否为管理员（任一权限为true即为管理员）
  const isAdmin = Boolean(
    authData?.isAdmin || authData?.isDataManager || authData?.isDomainExpert,
  );

  useEffect(() => {
    if (!urlQuery.nodeId) {
      // 管理员默认选择专家空间，非管理员不设置默认节点（显示提示信息）
      if (isAdmin) {
        setUrlQuery({ nodeId: MANAGE_NODE_ID_INIT });
      }
    }
  }, [urlQuery.nodeId, isAdmin, setUrlQuery]);

  const allowExpand = !urlsDisableChangeSpace.find(u => pathname.includes(u));

  // 根据路径决定使用哪个 SpaceSelector
  const renderSpaceSelector = () => {
    const commonProps = {
      allowExpand,
      allowChangeSpaceType: true,
      nodeId: urlQuery.nodeId,
      onChange: (nodeId: string, spaceType?: string) => {
        setUrlQuery({ nodeId, spaceType });
      },
    };

    if (pathname.includes('/data-manage')) {
      return <DataManageSpaceSelector {...commonProps} />;
    }

    if (pathname.includes('/metric-story')) {
      return <MetricStorySpaceSelector {...commonProps} />;
    }

    // 默认使用全局的 SpaceSelector
    return <SpaceSelector {...commonProps} />;
  };

  return renderSpaceSelector();
};

export default ManageArea;
