import { FC, useEffect } from 'react';

import { useLocation } from 'react-router-dom';

import SpaceSelector from '@/components/SpaceSelector';
import { useManageUrlQuery } from '@/hooks/url';
import DataManageSpaceSelector from '@/pages/data-manage/components/SpaceSelector';
import MetricStorySpaceSelector from '@/pages/metric-story-manage/components/SpaceSelector';
import { DataManageUrls, MetricStoryManageUrls } from '@/routers/path';
import { MANAGE_NODE_ID_INIT } from '@/utils/constant';

const urlsDisableChangeSpace = [
  MetricStoryManageUrls.MetricStoryFormCreate,
  MetricStoryManageUrls.MetricStoryFormEdit,
  DataManageUrls.DataModelCreate,
  DataManageUrls.DataModelDetails,
  '/metric-story/edit',
];

const ManageArea: FC = () => {
  const { pathname } = useLocation();
  const { urlQuery, setUrlQuery } = useManageUrlQuery();

  useEffect(() => {
    if (!urlQuery.nodeId) {
      setUrlQuery({ nodeId: MANAGE_NODE_ID_INIT });
    }
  }, [urlQuery.nodeId]);

  const allowExpand = !urlsDisableChangeSpace.find(u => pathname.includes(u));

  // 根据路径决定使用哪个 SpaceSelector
  const renderSpaceSelector = () => {
    const commonProps = {
      allowExpand,
      allowChangeSpaceType: true,
      nodeId: urlQuery.nodeId,
      onChange: (nodeId: string, spaceType?: string) => {
        setUrlQuery({ nodeId, spaceType });
      },
    };

    if (pathname.includes('/data-manage')) {
      return <DataManageSpaceSelector {...commonProps} />;
    }

    if (pathname.includes('/metric-story')) {
      return <MetricStorySpaceSelector {...commonProps} />;
    }

    // 默认使用全局的 SpaceSelector
    return <SpaceSelector {...commonProps} />;
  };

  return renderSpaceSelector();
};

export default ManageArea;
