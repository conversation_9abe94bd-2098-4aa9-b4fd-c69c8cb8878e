import React, { useState, useEffect } from 'react';

import { Divider, Layout, Menu, Tooltip } from '@arco-design/web-react';
import { IconMenuFold, IconMenuUnfold } from '@arco-design/web-react/icon';
import { useModel } from '@jupiter/plugin-runtime/model';
import { useHistory, useLocation } from '@jupiter/plugin-runtime/router';
import styled from '@jupiter/plugin-runtime/styled';
import { useRequest } from 'ahooks';
import { compile } from 'path-to-regexp';
import { ErrorBoundary } from 'react-error-boundary';

import ErrorFallback from '@/components/ErrorFallback';
import { LogEventMap, LogSceneMap } from '@/constants/log';
import globalModel from '@/model/global';
import { getRouteConfigByPath } from '@/routers';
import { RouteConfig } from '@/routers/interface';
import { BaseVirtualSpaceUrls, DevmindUrls, isSubMenu } from '@/routers/path';
import { treeFlat, XLog } from '@/utils/utils';

import Header from './Header';

const { Sider, Content } = Layout;
const { Item: MenuItem, SubMenu, ItemGroup } = Menu;

const SCLayout = styled(Layout)`
  .arco-layout-sider,
  .arco-layout-sider-trigger,
  .arco-menu,
  .arco-menu-item {
    background: ${({ theme }) => `${theme.colors.fill2} !important`};
  }
  .arco-layout-sider-trigger {
    justify-content: flex-start;
    padding-left: 20px;
    border: none;
  }
  .devmind-icon {
    font-size: 16px;
    margin-right: 16px;
  }
  .arco-menu-indent {
    width: 0;
  }
  .arco-menu-item:hover,
  .arco-menu-selected {
    background-color: #ecf2ff !important;
    color: rgb(var(--primary-6));
  }
`;
const MenuFoldWrapper = styled.div`
  padding: 0 2px;
  border-radius: 2px;
  :hover {
    background-color: var(--measure-brand1);
    .arco-icon {
      color: var(--measure-brand6);
    }
  }
`;

let SCLogo = styled.img<{ collapsed: boolean }>`
  margin-right: ${({ collapsed }) => (collapsed ? 0 : '30px')};
  width: ${({ collapsed }) => (collapsed ? '28px' : '130px')};
  height: 24px;
`;

/* IFTRUE_openEnv */

SCLogo = styled.img<{ collapsed: boolean }>`
  margin-right: ${({ collapsed }) => (collapsed ? 0 : '0px')};
  width: ${({ collapsed }) => (collapsed ? '28px' : '150px')};
  height: 24px;
`;

/* FITRUE_openEnv */

const SCContent = styled(Content)`
  height: 0;
  overflow-y: auto;
`;

const BaseLayout: React.FC = props => {
  const [collapsed, setCollapsed] = useState<boolean>(false);
  const history = useHistory();
  const { pathname } = useLocation();

  const [
    {
      deptTreeLoading,
      reportTreeLoading,
      deptTreeData,
      reportTreeData,
      newRoutes,
      virtualTreeMaps,
    },
    { getAuthData },
  ] = useModel([
    globalModel,
    ({ departmentTreeInfo, reportTreeInfo, newRoutes, virtualTreeMaps }) => ({
      deptTreeLoading: departmentTreeInfo.pending,
      reportTreeLoading: reportTreeInfo.pending,
      deptTreeData: departmentTreeInfo.data,
      reportTreeData: reportTreeInfo.data,
      newRoutes,
      virtualTreeMaps,
    }),
  ]);

  const { loading: authDataLoading = true, data: authData } = useRequest(
    async () => (await getAuthData()).value,
  );
  const isDevMindAdmin = authData
    ? authData.isAdmin || authData?.isDataManager
    : false;
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  useEffect(() => {
    if (!newRoutes || newRoutes?.length <= 0) {
      setSelectedKeys([]);
      return;
    }
    let keys = getMenuKeys(pathname, newRoutes || []);
    setSelectedKeys(keys || []);
  }, [pathname, newRoutes, isDevMindAdmin]);

  const handleMenuClick = (path: string) => {
    if (path.startsWith(DevmindUrls.VirtualSpace)) {
      // 这里由menuItem 控制
      return;
    }
    const { children, name } = getRouteConfigByPath(path, newRoutes || []);
    XLog({
      id: 'webclick',
      page_id: LogSceneMap.global.value,
      page_title: LogSceneMap.global.type,
      btn_title: {
        event: LogEventMap.global.menuClick,
        name,
        path,
      },
    });
    if (children?.every(({ showMenu = true }) => !showMenu)) {
      pathname !== children[0].path && history.push(children[0].path);
      return;
    }
    pathname !== path && history.push(path);
  };

  const renderMenu = (routes: RouteConfig[]) => {
    return routes.map(item => {
      const {
        icon,
        name,
        children,
        path,
        disabled = false,
        showMenu = true,
        group = false,
        icon_on,
        pName,
      } = item;

      if (path === DevmindUrls.DataView && !isDevMindAdmin) {
        return null;
      }

      if (!showMenu) {
        return null;
      }
      if (group && children) {
        return (
          <ItemGroup key={path} title={name}>
            {renderMenu(children)}
          </ItemGroup>
        );
      }
      const menuTitle = (
        <>
          {pName === '洞察'
            ? selectedKeys.includes(path)
              ? icon_on
              : icon
            : icon}
          {name}
        </>
      );
      if (!children || children.every(({ showMenu = true }) => !showMenu)) {
        return (
          <MenuItem
            onClick={() => {
              // 点击了当前菜单就不能再点击了
              if (selectedKeys.includes(path)) {
                return;
              }
              if (item.path.startsWith(DevmindUrls.VirtualSpace)) {
                XLog({
                  id: 'webclick',
                  page_id: LogSceneMap.global.value,
                  page_title: LogSceneMap.global.type,
                  btn_title: {
                    event: LogEventMap.global.menuClick,
                    name,
                    path,
                  },
                });
                history.push(
                  compile(BaseVirtualSpaceUrls.VirtualSpaceReportList, {
                    encode: encodeURIComponent,
                  })({ spaceType: item.spaceType }),
                );
              }
            }}
            key={path}
            disabled={disabled}
            className={`${icon ? '' : '!pl-12'}`}>
            {menuTitle}
          </MenuItem>
        );
      }
      return (
        <SubMenu key={path} title={menuTitle}>
          {renderMenu(children)}
        </SubMenu>
      );
    });
  };

  const IconMenuFoldComp = ({ isFold }) => {
    return (
      <MenuFoldWrapper>
        <Tooltip content={`${isFold ? '展开导航' : '收起导航'}`}>
          {isFold ? <IconMenuUnfold /> : <IconMenuFold />}
        </Tooltip>
      </MenuFoldWrapper>
    );
  };

  if (authDataLoading) {
    return null;
  }

  return (
    <SCLayout style={{ height: 'calc(100% - 48px' }}>
      {!isSubMenu() && (
        <Sider
          collapsed={collapsed}
          onCollapse={setCollapsed}
          collapsible={true}
          trigger={<IconMenuFoldComp isFold={collapsed} />}
          breakpoint="xl"
          width={200}>
          <Menu
            style={{ marginTop: '15px' }}
            defaultOpenKeys={selectedKeys}
            autoOpen
            selectedKeys={selectedKeys}
            onClickMenuItem={handleMenuClick}>
            {renderMenu(newRoutes || [])}
          </Menu>
          {/* IFTRUE_prodEnv */}
          <Divider className="mt-0" />
          <a
            // eslint-disable-next-line max-len
            className="flex justify-between items-center px-5 text-sm font-medium cursor-pointer text-text4 hover:text-brand5"
            href="https://measure.bytedance.net"
            target="_blank">
            <div className="flex gap-4 items-center">
              <iconpark-icon name="measure-logo" />
              Measure
            </div>
            <iconpark-icon name="arrow-right-up" class="text-xs" />
          </a>
          {/* FITRUE_prodEnv */}
        </Sider>
      )}
      <Layout className="scroll-stable">
        <Header />
        <Layout className="h-0">
          <SCContent>
            <ErrorBoundary FallbackComponent={ErrorFallback}>
              {props.children}
            </ErrorBoundary>
          </SCContent>
        </Layout>
      </Layout>
    </SCLayout>
  );
};

// 根据path获取菜单Keys
const getMenuKeys = (currentPath: string, routes: RouteConfig[]) => {
  if (routes?.length <= 0) {
    return;
  }
  const flatTreeList = treeFlat(routes, 'children');

  return (
    flatTreeList.filter(({ path }) => {
      if (currentPath.startsWith(DevmindUrls.VirtualSpace)) {
        return new RegExp(`^${`${path}/`}\/*`).test(currentPath);
      }

      return new RegExp(`^${path}\/*`).test(currentPath);
    }) ?? []
  ).map(({ path }) => path);
};

export default BaseLayout;
