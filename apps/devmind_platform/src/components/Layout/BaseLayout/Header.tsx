import { FC, useMemo } from 'react';

import { Layout } from '@arco-design/web-react';
import { IconLeft } from '@arco-design/web-react/icon';
import { match } from 'path-to-regexp';
import { useLocation, useHistory } from 'react-router-dom';

import { DevmindUrls } from '@/routers/path';

import ManageArea from './ManageArea';
import VirtualArea from './VirtualArea';

const { Header } = Layout;

const DevmindHeader: FC = () => {
  const { pathname } = useLocation();
  const history = useHistory();

  // 判断是不是需要头部的专家空间区域
  const isNeedSpaceArea = () => {
    if (
      [
        DevmindUrls.DataModelEdit,
        DevmindUrls.DataModelCreate,
        DevmindUrls.MetricStoryFormCreate,
        DevmindUrls.MetricStoryFormEdit,
        DevmindUrls.MetricStoryMap,
      ].some(url => match(url)(pathname))
    ) {
      return false;
    }
    return true;
  };

  const renderArea = useMemo(() => {
    if (pathname.startsWith(DevmindUrls.Workbench)) {
      if (pathname.startsWith(DevmindUrls.WorkbenchVisited)) {
        return (
          <div className="flex gap-4 items-center text-xl font-bold font-large">
            <IconLeft
              className="text-base cursor-pointer"
              onClick={history.goBack}
            />
            <span>最近的访问</span>
          </div>
        );
      }
      return <div className="text-xl font-bold font-large">我的工作台</div>;
    }
    if (
      pathname.startsWith(DevmindUrls.DataManage) ||
      pathname.startsWith(DevmindUrls.MetricStory)
    ) {
      if (!isNeedSpaceArea()) {
        return false;
      }

      return <ManageArea />;
    }

    return <VirtualArea />;
  }, [pathname]);

  return (
    <>
      {renderArea && (
        <Header className="flex justify-between items-center h-20 border-b border-solid border-line2 px-9">
          {renderArea}
        </Header>
      )}
    </>
  );
};

export default DevmindHeader;
