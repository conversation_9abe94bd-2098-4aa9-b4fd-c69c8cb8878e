$min-viewport-width: 1280px; /** 最小宽度 **/

$onesite-navbar-height: 48px; /* 主顶导航高度 */

$menu-animation-duration: 0.2s;

$mini-sider-width: 68px;


  .siderTrigger {
    position: fixed;
    top: 68px;
    z-index: 901;
    display: block;
    width: 20px;
    height: 20px;
    color: var(--color-border-3);
    background-color: #fff;
    border-radius: 10px;
    transform: translateX(-50%);
    cursor: pointer;
    transition: all $menu-animation-duration ease;
    user-select: none;
    &:hover {
      color: rgb(var(--primary-5));
    }
    > svg {
      transform-origin: center center;
      transition: all $menu-animation-duration ease;
    }
  }
