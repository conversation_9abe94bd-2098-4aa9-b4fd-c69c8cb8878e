/* eslint-disable max-len */
import React from 'react';

export default function LeftCircle(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_345_44400)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.99967 0.833252C15.0623 0.833252 19.1663 4.93731 19.1663 9.99992C19.1663 15.0625 15.0623 19.1666 9.99967 19.1666C4.93706 19.1666 0.833008 15.0625 0.833008 9.99992C0.833008 4.93731 4.93706 0.833252 9.99967 0.833252Z"
          fill="transparent"
          stroke="currentColor"
          strokeWidth="0.909091"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.9558 6.37204L11.545 6.96129C11.7077 7.12401 11.7077 7.38783 11.545 7.55055L9.09557 10L11.545 12.4494C11.7077 12.6122 11.7077 12.876 11.545 13.0387L10.9558 13.628C10.793 13.7907 10.5292 13.7907 10.3665 13.628L7.03317 10.2946C6.87045 10.1319 6.87045 9.86809 7.03317 9.70537L10.3665 6.37204C10.5292 6.20932 10.793 6.20932 10.9558 6.37204Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_345_44400">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
