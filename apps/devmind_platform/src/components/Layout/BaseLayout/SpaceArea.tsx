import { FC } from 'react';

import SpaceSelector from '@/components/SpaceSelector';
import { useSpaceUrlQuery } from '@/hooks/url';
import useSpaceNodeInit from '@/hooks/useSpaceNodeInit';

const SpaceArea: FC = () => {
  const { urlQuery, setUrlQuery } = useSpaceUrlQuery();

  useSpaceNodeInit();

  return (
    <SpaceSelector
      nodeId={urlQuery.nodeId}
      onChange={v => {
        setUrlQuery({ nodeId: v });
      }}
    />
  );
};

export default SpaceArea;
