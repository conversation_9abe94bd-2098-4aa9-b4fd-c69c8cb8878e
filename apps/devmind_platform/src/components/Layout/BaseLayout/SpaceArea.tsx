import { FC, useState } from 'react';

import SpaceSelector from '@/components/SpaceSelector';
import { useSpaceUrlQuery } from '@/hooks/url';
import useSpaceNodeInit from '@/hooks/useSpaceNodeInit';
import { SpaceType } from '@/interface';

const SpaceArea: FC = () => {
  const { urlQuery, setUrlQuery } = useSpaceUrlQuery();
  const [selectedMenu, setSelectedMenu] = useState<string>(SpaceType.Expert);

  useSpaceNodeInit();

  return (
    <SpaceSelector
      nodeId={urlQuery.nodeId}
      onChange={v => {
        setUrlQuery({ nodeId: v });
      }}
    />
  );
};

export default SpaceArea;
