import { FC, useState } from 'react';

import { Modal, Form, Alert, Radio } from '@arco-design/web-react';

import { PublishReportRes } from '@quality/common-apis/src/apis/api/insight_report/report';

const FormItem = Form.Item;

interface PublishFormValues {
  seal_widetable_flag: boolean;
  seal_plain_flag: boolean;
  push_flag: boolean;
}

interface PublishModalProps {
  visible: boolean;
  freezeVersion?: string;
  onCancel?: () => void;
  onOk?: (
    params: PublishFormValues,
    // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
  ) => Promise<void | PublishReportRes | undefined> | undefined;
}

const ReportPublishModal: FC<PublishModalProps> = ({
  visible,
  freezeVersion,
  onCancel = () => null,
  onOk = () => undefined,
}: PublishModalProps) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [form] = Form.useForm();

  function handleOk() {
    form.validate().then(data => {
      setConfirmLoading(true);
      onOk(data)?.finally(() => {
        setConfirmLoading(false);
      });
    });
  }

  return (
    <Modal
      title="确认保存并发布"
      visible={visible}
      onOk={handleOk}
      className="modal-demo-without-content-spacing"
      wrapStyle={{ padding: 0 }}
      style={{ padding: 0 }}
      confirmLoading={confirmLoading}
      onCancel={onCancel}>
      <Alert
        banner={true}
        type="info"
        showIcon={true}
        content="报告一天只能发布一次，请谨慎发布。"
      />
      <Form form={form} layout="vertical" style={{ padding: '40px 60px' }}>
        <FormItem
          label="推送报告给订阅者"
          field="push_flag"
          initialValue={1}
          rules={[{ required: true }]}>
          <Radio.Group>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </FormItem>
        <Form.Item shouldUpdate={true} noStyle={true}>
          {values => {
            return (
              <FormItem
                label="对当前报告进行封板"
                required={true}
                initialValue={freezeVersion ? 0 : 1}
                disabled={Boolean(freezeVersion)}
                field="seal_plain_flag"
                extra={
                  values.seal_plain_flag
                    ? '报告将会在封板成功后推送给订阅者'
                    : '如不封板发布，订阅者后续访问到的数据可能会与现在不一致'
                }
                rules={[{ required: true }]}>
                <Radio.Group>
                  <Radio value={1}>是</Radio>
                  <Radio value={0}>否</Radio>
                </Radio.Group>
              </FormItem>
            );
          }}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ReportPublishModal;
