import React, { useEffect, useRef, useState } from 'react';

// import '@lark/open-widgets/es/index.sdk';
// import '@lark/open-widgets';
// import '@lark/open-widgets/dist/main.css';

import { Drawer } from '@arco-design/web-react';
import { useModel } from '@jupiter/plugin-runtime/model';
import styled from '@jupiter/plugin-runtime/styled';
import { useLocation } from 'react-router-dom';

import { commentServiceClient } from '@quality/common-apis/src/apis/api/insight_report/comment';

import { METRIC_AREA_META_FIELD_NAME } from '@/constants';
import { useReportConfigUrlQuery } from '@/hooks/url';
import globalModel from '@/model/global';
import { reportModel } from '@/model/report';
import { MetricInfo } from '@/model/report/types';
import {
  authCallBackUrl,
  LARKWIDGETS_AUTH_URL,
} from '@/pages/biz-space/auth/LarkAuthRedirect';
import { API_V1 } from '@/utils/constant';

import { ReactComponent as IconCloseComment } from './double_chevron_right.svg';

const { CommentViewPlugin } = window.webComponent.OpComment;
export class ExtraReplyPlugin extends CommentViewPlugin {
  pluginName: string = 'ExtraPlugin';

  constructor(
    template_id,
    report_name,
    report_url,
    metric_name,
    component_type,
  ) {
    super();
    this.template_id = template_id;
    this.report_name = report_name;
    this.report_url = report_url;
    this.metric_name = metric_name;
    this.component_type = component_type;
  }

  replyWillAdd({ addReplyParams }: any) {
    addReplyParams.extra = {
      template_id: this.template_id,
      report_name: this.report_name,
      report_url: this.report_url,
      metric_name: this.metric_name,
      component_type: this.component_type,
      originUrl: window.location.href,
    };
  }
}

const CommentViewStyle = styled.div``;

const DrawerStyle = styled(Drawer)`
  .arco-drawer-content {
    padding: 0px 1px;
  }
  .arco-drawer-header-title {
    width: 100%;
  }
`;

const editorContentToCommentObjTree = (value: string, pValue: string) => {
  let finalArray = [
    { id: 'reporttitle' },
    { id: 'reporttasksummary' },
    { id: 'reportsummary' },
  ] as any;
  try {
    const contentObj = JSON.parse(value ?? '[]');
    const pContentObj = JSON.parse(pValue ?? '[]');

    pContentObj?.map(obj => {
      if (obj.metrics) {
        finalArray = finalArray.concat(
          (obj[METRIC_AREA_META_FIELD_NAME] as MetricInfo[]).map(o => {
            return { id: o.metric_id };
          }),
        );
      } else {
        finalArray.push({ id: obj.id });
      }
      return obj;
    });
    contentObj?.map(obj => {
      if (obj.metrics) {
        finalArray = finalArray.concat(
          (obj[METRIC_AREA_META_FIELD_NAME] as MetricInfo[]).map(o => {
            return { id: o.metric_id };
          }),
        );
      } else {
        finalArray.push({ id: obj.id });
      }
      return obj;
    });
    return finalArray;
  } catch (e) {
    return finalArray;
  }
};

const commentDevmindAppID = 'cli_a08fe8c7ac79d00d';

export const CommentView: React.FC = () => {
  const selfRef = useRef<any>(null);
  const { urlQuery } = useReportConfigUrlQuery() as any;
  const [mode, setMode] = useState<any>(urlQuery?.mode);
  const [comments, setComments] = useState<any>();
  const [buttonData, setButtonData] = useState<any>();

  const [, setAuthData] = useState<any>();
  const [, setPermissionData] = useState<any>();

  const [authcode, setAuthcode] = useState<any>();

  const [
    { commentMeta, viewMode, reportDetail, editorContent, parentEditorContent },
    { setCommentMeta },
  ] = useModel([
    reportModel,
    state => ({
      viewMode: state.viewMode,

      commentMeta: state.commentMeta,
      reportDetail: state.reportDetail,
      editorContent: state.$editorContent,
      parentEditorContent: state.$parentEditorContent,
    }),
  ]);
  const [{ userInfo }] = useModel(globalModel);

  let myComponent = useRef<any>(null);
  let shadowComponent = useRef<any>(null);
  const location = useLocation();
  const [addCommentTriggered, setAddCommentTriggered] =
    useState<boolean>(false);
  const [activeCommentTriggered, setActiveCommentTriggered] = useState<any>();
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const sameUser =
    reportDetail?.template_info?.user_info?.username === userInfo?.username;
  useEffect(() => {
    window.addEventListener(
      'message',
      e => {
        const event = e?.data?.event;
        if (event) {
          if (event === 'getAppData') {
            setAuthcode(e.data.value);
          } else if (event === 'commentButtonClicked') {
            setButtonData(e?.data?.value);
          }
        }
      },
      false,
    );
  }, []);

  /* 评论内容跟着自身报告内容走（待确认：继承的报告内容不允许被评论？） */
  useEffect(() => {
    if (editorContent || parentEditorContent) {
      setCommentMeta({
        commentObjTree: editorContentToCommentObjTree(
          editorContent!,
          parentEditorContent!,
        ),
      });
    }
  }, [editorContent]);

  useEffect(() => {
    /* IFTRUE_prodEnv */
    if (commentMeta?.entity_token && commentMeta.commentObjTree) {
      getSignature(true);
    }
    /* FITRUE_prodEnv */
  }, [commentMeta?.entity_token, commentMeta?.commentObjTree]);
  const openAuthWindow = () => {
    const height = 830;
    const width = 710;
    const iTop = (window.screen.availHeight - height) / 2;
    const iLeft = (window.screen.availWidth - width) / 2;
    window.open(
      `${LARKWIDGETS_AUTH_URL}${commentDevmindAppID}&redirect_uri=${authCallBackUrl}`,
      'popUpWindow',
      // eslint-disable-next-line max-len
      `height=${height},width=${width},top=${iTop},left=${iLeft},status=no,toolbar=no,menubar=no,location=no,resizable=no,scrollbar=no,titlebar=no`,
    );
  };
  const getSignature = async (init = false) => {
    const res = await commentServiceClient.GetCommentSignature(
      {
        url: locationParamFree,
        version: API_V1,
        code: authcode ?? '',
      },
      { hideNotice: true },
    );

    if (res.data == null) {
      openAuthWindow();
    } else {
      if (!init) {
        setDrawerVisible(true);
      }
      setAuthData(res.data);
      const { open_id: openId, noncestr: nonceStr } = res.data;

      await window.webComponent.config({
        openId,
        nonceStr,
        ...res.data,
        appId: commentDevmindAppID, // 应用 appId
        url: locationParamFree,
        jsApiList: ['comment'], // 指定要使用的组件，如单个评论组件为 ['comment']
        locale: 'zh-CN', // 指定组件的国际化语言：en-US-英文、zh-CN-中文、ja-JP-日文
      });

      renderComment(init ? 'shadow' : 'comment');
    }
    if (authcode) {
      setAuthcode(undefined);
    }
  };

  const renderComment = async (elId = 'comment') => {
    const res = await commentServiceClient.SetCommentPermission({
      version: API_V1,
      entity_token: commentMeta.entity_token,
    });
    setPermissionData(res);
    if (!drawerVisible && elId === 'comment') {
      setDrawerVisible(true);
    }

    if (
      myComponent.current &&
      elId === 'comment' &&
      (buttonData || urlQuery?.commentId)
    ) {
      setAddCommentTriggered(true);
    } else if (!myComponent.current) {
      setTimeout(async () => {
        const manager = await window.webComponent.render(
          'comment',
          {
            // 组件参数
            token: commentMeta.entity_token, //
            permission: { delete: sameUser, resolve: true },

            commentViewProps: {
              modules: {
                mention: {
                  enabled: true,
                  mentionBox: {
                    zIndex: 1002, // at人浮窗的层级，避免比arco-drawer低
                  },
                },
                position: {
                  minOffsetTop: 20,
                },
              },
            },
            registerViewPlugins: () => [
              new ExtraReplyPlugin(
                reportDetail?.template_info?.template_id ?? '',
                reportDetail?.template_info?.display_name ?? '',
                window.location.href ?? '',
                buttonData?.quote ?? '',
                buttonData?.component_type,
              ),
            ],
          },
          document.querySelector(`#${elId}`), // 将组件挂在到哪个元素上
        );
        manager.onCommentChange(res => {
          setComments(res);
          const newList = reorderComments(res) as any;
          if (elId === 'comment' && (buttonData || urlQuery?.commentId)) {
            setAddCommentTriggered(true);
          } else if (
            elId === 'shadow' &&
            res?.length > 0 &&
            newList?.length > 0
          ) {
            renderComment('comment');
          }
        });
        if (elId === 'comment') {
          manager.onActiveCommentChange(res => {
            setActiveCommentTriggered(res.activeCommentId);
          });
          manager.onResolveComment(() => {
            setActiveCommentTriggered(undefined);
          });
        }
        if (elId === 'comment') {
          myComponent.current = manager;
        } else {
          shadowComponent.current = manager;
        }
      }, 1);
    }
  };
  useEffect(() => {
    if (buttonData) {
      if (window.webComponent.auth.hasAuth()) {
        if (myComponent.current) {
          setAddCommentTriggered(true);
        } else {
          renderComment();
        }
      } else {
        getSignature();
      }
    } else if (authcode) {
      getSignature(true);
    }
  }, [authcode, buttonData]);

  const reorderComments = res => {
    if (!res) {
      return;
    }
    let newList = res;

    // const reportNodeTree = commentMeta?.commentObjTree;
    // if (reportNodeTree) {
    //   reportNodeTree.map(node => {
    //     const matchedComment = res?.filter(r => {
    //       return r.position === node.id;
    //     });
    //     if (matchedComment?.length > 0) {
    //       newList = newList.concat(matchedComment);
    //     }

    //     return node;
    //   });
    //   setComments(newList);

    //   setCommentMeta({ comments: newList });
    // }

    if (viewMode === 'default') {
      newList = res.filter(r => !r.position.includes('wide_table'));
    }

    // setComments(newList);
    setCommentMeta({ comments: newList });

    myComponent?.current?.setCommentMetadata(newList);
    return newList;
  };

  const addComment = () => {
    if (!drawerVisible) {
      setDrawerVisible(true);
    }
    if (comments && myComponent.current) {
      const matchedComment = comments?.find(c => c.position === buttonData?.id);
      const matchedCommentQuery = comments?.find(
        c => c.commentId === urlQuery?.commentId,
      );

      if (!buttonData && matchedCommentQuery) {
        myComponent.current.activateComment({
          commentId: matchedCommentQuery?.commentId,
          forceScroll: true,
        });
      } else if (matchedComment && buttonData) {
        setTimeout(() => {
          myComponent.current.activateComment({
            commentId: matchedComment?.commentId,
            forceScroll: true,
          });
        }, 100);
      } else if (
        addCommentTriggered &&
        buttonData &&
        buttonData.quote !== '0'
      ) {
        const reportNodeTree = commentMeta?.commentObjTree;
        if (reportNodeTree) {
          const index = reportNodeTree.findIndex(e => e.id === buttonData?.id);
          let insertIndex = -1;

          comments.map((c, cIndex) => {
            const cmIndex = reportNodeTree.findIndex(e =>
              c.position.includes(e.id),
            );
            if (cmIndex < index) {
              insertIndex = cIndex;
            }
            return c;
          });

          setTimeout(() => {
            myComponent.current.addComment({
              quote: buttonData?.quote,
              position: buttonData?.id,
              top: 0,
              insertIndex: insertIndex + 1,
            });
            setActiveCommentTriggered(undefined);
          }, 100);
        }
      }
    }
  };

  const locationParamFree =
    window.location.protocol + window.location.host + location.pathname;

  useEffect(() => {
    if (addCommentTriggered && (buttonData || urlQuery?.commentId)) {
      addComment();
      setAddCommentTriggered(false);
    }
  }, [comments, myComponent, addCommentTriggered]);

  useEffect(() => {
    // if (activeCommentTriggered) {
    const comment = comments?.find(c => c.commentId === activeCommentTriggered);
    if (comment) {
      window.postMessage(
        {
          event: 'activateComment',
          data: { position: comment?.position },
        },
        '*',
      );
    } else {
      window.postMessage(
        {
          event: 'activateComment',
          data: { position: '' },
        },
        '*',
      );
    }
  }, [comments, myComponent, activeCommentTriggered]);

  useEffect(() => {
    reorderComments(comments);
    if (urlQuery.mode === 'read') {
      reorderComments(comments);
    }
    setMode(urlQuery.mode);
  }, [urlQuery.mode, viewMode]);

  if (!commentMeta?.entity_token) {
    return null;
  }
  if (urlQuery?._metricId && drawerVisible) {
    setDrawerVisible(false);
  }

  return (
    <CommentViewStyle ref={selfRef} className="commentButtonContainer">
      <div id="shadow" style={{ display: 'none', height: 1 }}></div>
      <DrawerStyle
        mask={false}
        style={{
          width: '302px',
          borderLeft: '1px solid rgba(0, 0, 0, 0.1)',
          boxShadow: '0px 4px 16px rgba(0, 0, 0, 0.1)',
        }}
        visible={drawerVisible}
        title={
          <div className="flex w-full items-center justify-between">
            <div>{`评论（${comments?.length ?? ''}）`}</div>
            <IconCloseComment
              className="cursor-pointer"
              onClick={() => {
                setButtonData(undefined);
                setDrawerVisible(false);
              }}
            />
          </div>
        }
        closable={false}
        footer={null}
        onCancel={() => {
          setButtonData(undefined);
          setDrawerVisible(false);
        }}>
        <div
          className="overflow-y-auto py-3 flex-1 overflow-x-hidden h-full"
          id="comment"></div>
      </DrawerStyle>
    </CommentViewStyle>
  );
};
