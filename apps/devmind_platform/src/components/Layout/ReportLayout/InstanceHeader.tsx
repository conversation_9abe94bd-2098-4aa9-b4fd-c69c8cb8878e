import { FC, useMemo, useRef, useState, useEffect } from 'react';

import {
  Layout,
  Message,
  Button,
  Divider,
  Modal,
  Radio,
  Dropdown,
  Menu,
  Popover,
  Space,
} from '@arco-design/web-react';
import { IconMore } from '@arco-design/web-react/icon';
import { IconChart, IconDataTable } from '@arco-iconbox/react-bits';
import { useKeepPage, useRequest, useBoolean } from '@byted/hooks';
import { useModel } from '@jupiter/plugin-runtime/model';
import { useHistory, useLocation } from '@jupiter/plugin-runtime/router';
import styled from '@jupiter/plugin-runtime/styled';
import dayjs from 'dayjs';
import { match, compile } from 'path-to-regexp';

import {
  PublishReportReq,
  reportTemplateServiceClient,
} from '@quality/common-apis/src/apis/api/insight_report/report';
import { ReportTemplate } from '@quality/common-apis/src/apis/api/insight_report/report_model';
import { SealStatus } from '@quality/common-apis/src/apis/api/insight_report/sealing_version';
import { cancelAllRequest } from '@quality/common-utils/src/request';

import { useFreezeVersion } from '@/components/FreezeList/utils';
import ShrAndSub from '@/components/PlateEditor/ui/title/ShrAndSub';
import {
  isSameDetail,
  getReportValue,
} from '@/components/PlateEditor/utils/compare';
import { useSaveReport } from '@/components/ReportDetail/hooks/useSaveReport';
import {
  ReportConfigModal,
  ConfigMode,
} from '@/components/Space/components/ReportConfigModal';
import {
  TimeCycleText,
  TimeGranularityText,
  format,
} from '@/components/TimeFilter';
import {
  getFormatTimeFilter,
  getReportNameSuffix,
} from '@/components/TimeFilter/utils';
import { API_V1, Authority, EDITOR_MODE } from '@/constants';
import { LogSceneMap, LogEventMap } from '@/constants/log';
import { useReportConfigUrlQuery } from '@/hooks/url';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import globalModel from '@/model/global';
import { reportModel } from '@/model/report';
import { ReportManager } from '@/modules/report';
import { DevmindUrls, isSubMenu, isSubModule } from '@/routers/path';
import { hasAuthority } from '@/utils/auth';
import { downloadPDF } from '@/utils/downloadFile';
import { createUrlQuery, inDashboardUrl, sceneName } from '@/utils/url';
import { isEmbededIframe, XLog } from '@/utils/utils';

import { HeaderLeftArea } from '../DashboardLayout/Header';

import SnapShotModal from './SnapShotModal';

const { Header } = Layout;

const SCHeader = styled(Header)`
  position: relative;
  background: var(--measure-fill1);
  padding: 0px 36px;
  width: 100%;
  height: 72px;
  border-bottom: 1px solid var(--measure-line2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: rgb(0 0 0 / 8%) 0px 2px 6px;
`;
const SCDivider = styled(Divider)`
  margin: 0 16px;
  height: 12px;
`;

// 新建/编辑/复制 报告模板
// eslint-disable-next-line complexity
const InstanceHeader: FC = () => {
  const history = useHistory();
  const location = useLocation();
  const { virtualSpaceKey } = useVirtualSpaceType();

  const {
    urlQuery: {
      nodeId,
      granularity,
      cycle,
      range = [],
      reportId = '',
      from,
      mode = EDITOR_MODE.Read,
      ...rest
    },
    setUrlQuery,
  } = useReportConfigUrlQuery();

  const freezeVersion = useFreezeVersion();

  const timeFilterInfo = {
    granularity,
    cycle,
    range,
  };

  const [{ userInfo, nodeIdRecord, authData }, { setGlobalMetricItem }] =
    useModel([
      globalModel,
      state => ({
        userInfo: state.userInfo,
        nodeIdRecord: state.nodeIdRecord,
        authData: state.authData,
      }),
    ]);
  const [
    {
      saveing,
      viewMode,
      freezeList,
      instanceInfo,
      reportDetail,
      editorRefById,
      originReportDetail,
    },
    { toggleViewMode, setReportDetail, fetchTempAndInstance, setFreezeVersion },
  ] = useModel([
    reportModel,
    state => ({
      saveing: state.saveing,
      viewMode: state.viewMode,
      freezeList: state.freezeList,
      reportDetail: state.reportDetail,
      editorRefById: state.editorRefById,
      originReportDetail: state.originReportDetail,
      instanceInfo: state.$instanceInfo,
    }),
  ]);
  const hasSnapShotting = freezeList?.some(item =>
    [SealStatus.Running, SealStatus.Initial, SealStatus.Suspend].includes(
      item.freeze_status,
    ),
  );

  const { saveReport } = useSaveReport();

  const isDefaultMode = viewMode === 'default';
  // const [publishing, setPublishing] = useState(false);

  const readOnly = mode === EDITOR_MODE.Read;

  const currentSpacePath = DevmindUrls.VirtualSpaceReportDetail;
  const urlMatch = match(currentSpacePath)(location.pathname) as any;
  const templateId = urlMatch?.params?.templateId;

  // 编辑模式 页面刷新提示
  const [_, setShouldKeepPage] = useKeepPage(true);
  useEffect(() => {
    templateId &&
      setShouldKeepPage(
        location.pathname !==
          compile<{ templateId: string; spaceType: string }>(currentSpacePath, {
            validate: false,
          })({
            templateId,
            spaceType: virtualSpaceKey,
          }),
      );
  }, [location.pathname, templateId]);

  const { template_info } = reportDetail;

  const hasAdminAuthority = hasAuthority(
    template_info?.authorization_type as string,
    Authority.admin,
  );

  const hasEditAuthority = hasAuthority(
    template_info?.authorization_type as string,
    Authority.write,
  );

  const hasReadAuthority = hasAuthority(
    template_info?.authorization_type as string,
    Authority.read,
  );

  const changeReadOnly = () => {
    if (mode === EDITOR_MODE.Read) {
      XLog({
        id: 'webclick',
        page_id: LogSceneMap.report.value,
        page_title: LogSceneMap.report.type,
        btn_title: {
          report: `${
            reportDetail?.template_info?.display_name
          }${getReportNameSuffix(
            TimeGranularityText[granularity],
            TimeCycleText[cycle],
            range,
          )}`,
          event: LogEventMap.report.reportStartEdit,
        },
      });
    }
    setUrlQuery({
      mode: mode === EDITOR_MODE.Read ? EDITOR_MODE.Write : EDITOR_MODE.Read,
    });
  };

  const isCurrentSnapShotting =
    freezeVersion?.freeze_status !== undefined &&
    (freezeVersion.freeze_status <= SealStatus.Running ||
      freezeVersion.freeze_status === SealStatus.Suspend);

  const isCurrentFail =
    freezeVersion?.freeze_status !== undefined &&
    freezeVersion.freeze_status === SealStatus.Fail;

  const toggleButton = useMemo(() => {
    return (
      <>
        {readOnly ? (
          hasEditAuthority ? (
            <Popover
              position="left"
              trigger="hover"
              disabled={!isCurrentSnapShotting && !isCurrentFail}
              content={
                isCurrentSnapShotting
                  ? '快照保存中，不允许编辑'
                  : '快照保存失败，不允许编辑'
              }>
              <Button
                disabled={isCurrentSnapShotting || isCurrentFail}
                onClick={changeReadOnly}>
                编辑报告
              </Button>
            </Popover>
          ) : (
            nodeId &&
            Boolean(template_info?.authorization_type) && (
              <Button disabled={true}>暂无编辑权限</Button>
            )
          )
        ) : (
          <Button
            onClick={() => {
              if (
                !hasReadAuthority ||
                !hasEditAuthority ||
                !isDefaultMode ||
                readOnly
              ) {
                changeReadOnly();
                return;
              }

              // 如果和原来的不相同需要有保存提示
              if (
                !isSameDetail(
                  originReportDetail,
                  getReportValue(editorRefById, instanceInfo!),
                )
              ) {
                Modal.confirm({
                  title: '保存提示',
                  content: '切换浏览模式前请先保存编辑内容',
                  onOk: () => {
                    saveReport()?.then(changeReadOnly);
                  },
                });
                return;
              }

              /* 兜底切换 */
              changeReadOnly();
            }}>
            浏览模式
          </Button>
        )}
      </>
    );
  }, [
    readOnly,
    nodeId,
    originReportDetail,
    hasEditAuthority,
    hasReadAuthority,
    changeReadOnly,
  ]);

  const handleSnapShot = () => {
    const [push_flag, seal_plain_flag] = [0, 1];
    return saveReport(false, true)?.then(res => {
      if (res.code !== 200) {
        return;
      }
      const _reportId = res.data?.instance_info?.report_id;
      const params: PublishReportReq = {
        template_id: templateId,
        start_time: range?.[0] || '',
        end_time: range?.[1] || '',
        node_info_id: nodeId || '',
        version: API_V1,
        report_id: res.data?.instance_info?.report_id,
        publish_date: dayjs().format(format),
        publisher: userInfo?.username || '',
        push_flag: Boolean(push_flag),
        seal_plain_flag: Boolean(seal_plain_flag),
        seal_widetable_flag: Boolean(seal_plain_flag),
        space_type: virtualSpaceKey,
      };
      // eslint-disable-next-line promise/no-nesting
      return reportTemplateServiceClient
        .PublishReport(params)
        .then(res => {
          if (res.code === 200 && res.data) {
            Message.success('提交保存快照成功');
            setUrlQuery({
              nodeId,
              granularity,
              cycle,
              range,
              reportId: _reportId,
              from,
              freezeVersion: res?.data,
              mode: 'read',
            });
            setFreezeVersion({
              freeze_key: res?.data,
              freeze_status: 1,
              publish_date: dayjs().format(format),
              publisher: '',
              view_type: '',
              id: '',
            });
            setReportDetail({
              parent_editor_info: undefined,
              editor_info: undefined,
            });
          }
        })
        .catch(_ => {
          Message.error('提交保存快照失败');
        })
        .finally(() => {
          closeSnapShotModal();
        });
    });
  };

  // 删除报告
  const isDeleteAllRef = useRef<boolean>(true); // 是否删除下面子部门继承的报告
  const { run: deleteReport } = useRequest(
    async (templateId: string, business_id: string) => {
      const res = await reportTemplateServiceClient.DeleteTemplateAndInstance({
        version: API_V1,
        template_id: templateId,
        is_delete_all: isDeleteAllRef.current,
        business_id,
      });
      if (Number(res.code) !== 200) {
        return;
      }
      goBack();
      Message.success('删除报告成功');
    },
  );
  const handleDeleteReport = (reportData: ReportTemplate) => {
    if (!hasEditAuthority) {
      Message.error({
        content: <>没有节点编辑者权限</>,
      });
      return;
    }
    if (reportData?.parent_template_name && reportData.parent_id) {
      Modal.confirm({
        title: '删除提示',
        content: (
          <>
            此报告为继承 「{reportData.parent_template_name}」
            报告，删除后，报告在本空间下的相关历史也同步删除，且不再同步此报告，请确认是否要删除？
          </>
        ),
        okText: '删除',
        okButtonProps: { status: 'danger' },
        onOk: () =>
          deleteReport(
            reportData.template_id,
            reportData.business_info.business_id || nodeId,
          ),
        afterClose: () => (isDeleteAllRef.current = false),
      });
      return;
    }
    Modal.confirm({
      title: '删除提示',
      content: (
        <>
          <div>
            删除后，当前{sceneName()}及相关历史也同步删除，请确认是否要删除？
          </div>
          <Radio.Group
            className="mt-1"
            direction="vertical"
            defaultValue={true}
            onChange={v => (isDeleteAllRef.current = v)}>
            <Radio value={true}>
              删除当前及其他空间引用的全部{sceneName()}
            </Radio>
            <Radio value={false}>仅删除当前空间{sceneName()}</Radio>
          </Radio.Group>
        </>
      ),
      okText: '删除',
      okButtonProps: { status: 'danger' },
      onOk: () =>
        deleteReport(
          reportData.template_id || templateId,
          reportData.business_info.business_id || nodeId,
        ),
      afterClose: () => (isDeleteAllRef.current = false),
    });
  };

  const goBack = () => {
    const pathname = (() => {
      switch (from) {
        case 'owner':
          return DevmindUrls.WorkbenchOwnerList;
        case 'subscriber':
          return DevmindUrls.WorkbenchSubcribeList;
        default:
          return DevmindUrls.VirtualSpaceReportList.replace(
            '/:spaceType/',
            `/${virtualSpaceKey}/`,
          );
      }
    })();

    history.push({
      pathname,
      search: createUrlQuery({
        _listGranularity: rest._listGranularity,
        _listCycle: rest._listCycle,
        _listRange: rest._listRange,
        nodeId,
        ...getFormatTimeFilter(timeFilterInfo ?? defaultTimeValue),
      }).str,
    });
  };

  const handleGoBack = () => {
    if (
      !hasReadAuthority ||
      !hasEditAuthority ||
      isSameDetail(
        originReportDetail,
        getReportValue(editorRefById, instanceInfo!),
      ) ||
      readOnly
    ) {
      goBack();
    } else {
      Modal.confirm({
        title: '离开提示',
        content: '系统可能不会保存您所做的更改。',
        onOk: goBack,
      });
    }
  };

  const {
    state: configModalVisible,
    setTrue: openConfigModal,
    setFalse: closeConfigModal,
  } = useBoolean();

  const {
    state: snapShotModalVisible,
    setTrue: openSnapShotModal,
    setFalse: closeSnapShotModal,
  } = useBoolean();
  const [reportConfigInfo, setReportConfigInfo] = useState<{
    mode: ConfigMode;
    templateId: string;
    reportId: string;
  }>({ mode: ConfigMode.Edit, templateId: '', reportId: '' });

  const handleOpenModal = (data: {
    mode: ConfigMode;
    templateId: string;
    reportId: string;
  }) => {
    setReportConfigInfo(data);
    openConfigModal();
  };

  let tooltipContent = '';
  const isDisabled = dayjs() < dayjs(range?.[1]);

  const notManager = !hasEditAuthority && !hasAdminAuthority;

  const filteredFreeze = freezeList.filter(
    item =>
      item.freeze_status === SealStatus.Success ||
      item.freeze_status === SealStatus.Running ||
      item.freeze_status === SealStatus.Initial,
  );

  const isToday =
    filteredFreeze?.length &&
    dayjs() <
      dayjs(filteredFreeze?.[filteredFreeze?.length - 1]?.publish_date).add(
        1,
        'day',
      );

  if (isDisabled) {
    tooltipContent = '报告周期尚未过完，暂不允许发布';
  }

  if (notManager) {
    tooltipContent = `你没有操作权限`;
  }

  if (isToday) {
    tooltipContent = '单日仅可发布一次，今天已经完成发布，请勿重复操作';
  }
  const node_name = nodeIdRecord[nodeId]?.node_name ?? '无组织节点';

  const buttonList: {
    title: string;
    onClick: () => void;
    status?: string;
    disabled?: boolean;
    tooltipContent?: string;
    isHide?: boolean;
  }[] = [
    {
      title: '复制报告',
      onClick: () => {
        setGlobalMetricItem({
          virtualSpaceKey,
        });
        handleOpenModal({
          mode: ConfigMode.Copy,
          templateId,
          reportId,
        });
      },
      disabled: Boolean(freezeVersion),
    },
    {
      title: '报告信息',
      disabled: !hasEditAuthority,
      onClick: () => {
        setGlobalMetricItem({
          virtualSpaceKey,
        });
        handleOpenModal({
          mode: ConfigMode.Edit,
          templateId,
          reportId,
        });
      },
    },
    {
      title: '报告下载',
      onClick: () => {
        // 获取实时时间
        const reportName = `${node_name}-${
          reportDetail?.template_info?.display_name
        }${getReportNameSuffix(
          TimeGranularityText[granularity],
          TimeCycleText[cycle],
          range,
        )}-${dayjs().format('YYYYMMDDHHmmss')}`;
        XLog({
          id: 'webclick',
          page_id: LogSceneMap.report.value,
          page_title: LogSceneMap.report.type,
          btn_title: {
            report: `${
              reportDetail?.template_info?.display_name
            }${getReportNameSuffix(
              TimeGranularityText[granularity],
              TimeCycleText[cycle],
              range,
            )}`,
            event: LogEventMap.report.downloadReport,
          },
        });
        downloadPDF('report-content', 'report', `${reportName}.pdf`);
      },
    },
    {
      title: '保存快照',
      onClick: () => {
        openSnapShotModal();
      },
      tooltipContent,
      disabled:
        (!hasEditAuthority && !hasAdminAuthority) ||
        isToday ||
        isDisabled ||
        Boolean(freezeVersion),
      isHide: !authData?.isAdmin,
    },
    {
      title: '删除',
      status: hasEditAuthority ? 'danger' : '',
      onClick: () => handleDeleteReport(template_info),
      disabled: !hasEditAuthority || hasSnapShotting,
      tooltipContent: hasSnapShotting
        ? '当前存在快照版本保存中，不允许删除'
        : undefined,
      isHide: !template_info,
    },
  ];

  if (isEmbededIframe() && !isSubModule() && !isSubMenu()) {
    return null;
  }

  return (
    <SCHeader>
      {isSubModule() ? <div></div> : <HeaderLeftArea onClick={handleGoBack} />}
      <Radio.Group
        type="button"
        value={viewMode}
        onChange={value => {
          cancelAllRequest();
          if (value === 'table') {
            XLog({
              id: 'webclick',
              page_id: LogSceneMap.report.value,
              page_title: LogSceneMap.report.type,
              btn_title: {
                report: `${
                  reportDetail?.template_info?.display_name
                }${getReportNameSuffix(
                  TimeGranularityText[granularity],
                  TimeCycleText[cycle],
                  range,
                )}`,
                event: LogEventMap.report.reportSwitchWidth,
              },
            });
          }

          toggleViewMode(value);
          ReportManager.getModule()?.updateViewModeLRUCache(
            reportDetail.template_info?.template_id as string,
            value,
          );
        }}>
        <Radio value="default">
          <div className="flex items-center gap-1">
            <IconChart />
            指标卡模式
          </div>
        </Radio>
        <Radio value="table" disabled={Boolean(freezeVersion)}>
          <Popover
            position="bottom"
            trigger="hover"
            disabled={!freezeVersion}
            content={'快照版本不支持宽表模式'}>
            <div className="flex items-center gap-1">
              <IconDataTable />
              宽表模式
            </div>
          </Popover>
        </Radio>
      </Radio.Group>
      <Space>
        {!inDashboardUrl() && (
          <ShrAndSub
            templateId={templateId}
            hasEditAuthority={hasEditAuthority}
          />
        )}
        {toggleButton}

        {!readOnly && isDefaultMode && (
          <>
            <SCDivider type="vertical" />
            <Button
              type="outline"
              onClick={() => saveReport()}
              loading={saveing}>
              保存报告
            </Button>
          </>
        )}

        {!isSubModule() && (
          <Dropdown
            droplist={
              <Menu>
                {buttonList
                  .filter(o => !o.isHide)
                  .map(item => (
                    <Menu.Item
                      disabled={item.disabled}
                      key={item.title}
                      onClick={item.onClick}
                      style={{
                        color:
                          item.status === 'danger'
                            ? item.disabled
                              ? 'var(--color-danger-light-3)'
                              : 'rgb(var(--danger-6))'
                            : '',
                      }}>
                      <Popover
                        position="left"
                        trigger="hover"
                        disabled={!item.tooltipContent}
                        content={item.tooltipContent}>
                        {item.title}
                      </Popover>
                    </Menu.Item>
                  ))}
              </Menu>
            }>
            <Button type="outline" icon={<IconMore />} />
          </Dropdown>
        )}
      </Space>
      <ReportConfigModal
        readOnly={readOnly}
        visible={configModalVisible}
        {...reportConfigInfo}
        onCancel={closeConfigModal}
        nodeId={nodeId}
        timeFilterInfo={timeFilterInfo ?? defaultTimeValue}
        onSuccess={() =>
          fetchTempAndInstance(templateId, freezeVersion?.freeze_key, nodeId)
        }
      />
      <SnapShotModal
        visible={snapShotModalVisible}
        onCancel={closeSnapShotModal}
        onOk={handleSnapShot}
      />
    </SCHeader>
  );
};

export default InstanceHeader;
