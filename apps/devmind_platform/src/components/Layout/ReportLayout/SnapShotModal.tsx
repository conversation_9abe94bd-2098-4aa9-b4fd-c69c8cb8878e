import { FC, useState } from 'react';

import { Modal } from '@arco-design/web-react';

import { PublishReportRes } from '@quality/common-apis/src/apis/api/insight_report/report';

import iconWarning from '@/assets/svg/vt/iconWarning.svg';

interface SnapShotModalProps {
  visible: boolean;
  onCancel?: () => void;
  onOk?: () => Promise<void | PublishReportRes | undefined> | undefined;
}

const SnapShotModal: FC<SnapShotModalProps> = ({
  visible,
  onCancel = () => null,
  onOk = () => undefined,
}: SnapShotModalProps) => {
  const [confirmLoading, setConfirmLoading] = useState(false);

  function handleOk() {
    setConfirmLoading(true);
    onOk()?.finally(() => {
      setConfirmLoading(false);
    });
  }

  return (
    <Modal
      title={
        <div
          style={{ textAlign: 'left', display: 'flex', alignItems: 'center' }}>
          <img
            width={22}
            height={22}
            src={iconWarning}
            style={{ marginRight: 8 }}
          />
          确定要保存快照？
        </div>
      }
      visible={visible}
      onOk={handleOk}
      className="modal-demo-without-content-spacing"
      wrapStyle={{ padding: 0 }}
      style={{ width: 360, padding: 0 }}
      confirmLoading={confirmLoading}
      okText="保存快照"
      onCancel={onCancel}>
      <div style={{ padding: '0 24px 20px' }}>
        <div>·&nbsp;快照只支持指标卡模式，快照版本将只提供按指标卡模式查看</div>
        <div>·&nbsp;保存后不可撤销</div>
      </div>
    </Modal>
  );
};

export default SnapShotModal;
