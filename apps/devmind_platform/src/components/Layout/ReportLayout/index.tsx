import { FC, ReactChildren } from 'react';

import { Layout } from '@arco-design/web-react';

import InstanceHeader from './InstanceHeader';
import './styles.scss';

interface ReportLayoutProps {
  children?: ReactChildren;
}

const ReportLayout: FC<ReportLayoutProps> = (props: ReportLayoutProps) => {
  return (
    <Layout className="reportLayout">
      <InstanceHeader />
      <div id="report-container" className="report-container">
        {props.children}
      </div>
    </Layout>
  );
};

export default ReportLayout;
