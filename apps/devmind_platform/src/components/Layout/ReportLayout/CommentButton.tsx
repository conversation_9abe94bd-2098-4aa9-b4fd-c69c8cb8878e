import React, { useEffect, useRef, useState } from 'react';

// import '@lark/open-widgets/dist/main.css';

import { Button, Tooltip } from '@arco-design/web-react';
import { Editor } from '@editor-kit/core';
import { useModel } from '@jupiter/plugin-runtime/model';
import styled from '@jupiter/plugin-runtime/styled';

import { EDITOR_MODE } from '@/constants/editor';
import useReportConfigUrlQuery from '@/hooks/url/useReportConfigUrlQuery';
import { reportModel } from '@/model/report';
import { isEmbededIframe } from '@/utils/utils';

import { ReactComponent as IconComment } from './comment.svg';
import { ReactComponent as IconCommentBg } from './CommentBg.svg';

const CommentButtonContainer = styled.div<{
  position: {
    top?: number;
    right?: number;
    left?: number;
  };
  visible: boolean;
  absolute: boolean;
}>`
  opacity: ${({ visible }) => `${visible ? 1 : 0}`};
  z-index: 1000;
  position: ${({ absolute }) => (absolute ? 'absolute' : 'relative')};
  /* background-color: ${({ absolute }) =>
    absolute ? 'transparent' : '#f2f3f5'};*/
  border-radius: 2px;
  width: ${({ absolute }) => (absolute ? '26px !important' : '')};
  height: ${({ absolute }) => (absolute ? '26px !important' : '')};
  top: ${({ position, absolute }) => (!absolute ? '0' : `${position.top}px`)};
  left: ${({ position, absolute }) => (!absolute ? '0' : `${position.left}px`)};
  right: ${({ position, absolute }) =>
    !absolute ? '0' : `${position.right}px`};
  :hover {
    background-color: ${({ absolute }) =>
      absolute ? 'transparent' : '#e5e6eb'};
  }
`;

const HeaderButtonWrap = styled(Button)`
  /* margin: 0 8px; */

  background-color: transparent !important;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  :hover {
    background-color: lightgray;
  }
`;
const HeaderTextButtonWrap = styled(Button)`
  /* margin: 0 8px; */
  position: relative;
  background-color: transparent !important;
  border-radius: 2px;
  width: 100% !important;
  height: 100% !important;
  display: flex;
  align-items: center;
  font-size: 12px;

  justify-content: center;
  color: white !important;
  :hover {
    background-color: lightgray;
  }
  svg {
    position: absolute;
    width: 80%;
    margin-left: 2px;
    margin-top: 3px;
    height: 80%;
    z-index: -1;
    color: #fadc6d;
  }
`;

interface CommentButtonProps {
  top?: number;
  right?: number;
  left?: number;
  absolute?: boolean;
  visible?: boolean;
  id: string;
  quote: string;
  hoverable?: boolean;
  getWrapedRef?: (any) => any;
}

export const CommentButton: React.FC<CommentButtonProps> = ({
  top,
  right,
  left,
  id,
  quote,
  absolute = true,
  visible = true,
  hoverable = false,
  getWrapedRef,
}) => {
  const selfRef = useRef<any>(null);

  const {
    urlQuery: { mode },
  } = useReportConfigUrlQuery();

  const [{ commentMeta }] = useModel([
    reportModel,
    state => ({
      commentMeta: state.commentMeta,
      reportDetail: state.reportDetail,
    }),
  ]);
  useEffect(() => {
    if (getWrapedRef) {
      getWrapedRef(selfRef.current);
    }
    const parent = selfRef?.current?.parentElement;
    const originStr = parent?.getAttribute('class');

    if (selfRef.current && parent && absolute) {
      const classStr = `comment-parent ${hoverable ? 'comment-hover' : ''}`;
      if (!originStr?.includes(classStr)) {
        parent.setAttribute('class', `${originStr} ${classStr}`);
      }
    }
  }, [selfRef?.current]);

  const commentData = commentMeta?.comments?.filter(c => {
    return c.position === id;
  });

  if (mode === EDITOR_MODE.Write || commentMeta?.entity_token === undefined) {
    return null;
  }
  if (isEmbededIframe()) {
    return null;
  }
  return visible ? (
    <Tooltip content="评论">
      <CommentButtonContainer
        visible={commentData?.[0]?.replyList?.length > 0 || !absolute}
        position={{
          top,
          left,
          right,
        }}
        absolute={absolute}
        ref={selfRef}
        className="commentButtonContainer">
        {commentData?.[0]?.replyList?.length > 0 ? (
          <HeaderTextButtonWrap
            onClick={e => {
              e.stopPropagation();
              window.postMessage(
                { event: 'commentButtonClicked', value: { id, quote } },
                '*',
              );
            }}
            icon={<IconCommentBg />}>
            {commentData?.[0]?.replyList?.length}
          </HeaderTextButtonWrap>
        ) : (
          <HeaderButtonWrap
            onClick={e => {
              e.stopPropagation();
              window.postMessage(
                {
                  event: 'commentButtonClicked',
                  value: { id, quote },
                },
                '*',
              );
            }}
            icon={<IconComment id="commentIcon" />}
          />
        )}
      </CommentButtonContainer>
    </Tooltip>
  ) : null;
};

export interface CommentProps {
  commentId: string;
  commentQuote: string;
  commentPosition: { top?: number; right?: number; left?: number };
  commentVisible?: boolean;
  isEditor?: boolean;
  hoverable?: boolean;
}

export const withComment = Component => {
  return (props: any | CommentProps) => {
    const {
      commentId,
      commentQuote,
      commentPosition: { top = 24, right = 24, left },
      commentVisible = true,
      hoverable,
      isEditor,
      ...rest
    } = props;
    const [actived, setActived] = useState(false);

    const nodeRef = useRef(null);
    const editRef = useRef(null);

    useEffect(() => {
      function scrollEl(e) {
        const event = e?.data?.event;
        if (event) {
          const position = e.data?.data?.position;

          if (event === 'activateComment' && position === commentId) {
            setActived(true);
            const { current } = nodeRef;

            (current as unknown as HTMLDivElement)?.scrollIntoView({
              behavior: 'smooth',
            });
            // document.getElementById('report-content')?.scrollTo({
            //   left: 0,
            //   top:
            //     (current as unknown as HTMLDivElement)?.offsetTop +
            //     (
            //       (current as unknown as HTMLDivElement)
            //         ?.offsetParent as HTMLDivElement
            //     ).offsetTop -
            //     100,
            //   behavior: 'smooth',
            // });
          } else {
            setActived(false);
          }
        }
      }
      window.addEventListener('message', scrollEl, false);
      () => {
        window.removeEventListener('message', scrollEl);
      };
    }, []);

    return (
      <>
        <Component
          {...rest}
          commentId={commentId}
          getEditorRef={ref => (editRef.current = ref)}
          style={{
            background: actived ? 'rgb(250, 241, 209)' : '',
          }}
        />

        <CommentButton
          hoverable={hoverable}
          getWrapedRef={ref => (nodeRef.current = ref)}
          top={top}
          right={right}
          id={commentId}
          quote={
            String(commentQuote) +
            String(
              (isEditor &&
                (editRef?.current as unknown as Editor)?.getText()) ||
                '',
            )
          }
          visible={commentVisible}
          left={left}
        />
      </>
    );
  };
};

export const CommentBlock = withComment(({ children, ...rest }) => {
  return children ? React.cloneElement(children, rest) : null;
});
