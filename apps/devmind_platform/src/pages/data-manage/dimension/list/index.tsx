import { useEffect, useState } from 'react';

import { Button, Form, Message } from '@arco-design/web-react';
import { useModal } from '@ebay/nice-modal-react';
import { useRequest, useDebounceFn } from 'ahooks';

import { metricDictServiceClient } from '@quality/common-apis/src/apis/api/metric_platform/dimension';
import { TransferStatusOffline } from '@quality/common-apis/src/apis/api/metric_platform/model';

import CollectionView from '@/components/PlatformAdmin/CollectionView';
import { PlatformPage } from '@/components/PlatformAdmin/layout';
import { CaliberSqlRenderer } from '@/components/PlatformAdmin/tableRenderers';
import { handlePaginationCurrentPageNum } from '@/components/PlatformAdmin/util';
import { API_V1 } from '@/constants';
import useManageUrlQuery from '@/hooks/url/useManageUrlQuery';
import { NodeManager } from '@/modules/node';
import {
  // FormItemEfficientMap,
  FormItemModelInfo,
  FormItemOwners,
  FormItemSearch,
  FormItemVisible,
} from '@/pages/data-manage/common/formItems';

import { pageSize } from '../../metric/list/config';

import { DimensionManageDrawer } from './DimensionManageDrawer';

const DimensionHomePage = () => {
  const {
    urlQuery: { nodeId, spaceType: urlSpaceType },
  } = useManageUrlQuery();
  const spaceType =
    urlSpaceType || NodeManager.getModule()?.getSpaceType(nodeId);
  const [filterData, setFilterData] = useState<any>({
    efficient_map: '',
    /** 数据集 */
    model_id: '',
    /** 可见性 */
    visible_space: '',
    /** 搜索内容 */
    search_content: '',
    /** 几点id */
    node_id: '',
    /** 当前所处页 */
    page_num: 1,
    /** 每页的容量 */
    page_count: pageSize,
  });
  const dimensionManageModal = useModal(DimensionManageDrawer);

  const handleCreateDimension = async () => {
    try {
      await dimensionManageModal.show({});
      fetchDimensionList();
    } catch (err) {
      console.log(err);
    }
  };

  const handleEditDimension = async (dimensionId: string) => {
    await dimensionManageModal.show({ dimensionId });
    fetchDimensionList();
  };
  const handleViewDimension = (dimensionId: string) => {
    dimensionManageModal.show({ dimensionId, readonly: true });
  };
  const handleStatusChange = async (
    id: string,
    name: string,
    status: string,
  ) => {
    const res = await metricDictServiceClient.TransferDimensionStatus({
      version: API_V1,
      id,
      transfer_status: status,
    });
    if (res.code === 200) {
      Message.info({
        content: `${name}已${status === TransferStatusOffline ? '下' : '上'}线`,
      });
      fetchDimensionList();
    }
  };

  const {
    loading,
    data: dimensionList,
    run: fetchDimensionList,
  } = useRequest(
    async () => {
      const res = await metricDictServiceClient.ListDimensionListByCondition({
        version: API_V1,
        cond: { ...filterData, node_id: nodeId, space_type: spaceType },
      });
      return res.data ?? [];
    },
    { manual: true },
  );
  useEffect(() => {
    fetchDimensionList();
  }, [filterData, nodeId, spaceType]);

  const handleRefresh = async () => {
    const res = await metricDictServiceClient.ListDimensionListByCondition({
      version: API_V1,
      cond: { ...filterData, node_id: nodeId, space_type: spaceType },
    });
    setFilterData({
      ...filterData,
      page_num: handlePaginationCurrentPageNum(
        pageSize,
        filterData.page_num,
        res.data?.total_count || 0,
      ),
    });
  };

  const { run: debounceValuesChange } = useDebounceFn(
    (_, v) => {
      setFilterData(v);
    },
    {
      wait: 300,
    },
  );

  return (
    <PlatformPage>
      <div className="header">
        <div>维度字典</div>
        <Button type="primary" onClick={handleCreateDimension}>
          新建维度
        </Button>
      </div>

      <div className="filter">
        <Form
          layout="inline"
          initialValues={filterData}
          onValuesChange={debounceValuesChange}>
          {/* <FormItemEfficientMap /> */}
          <FormItemModelInfo />
          <FormItemVisible />
          <FormItemOwners />
          <FormItemSearch />
          {/* <Button className="search-button">搜索</Button> */}
        </Form>
      </div>
      <CollectionView
        domainKV={[
          'display_name',
          'visible_space',
          'model_info.display_name',
          'owners_cn',
          'description',
          'operations',
        ]}
        loading={loading}
        data={dimensionList?.data_list}
        context={{
          edit: handleEditDimension,
          view: handleViewDimension,
          transferStatus: handleStatusChange,
          refresh: handleRefresh,
        }}
        extraTableProps={{
          pagination: {
            total: dimensionList?.total_count,
            pageSize,
            current: filterData.page_num,
          },
          expandedRowRender(record) {
            return CaliberSqlRenderer(record.caliber_sql);
          },
          onChange(pagination) {
            setFilterData({
              ...filterData,
              page_num: pagination.current,
            });
          },
        }}
      />
    </PlatformPage>
  );
};
export default DimensionHomePage;
