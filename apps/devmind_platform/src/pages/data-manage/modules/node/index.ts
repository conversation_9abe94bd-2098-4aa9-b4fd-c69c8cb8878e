import { transformNodeTree } from '@devmind/server/app/utils/index';
import { match } from 'path-to-regexp';

import { ExpertSpaceNodeID } from '@quality/common-apis/src/apis/api/metric_platform/model';
import { ProductInfo } from '@quality/common-apis/src/apis/api/product/product_model';

import { Authority } from '@/constants';
import { SpaceType } from '@/interface';
import { BaseVirtualSpaceUrls, pathPrefix } from '@/routers/path';
import { hasAuthority } from '@/utils/auth';

export { transformNodeTree };

type NodeId = string;

let _nodeModuleInstance: NodeModule | null = null;

class NodeModule {
  private readonly nodeIdRecord: Record<
    NodeId,
    ProductInfo & { spaceType?: string }
  >;

  constructor(
    private readonly expertTree: { value: ProductInfo[]; spaceType: string },
    private readonly initNodes: {
      value: ProductInfo[];
      spaceType: string | undefined;
    },
  ) {
    const { treeIdRecord: expertTreeIdRecord } = transformNodeTree(
      this.expertTree.value ?? [],
      SpaceType.Expert,
    );
    if (this.initNodes.spaceType) {
      const { treeIdRecord: initNodesRecord } = transformNodeTree(
        this.initNodes.value ?? [],
        this.initNodes.spaceType as string,
      );
      this.nodeIdRecord = {
        ...expertTreeIdRecord,
        ...initNodesRecord,
      };
    } else {
      this.nodeIdRecord = {
        ...expertTreeIdRecord,
      };
    }
  }

  // 校验节点权限
  validAuth(nodeId: string, auth: Authority) {
    if (!nodeId || !this.nodeIdRecord[nodeId]) {
      return false;
    }
    const { authorization_type } = this.nodeIdRecord[nodeId];
    return hasAuthority(authorization_type, auth);
  }

  // 获取具体节点信息
  getNodeInfo(nodeId: string) {
    if (nodeId === ExpertSpaceNodeID) {
      return {
        // JD
        node_name: '专家空间',
        node_id: ExpertSpaceNodeID,
      } as ProductInfo & { spaceType?: string };
    }
    if (!nodeId || !this.nodeIdRecord[nodeId]) {
      return {} as ProductInfo & { spaceType?: string };
    }
    return this.nodeIdRecord[nodeId];
  }

  getSpaceType(nodeId?: string) {
    try {
      if (nodeId === ExpertSpaceNodeID) {
        return SpaceType.Expert;
      }

      // 通过 match 方法来匹配对应的 spaceType
      let matchRes;
      for (const key in BaseVirtualSpaceUrls) {
        let item = BaseVirtualSpaceUrls[key];
        let res = match(`${pathPrefix}${item}`)(location.pathname);
        if (res) {
          matchRes = res;
          break;
        }
      }

      // 置为 undefined 为了让 ?? 能够走到后面的操作
      return matchRes ? matchRes?.params?.spaceType : undefined;
    } catch (error) {
      console.log(error, 'error');
    }
  }

  addNodeRecord(nodeInfo: ProductInfo) {
    this.nodeIdRecord[nodeInfo.node_id] = { ...nodeInfo };
  }
}

export const NodeManager: ModuleManager<NodeModule> = {
  create(
    expertTree: { value: ProductInfo[]; spaceType: string },
    initNodes: { value: ProductInfo[]; spaceType: string | undefined },
  ): NodeModule {
    // if (!_nodeModuleInstance) {

    _nodeModuleInstance = new NodeModule(expertTree, initNodes);
    // }
    return _nodeModuleInstance;
  },
  getModule(): NodeModule | null {
    return _nodeModuleInstance;
  },
  destroy() {
    _nodeModuleInstance = null;
  },
};
