import { useEffect, useState } from 'react';

import { Button, Form, Message } from '@arco-design/web-react';
import { useModal } from '@ebay/nice-modal-react';
import { useRequest, useDebounceFn } from 'ahooks';

import { metricDictServiceClient } from '@quality/common-apis/src/apis/api/metric_platform/dimension';
import { TransferStatusOffline } from '@quality/common-apis/src/apis/api/metric_platform/model';

import CollectionView from '@/components/PlatformAdmin/CollectionView';
import { PlatformPage } from '@/components/PlatformAdmin/layout';
import { CaliberSqlRenderer } from '@/components/PlatformAdmin/tableRenderers';
import { handlePaginationCurrentPageNum } from '@/components/PlatformAdmin/util';
import { API_V1 } from '@/constants';
import useManageUrlQuery from '@/hooks/url/useManageUrlQuery';
import { NodeManager } from '@/modules/node';
import {
  // FormItemEfficientMap,
  FormItemModelInfo,
  FormItemOwners,
  FormItemSearch,
  FormItemVisible,
} from '@/pages/data-manage/common/formItems';

import { ViewDomain } from '../../common/kvconfigs';

import { MetricKVConfig, TabsType, pageSize } from './config';
import { MetricManageDrawer } from './MetricManageDrawer';

// const TabPane = Tabs.TabPane;

const MetricHomePage = () => {
  const {
    urlQuery: { nodeId, spaceType: urlSpaceType },
  } = useManageUrlQuery();
  // 优先使用 URL 中的 spaceType，如果没有则从 NodeManager 中获取
  const spaceType =
    urlSpaceType || NodeManager.getModule()?.getSpaceType(nodeId);
  const [activeTab] = useState<any>(TabsType.ModelMetric);
  const [filterData, setFilterData] = useState<any>({
    efficient_map: '',
    /** 数据集 */
    model_id: '',
    /** 可见性 */
    visible_space: '',
    /** 搜索内容 */
    search_content: '',
    /** 几点id */
    node_id: '',
    /** 当前所处页 */
    page_num: 1,
    /** 每页的容量 */
    page_count: pageSize,
  });

  const metricManageModal = useModal(MetricManageDrawer);

  const handleCreateMetric = async () => {
    try {
      await metricManageModal.show({ metricType: TabsType.ModelMetric });
      fetchMetricList();
    } catch (err) {
      console.log(err);
    }
  };

  const handleEditMetric = async (metricId: string) => {
    await metricManageModal.show({ metricType: activeTab, metricId });
    fetchMetricList();
  };
  const handleViewMetric = (metricId: string) => {
    metricManageModal.show({ metricId, metricType: activeTab, readonly: true });
  };
  const handleStatusChange = async (
    metricId: string,
    metricName: string,
    status: string,
  ) => {
    const res = await metricDictServiceClient.TransferMetricStatus({
      version: API_V1,
      id: metricId,
      transfer_status: status,
    });
    if (res.code === 200) {
      Message.info({
        content: `${metricName}已${
          status === TransferStatusOffline ? '下' : '上'
        }线`,
      });
      fetchMetricList();
    }
  };

  const {
    data: metricListData,
    run: fetchMetricList,
    loading,
  } = useRequest(
    async () => {
      // 如果没有nodeId，不调用API
      if (!nodeId) {
        return { data_list: [], total_count: 0 };
      }
      const res = await metricDictServiceClient.ListMetricListByCondition({
        version: API_V1,
        cond: { ...filterData, node_id: nodeId, space_type: spaceType },
      });
      return res.data ?? [];
    },
    { manual: true },
  );
  useEffect(() => {
    // 只有在有nodeId时才调用API
    if (nodeId) {
      fetchMetricList();
    }
  }, [filterData, nodeId, spaceType]);

  const handleRefresh = async () => {
    const res = await metricDictServiceClient.ListMetricListByCondition({
      version: API_V1,
      cond: { ...filterData, node_id: nodeId, space_type: spaceType },
    });
    setFilterData({
      ...filterData,
      page_num: handlePaginationCurrentPageNum(
        pageSize,
        filterData.page_num,
        res.data?.total_count || 0,
      ),
    });
  };

  const { run: debounceValuesChange } = useDebounceFn(
    (_, v) => {
      setFilterData(v);
    },
    {
      wait: 300,
    },
  );

  return (
    <PlatformPage>
      <div className="header">
        <div>指标字典</div>
        <Button type="primary" onClick={handleCreateMetric}>
          新建指标
        </Button>
      </div>
      {/* <Tabs
        activeTab={activeTab}
        onChange={v => {
          setActiveTab(v);
        }}
        className="mb-5">
        <TabPane
          key={TabsType.ModelMetric}
          title={tabsConfig[TabsType.ModelMetric].title}
        />
        <TabPane
          key={TabsType.ComplexMetric}
          title={tabsConfig[TabsType.ComplexMetric].title}
        />
      </Tabs> */}
      <div className="filter">
        <Form
          layout="inline"
          initialValues={filterData}
          onValuesChange={debounceValuesChange}>
          {/* <FormItemEfficientMap /> */}
          {activeTab !== TabsType.ComplexMetric && <FormItemModelInfo />}
          <FormItemVisible />
          <FormItemOwners />
          <FormItemSearch />
          {/* <Button className="search-button">搜索</Button> */}
        </Form>
      </div>
      <CollectionView
        loading={loading}
        domain={ViewDomain.Metric}
        domainKV={MetricKVConfig.map(c => c.keyName)}
        data={metricListData?.data_list}
        context={{
          edit: handleEditMetric,
          view: handleViewMetric,
          transferStatus: handleStatusChange,
          refresh: handleRefresh,
        }}
        extraTableProps={{
          pagination: {
            total: metricListData?.total_count,
            pageSize,
            current: filterData.page_num,
          },
          expandedRowRender(record) {
            return CaliberSqlRenderer(record.caliber_sql);
          },
          onChange(pagination) {
            setFilterData({
              ...filterData,
              page_num: pagination.current,
            });
          },
        }}
      />
    </PlatformPage>
  );
};
export default MetricHomePage;
