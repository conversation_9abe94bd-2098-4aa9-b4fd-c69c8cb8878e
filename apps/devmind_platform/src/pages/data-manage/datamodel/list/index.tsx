import { useEffect, useState } from 'react';

import {
  Button,
  Form,
  Message,
  Spin,
  Notification,
} from '@arco-design/web-react';
import { useRequest, useDebounceFn } from 'ahooks';
import { compile } from 'path-to-regexp';
import { useHistory } from 'react-router-dom';

import { metricDictServiceClient } from '@quality/common-apis/src/apis/api/metric_platform/dimension';
import { TransferStatusOffline } from '@quality/common-apis/src/apis/api/metric_platform/model';

import CollectionView from '@/components/PlatformAdmin/CollectionView';
import { PlatformPage } from '@/components/PlatformAdmin/layout';
import { handlePaginationCurrentPageNum } from '@/components/PlatformAdmin/util';
import { API_V1 } from '@/constants';
import useManageUrlQuery from '@/hooks/url/useManageUrlQuery';
import { NodeManager } from '@/modules/node';
import {
  // FormItemEfficientMap,
  FormItemOwners,
  FormItemSearch,
  FormItemVisible,
} from '@/pages/data-manage/common/formItems';
import { DevmindUrls } from '@/routers/path';

import { ViewDomain } from '../../common/kvconfigs';

import { pageSize, DomainKVConfig } from './config';

const MetricHomePage = () => {
  const {
    urlQuery: { nodeId, spaceType: urlSpaceType },
  } = useManageUrlQuery();
  const spaceType =
    urlSpaceType || NodeManager.getModule()?.getSpaceType(nodeId);
  const [filterData, setFilterData] = useState<any>({
    efficient_map: '',
    /** 数据集 */
    model_id: '',
    /** 可见性 */
    visible_space: '',
    /** 搜索内容 */
    search_content: '',
    /** 几点id */
    node_id: '',
    /** 当前所处页 */
    page_num: 1,
    /** 每页的容量 */
    page_count: pageSize,
    /** 数据模型结构 */
    // data_type: '',
  });

  const history = useHistory();

  // const accessTypeModal = useModal(AccessTypeModal);

  const handleCreateModel = () => {
    // const accessType = (await accessTypeModal.show({})) as string;
    // localStorage.setItem(MANAGE_DATA_MODEL_ACCESS_TYPE, accessType);
    history.push(`${DevmindUrls.DataModelCreate}${history.location.search}`);
  };

  const handleEditModel = (id: string) =>
    history.push(
      compile<{ id: string }>(DevmindUrls.DataModelEdit)({
        id,
      }),
    );

  const handleStatusChange = async (
    id: string,
    name: string,
    status: string,
  ) => {
    const res = await metricDictServiceClient.TransferModelStatus({
      version: API_V1,
      id,
      transfer_status: status,
    });
    if (res.code === 200) {
      Message.info({
        content: `${name}已${status === TransferStatusOffline ? '下' : '上'}线`,
      });
      fetchList();
    }
  };

  const {
    data: listData,
    run: fetchList,
    loading,
  } = useRequest(
    async () => {
      const res = await metricDictServiceClient.GetModelListByCondition({
        version: API_V1,
        cond: { ...filterData, node_id: nodeId, space_type: spaceType },
      });
      return res.data ?? [];
    },
    { manual: true },
  );
  useEffect(() => {
    fetchList();
  }, [filterData, nodeId, spaceType]);

  const handleRefresh = async () => {
    const res = await metricDictServiceClient.GetModelListByCondition({
      version: API_V1,
      cond: { ...filterData, node_id: nodeId, space_type: spaceType },
    });
    setFilterData({
      ...filterData,
      page_num: handlePaginationCurrentPageNum(
        pageSize,
        filterData.page_num,
        res.data?.total_count || 0,
      ),
    });
  };

  const { run: refreshSystemCache, loading: refreshLoading } = useRequest(
    async () => {
      try {
        const res = await metricDictServiceClient.RefreshInfo({
          version: API_V1,
        });
        if (res.code === 200) {
          fetchList();
          Message.success('缓存刷新成功');
        }
      } catch (error) {
        Notification.error({
          title: '缓存刷新失败',
          content: '请联系后台管理员',
        });
      }
    },
    { manual: true },
  );

  const { run: debounceValuesChange } = useDebounceFn(
    (_, v) => {
      setFilterData(v);
    },
    {
      wait: 300,
    },
  );

  if (refreshLoading) {
    return <Spin />;
  }

  return (
    <PlatformPage>
      <div className="header">
        <div>数据模型</div>
        <div>
          <Button type="primary" onClick={handleCreateModel}>
            新建数据模型
          </Button>
        </div>
      </div>
      <div className="filter">
        <Form
          layout="inline"
          initialValues={filterData}
          onValuesChange={debounceValuesChange}>
          {/* <FormItemEfficientMap /> */}
          <FormItemVisible />
          {/* <FormItemDataType /> */}
          <FormItemOwners />
          <FormItemSearch />
          {/* <Button className="search-button">搜索</Button> */}
        </Form>
      </div>
      <CollectionView
        loading={loading}
        domainKV={DomainKVConfig}
        domain={ViewDomain.Model}
        data={listData?.data_list}
        context={{
          edit: handleEditModel,
          // view: handleViewMetric,
          transferStatus: handleStatusChange,
          refresh: handleRefresh,
        }}
        extraTableProps={{
          pagination: {
            total: listData?.total_count,
            pageSize,
            current: filterData.page_num,
          },
          // expandedRowRender(record) {
          //   return CaliberSqlRenderer(record.caliber_sql);
          // },
          onChange(pagination) {
            setFilterData({
              ...filterData,
              page_num: pagination.current,
            });
          },
        }}
      />
    </PlatformPage>
  );
};
export default MetricHomePage;
