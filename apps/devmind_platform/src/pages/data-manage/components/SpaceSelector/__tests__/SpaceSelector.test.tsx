import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from '@reduck/core';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';

import globalModel from '@/model/global';
import { SpaceType } from '@/interface';

import SpaceSelector from '../index';

// Mock dependencies
jest.mock('@/hooks/useSpaceType', () => ({
  useVirtualSpaceType: () => ({
    virtualSpaceKey: 'expert',
    virtualItem: { NameZh: '专家空间', NameEn: 'expert' },
  }),
}));

jest.mock('@/modules/node', () => ({
  NodeManager: {
    getModule: () => ({
      getNodeInfo: jest.fn(),
      addNodeRecord: jest.fn(),
      getSpaceType: jest.fn(() => 'expert'),
    }),
  },
}));

const mockGlobalState = {
  nodeIdRecord: {
    '-1': { node_name: '专家空间', node_id: '-1' },
    'test-node-1': { node_name: '测试节点1', node_id: 'test-node-1' },
  },
  virtualSpaceList: [
    { NameZh: '专家空间', NameEn: 'expert', Virtual: true },
    { NameZh: '部门空间', NameEn: 'department', Virtual: true },
  ],
  authData: {
    isAdmin: true,
    isDataManager: false,
    isDomainExpert: false,
  },
  virtualTreeMaps: {},
};

const TestWrapper = ({ children, authData = mockGlobalState.authData }) => (
  <BrowserRouter>
    <Provider
      models={[globalModel]}
      preloadedState={{
        'devmind-global': {
          ...mockGlobalState,
          authData,
        },
      }}>
      {children}
    </Provider>
  </BrowserRouter>
);

describe('DataManage SpaceSelector', () => {
  const defaultProps = {
    nodeId: '-1',
    allowExpand: true,
    allowChangeSpaceType: true,
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly for admin users', () => {
    render(
      <TestWrapper>
        <SpaceSelector {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('专家空间(数据管理)')).toBeInTheDocument();
  });

  it('should show "请选择节点" for non-admin users without nodeId', () => {
    const nonAdminAuthData = {
      isAdmin: false,
      isDataManager: false,
      isDomainExpert: false,
    };

    render(
      <TestWrapper authData={nonAdminAuthData}>
        <SpaceSelector {...defaultProps} nodeId="" />
      </TestWrapper>
    );

    expect(screen.getByText('请选择节点(数据管理)')).toBeInTheDocument();
  });

  it('should show "专家空间" for admin users without nodeId', () => {
    render(
      <TestWrapper>
        <SpaceSelector {...defaultProps} nodeId="" />
      </TestWrapper>
    );

    expect(screen.getByText('专家空间(数据管理)')).toBeInTheDocument();
  });

  it('should handle space type change correctly', async () => {
    const onChange = jest.fn();
    
    render(
      <TestWrapper>
        <SpaceSelector {...defaultProps} onChange={onChange} />
      </TestWrapper>
    );

    // Click to open dropdown
    fireEvent.click(screen.getByText('专家空间(数据管理)'));

    // Wait for popup to appear
    await waitFor(() => {
      expect(screen.getByText('专家空间')).toBeInTheDocument();
    });

    // Select expert space type
    const expertOption = screen.getByText('专家空间');
    fireEvent.click(expertOption);

    // Should call onChange with expert space
    expect(onChange).toHaveBeenCalledWith('-1', 'expert');
  });

  it('should preserve space type on refresh', () => {
    const manageSpaceType = 'department';
    
    render(
      <TestWrapper>
        <SpaceSelector {...defaultProps} manageSpaceType={manageSpaceType} />
      </TestWrapper>
    );

    // Should use the passed manageSpaceType
    expect(screen.getByText('专家空间(数据管理)')).toBeInTheDocument();
  });

  it('should not immediately request space-tree when dropdown opens', async () => {
    const mockGetVirtualTreeNodes = jest.fn();
    
    render(
      <TestWrapper>
        <SpaceSelector {...defaultProps} />
      </TestWrapper>
    );

    // Click to open dropdown
    fireEvent.click(screen.getByText('专家空间(数据管理)'));

    // Should not immediately call API
    expect(mockGetVirtualTreeNodes).not.toHaveBeenCalled();
  });
});
