import { FC, useRef } from 'react';

import { Avatar, Trigger, Toolt<PERSON> } from '@arco-design/web-react';
import { IconCaretDown, IconCaretUp } from '@arco-design/web-react/icon';
import { useBoolean } from '@byted/hooks';
import { useModel } from '@reduck/core';
import cs from 'classnames';

import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { SpaceType } from '@/interface';
import globalModel from '@/model/global';

import Popup, { Props as PopupProps } from './components/Popup';

interface Props extends Omit<PopupProps, 'onClose' | 'spaceType'> {
  logoSize?: number;
  nameSize?: number;
  allowExpand?: boolean; // 是否支持展开属性选择
  manageSpaceType?: string; // 管理模块独立的空间类型，不影响全局状态
}

const SpaceSelector: FC<Props> = props => {
  const {
    nodeId,
    treeData,
    allowExpand = true,
    allowChangeSpaceType = false,
    logoSize,
    nameSize,
    onChange,
    manageSpaceType,
  } = props;
  const { virtualSpaceKey: globalVirtualSpaceKey } = useVirtualSpaceType();
  const [{ nodeIdRecord, virtualSpaceList }] = useModel([globalModel]);

  // 如果是管理模块且传递了manageSpaceType，使用独立的空间类型，否则使用全局的
  const virtualSpaceKey = manageSpaceType || globalVirtualSpaceKey;

  const { state: visible, toggle, setFalse } = useBoolean(false);
  const nodeRef = useRef(null);

  // 优化后，通过全局的 nodeIdRecord 获取节点名称，并支持虚拟组织类型显示
  const getDisplayName = () => {
    const baseNodeName =
      nodeIdRecord[nodeId]?.node_name ??
      (nodeId === '-1' ? '专家空间' : '无组织节点');

    // 如果允许切换空间类型，显示虚拟组织类型/节点名称的格式
    if (allowChangeSpaceType && nodeId !== '-1') {
      // 使用实际的空间类型（管理模块独立状态或全局状态）
      const spaceTypeInfo = virtualSpaceList?.find(
        item => item.NameEn === virtualSpaceKey,
      );

      if (spaceTypeInfo && spaceTypeInfo.NameEn !== 'expert') {
        return `${spaceTypeInfo.NameZh}/${baseNodeName}`;
      }
    }

    return baseNodeName;
  };

  const nodeName = getDisplayName();

  return (
    <Trigger
      popupVisible={visible}
      popup={() => (
        <Popup
          spaceType={virtualSpaceKey as SpaceType}
          nodeId={nodeId}
          treeData={treeData}
          allowChangeSpaceType={allowChangeSpaceType}
          onClose={setFalse}
          onChange={onChange}
        />
      )}
      popupAlign={{ bottom: 7 }}
      style={{ zIndex: 999 }}
      position="bl"
      getPopupContainer={() => nodeRef?.current ?? document.body}
      trigger="click"
      onClickOutside={setFalse}>
      <Tooltip
        disabled={!allowExpand}
        content="点击切换"
        getPopupContainer={() => document.getElementById('root') as HTMLElement}
        popupHoverStay={false}>
        <div
          className={cs([
            'flex items-center gap-2',
            allowExpand && 'cursor-pointer',
          ])}
          ref={nodeRef}
          onClick={() => allowExpand && toggle()}>
          <Avatar
            size={logoSize || 32}
            style={{ background: '#0057fe', color: 'white' }}>
            {nodeName![0]}
          </Avatar>
          <span
            className="text-xl font-medium"
            style={nameSize ? { fontSize: nameSize } : {}}>{`${nodeName}${
            allowChangeSpaceType ? '(数据管理)' : ''
          }`}</span>
          {allowExpand &&
            (visible ? (
              <IconCaretUp className="!text-xs" />
            ) : (
              <IconCaretDown className="!text-xs" />
            ))}
        </div>
      </Tooltip>
    </Trigger>
  );
};

export default SpaceSelector;
