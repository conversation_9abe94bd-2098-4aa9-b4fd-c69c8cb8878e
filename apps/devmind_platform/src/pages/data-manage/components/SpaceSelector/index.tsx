import { FC, useRef } from 'react';

import { Ava<PERSON>, Trigger, Toolt<PERSON> } from '@arco-design/web-react';
import { IconCaretDown, IconCaretUp } from '@arco-design/web-react/icon';
import { useBoolean } from '@byted/hooks';
import { useModel } from '@reduck/core';
import cs from 'classnames';

import { ExpertSpaceNodeID } from '@quality/common-apis/src/apis/api/metric_platform/model';

import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { SpaceType } from '@/interface';
import globalModel from '@/model/global';

import Popup, { Props as PopupProps } from './components/Popup';

interface Props extends Omit<PopupProps, 'onClose' | 'spaceType'> {
  logoSize?: number;
  nameSize?: number;
  allowExpand?: boolean; // 是否支持展开属性选择
  manageSpaceType?: string; // 管理模块独立的空间类型，不影响全局状态
}

const SpaceSelector: FC<Props> = props => {
  const {
    nodeId,
    treeData,
    allowExpand = true,
    allowChangeSpaceType = false,
    logoSize,
    nameSize,
    onChange,
    manageSpaceType,
  } = props;
  const { virtualSpaceKey: globalVirtualSpaceKey } = useVirtualSpaceType();
  const [{ nodeIdRecord, virtualSpaceList, authData }] = useModel([
    globalModel,
  ]);

  // 如果是管理模块且传递了manageSpaceType，使用独立的空间类型，否则使用全局的
  const virtualSpaceKey = manageSpaceType || globalVirtualSpaceKey;

  const { state: visible, toggle, setFalse } = useBoolean(false);
  const nodeRef = useRef(null);

  // 判断用户是否为管理员
  const isAdmin = Boolean(
    authData?.isAdmin || authData?.isDataManager || authData?.isDomainExpert,
  );

  // 优化: 节点显示名称
  const getDisplayName = () => {
    // 非管理员且没有有效节点 → 显示提示
    if (!isAdmin && (!nodeId || nodeId === ExpertSpaceNodeID)) {
      return '请选择节点';
    }

    // 获取基础节点名称
    const baseNodeName =
      nodeIdRecord[nodeId]?.node_name ??
      (nodeId === ExpertSpaceNodeID ? '专家空间' : '无组织节点');

    // 添加空间类型前缀
    if (allowChangeSpaceType && nodeId !== ExpertSpaceNodeID) {
      const spaceTypeInfo = virtualSpaceList?.find(
        item => item.NameEn === virtualSpaceKey,
      );

      if (spaceTypeInfo && spaceTypeInfo.NameEn !== SpaceType.Expert) {
        return `${spaceTypeInfo.NameZh}/${baseNodeName}`;
      }
    }

    return baseNodeName;
  };

  const nodeName = getDisplayName();

  return (
    <Trigger
      popupVisible={visible}
      popup={() => (
        <Popup
          spaceType={virtualSpaceKey as SpaceType}
          nodeId={nodeId}
          treeData={treeData}
          allowChangeSpaceType={allowChangeSpaceType}
          onClose={setFalse}
          onChange={(newNodeId, newSpaceType) => {
            // 非管理员禁止选择专家空间
            if (!isAdmin && newSpaceType === SpaceType.Expert) {
              return;
            }

            onChange?.(newNodeId, newSpaceType);
          }}
        />
      )}
      popupAlign={{ bottom: 7 }}
      style={{ zIndex: 999 }}
      position="bl"
      getPopupContainer={() => nodeRef?.current ?? document.body}
      trigger="click"
      onClickOutside={setFalse}>
      <Tooltip
        disabled={!allowExpand}
        content="点击切换"
        getPopupContainer={() => document.getElementById('root') as HTMLElement}
        popupHoverStay={false}>
        <div
          className={cs([
            'flex items-center gap-2',
            allowExpand && 'cursor-pointer',
          ])}
          ref={nodeRef}
          onClick={() => allowExpand && toggle()}>
          <Avatar
            size={logoSize || 32}
            style={{
              background: !isAdmin && !nodeId ? '#ccc' : '#0057fe',
              color: 'white',
            }}>
            {!isAdmin && !nodeId ? '?' : nodeName?.[0]}
          </Avatar>
          <span
            className="text-xl font-medium"
            style={nameSize ? { fontSize: nameSize } : {}}>
            {!isAdmin && !nodeId
              ? '请选择节点'
              : `${nodeName}${allowChangeSpaceType ? '(空间)' : ''}`}
          </span>
          {allowExpand &&
            (visible ? (
              <IconCaretUp className="!text-xs" />
            ) : (
              <IconCaretDown className="!text-xs" />
            ))}
        </div>
      </Tooltip>
    </Trigger>
  );
};

export default SpaceSelector;
