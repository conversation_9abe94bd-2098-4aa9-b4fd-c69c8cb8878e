import { FC, useState, useEffect, useCallback, useMemo } from 'react';

import { Input, Tree, Spin, Empty, Select } from '@arco-design/web-react';
import { IconSearch } from '@arco-design/web-react/icon';
import { useModel } from '@reduck/core';
import { useRequest, useDebounce, useDebounceFn } from 'ahooks';
import cs from 'classnames';

import { NodeStatusAccessed } from '@quality/common-apis/src/apis/api/insight/consts';
import { insightServiceClient } from '@quality/common-apis/src/apis/api/insight/homepage';
import type { SearchNodeItem as SearchNodeItemType } from '@quality/common-apis/src/apis/api/insight/model';
import { metricDictServiceClient } from '@quality/common-apis/src/apis/api/metric_platform/dimension';
import { ExpertSpaceNodeID } from '@quality/common-apis/src/apis/api/metric_platform/model';
import {
  SimpleUserInfo,
  ProductInfo,
} from '@quality/common-apis/src/apis/api/product/product_model';

import { API_V1 } from '@/constants';
import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { SpaceType } from '@/interface';
import globalModel from '@/model/global';
import { NodeManager } from '@/modules/node';
import { DataManageUrls, MetricStoryManageUrls } from '@/routers/path';
import { treeFlat } from '@/utils/utils';

import SearchNodeItem from './SearchNodeItem';
import TreeNodeItem from './TreeNodeItem';

export interface TreeNode {
  id: string;
  pid: string;
  authorization_type: string;
  liaison_officers?: SimpleUserInfo[];
  name: string;
  children?: TreeNode[];
  parent: { id: string; name: string }[]; // 父空间名称列表, 面包屑展示
  color?: string;
}

export interface Props {
  spaceType: SpaceType;
  nodeId: string;
  treeData?: ProductInfo[];
  allowChangeSpaceType?: boolean; // 是否支持空间切换
  onClose: () => void;
  onChange?: (nodeId: string, spaceType: SpaceType) => void;
}

const expertSpaceOption = {
  label: '专家空间',
  value: SpaceType.Expert,
};

const Popup: FC<Props> = props => {
  const {
    spaceType,
    nodeId,
    treeData: propsTreeData,
    allowChangeSpaceType = false,
    onClose,
    onChange,
  } = props;

  const { virtualItem } = useVirtualSpaceType();
  const [{ authData, virtualTreeMaps, virtualSpaceList }] = useModel([
    globalModel,
  ]);

  // 判断用户是否为管理员（任一权限为true即为管理员）
  const isAdmin = Boolean(
    authData?.isAdmin || authData?.isDataManager || authData?.isDomainExpert,
  );

  // 获取虚拟组织类型选项（除了专家空间）
  const virtualSpaceOptions = useMemo(
    () =>
      (virtualSpaceList ?? [])
        .filter(item => item.Virtual && item.NameEn !== SpaceType.Expert)
        .map(item => ({
          label: item?.NameZh,
          value: item?.NameEn,
        })),
    [virtualSpaceList],
  );

  // 获取所有空间类型选项（专家空间 + 虚拟组织类型）
  const allSpaceOptions = useMemo(() => {
    return [expertSpaceOption, ...virtualSpaceOptions];
  }, [virtualSpaceOptions]);

  // 支持空间切换时 记录当前选中的空间类型
  const [selectedSpaceType, setSelectedSpaceType] = useState<SpaceType>(
    spaceType || SpaceType.Expert,
  );

  // 同步父组件传入的空间类型变化
  useEffect(() => {
    setSelectedSpaceType(spaceType);
  }, [spaceType]);

  const handleSpaceTypeChange = (type: SpaceType) => {
    setSelectedSpaceType(type);

    // 如果是管理模块，立即通知父组件空间类型变化
    const pathname = window.location.pathname;
    const isInManageModule =
      pathname.startsWith(MetricStoryManageUrls.MetricStory) ||
      pathname.startsWith(DataManageUrls.DataManage);

    if (isInManageModule) {
      // 传递空节点ID表示仅更新空间类型
      onChange?.('', type);
    }

    // 专家空间特殊处理
    if (type === SpaceType.Expert) {
      onChange?.(ExpertSpaceNodeID, type);
      onClose?.();
    }
  };

  // 搜索空间
  const [queryName, setQueryName] = useState<string>('');
  const debounceQueryName = useDebounce(queryName);
  const {
    data: searchRes,
    loading: searchLoading,
    run,
  } = useRequest(
    async (nodeName: string) => {
      if (!nodeName) {
        return;
      }
      // 外部数据源，则直接走前端搜索
      if (propsTreeData) {
        const list = treeFlat<ProductInfo>(propsTreeData, 'children');
        return list
          .filter(({ node_name }) =>
            node_name.toLowerCase().includes(nodeName.toLowerCase()),
          )
          .map<SearchNodeItemType>(
            ({ node_id, node_name, authorization_type, liaison_officers }) => ({
              node_id,
              display_name: node_name,
              hint_content: '',
              authorization_type,
              node_status: NodeStatusAccessed,
              user_name: liaison_officers?.[0].username || '',
            }),
          );
      }
      // 正常走接口搜索
      const res = await metricDictServiceClient.SearchSpaceTree({
        version: API_V1,
        space_type: selectedSpaceType,
        content: nodeName,
      });
      const { data } = res.data;
      return data;
    },
    { manual: true },
  );
  const { run: debounceSearch } = useDebounceFn(run, { wait: 300 });
  const handleSearch = (v: string) => {
    setQueryName(v);
    debounceSearch(v);
  };

  // 空间树 - 根据选中的空间类型获取对应的树数据
  const treeData = useMemo(() => {
    if (propsTreeData) {
      return propsTreeData;
    }
    if (selectedSpaceType === SpaceType.Expert) {
      return [];
    }
    return virtualTreeMaps[selectedSpaceType]?.treeData ?? [];
  }, [propsTreeData, selectedSpaceType, virtualTreeMaps]);

  const generatorTreeNodes = useCallback(
    (treeData: ProductInfo[]) =>
      treeData.map(item => {
        const { children, node_id } = item;
        return (
          <Tree.Node
            key={node_id}
            dataRef={item}
            title={
              <TreeNodeItem
                activeNodeId={nodeId}
                nodeInfo={item}
                onClick={handleNodeChange}
              />
            }>
            {children?.length ? generatorTreeNodes(children) : null}
          </Tree.Node>
        );
      }),
    [nodeId],
  );

  const handleNodeChange = async (nodeId: string) => {
    // 确保使用当前选中的空间类型
    const actualSpaceType = selectedSpaceType;

    // 在数据管理和指标故事管理模块中，对于虚拟组织节点，直接使用树数据中的信息
    const pathname = window.location.pathname;
    const isInManageModule =
      pathname.startsWith(MetricStoryManageUrls.MetricStory) ||
      pathname.startsWith(DataManageUrls.DataManage);

    if (isInManageModule && actualSpaceType !== SpaceType.Expert) {
      // 从树数据中查找节点信息
      const findNodeInTree = (
        nodes: ProductInfo[],
        targetId: string,
      ): ProductInfo | null => {
        for (const node of nodes) {
          if (node.node_id === targetId) {
            return node;
          }
          if (node.children) {
            const found = findNodeInTree(node.children, targetId);
            if (found) {
              return found;
            }
          }
        }
        return null;
      };

      const nodeInfo = findNodeInTree(treeData, nodeId);
      if (nodeInfo && !NodeManager.getModule()?.getNodeInfo(nodeId)?.node_id) {
        // 将节点信息添加到NodeManager中，并包含空间类型信息
        const nodeInfoWithSpaceType = {
          ...nodeInfo,
          spaceType: actualSpaceType,
        };
        NodeManager.getModule()?.addNodeRecord(nodeInfoWithSpaceType);
      }
    }

    // 管理模块不需要调用GetNodeInfo API，因为它有独立的数据加载机制
    if (!isInManageModule) {
      // 只有非管理模块才需要调用API获取节点信息
      if (
        nodeId !== ExpertSpaceNodeID &&
        !NodeManager.getModule()?.getNodeInfo(nodeId)?.node_id
      ) {
        try {
          const res = await insightServiceClient.GetNodeInfo({
            version: API_V1,
            node_id: nodeId,
            space_type: actualSpaceType,
          });

          // 检查API响应是否有效
          if (res?.data?.node_id) {
            NodeManager.getModule()?.addNodeRecord(res.data);
          } else {
            console.error('API返回的节点信息无效:', res);
          }
        } catch (error) {
          console.error('获取节点信息失败:', error);
        }
      }
    }

    // 确保传递正确的空间类型
    onChange?.(nodeId, actualSpaceType);
    onClose?.();
  };

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  useEffect(() => {
    if (nodeId) {
      const keys: string[] = [];
      const loop = (id: string) => {
        const pid = NodeManager.getModule()?.getNodeInfo(id)?.pid;
        if (pid) {
          keys.push(pid);
          loop(pid);
        }
      };
      loop(nodeId);
      setExpandedKeys(keys);
    }
  }, [nodeId, selectedSpaceType]);

  // 空间列表渲染
  const renderContent = () => {
    if (searchLoading) {
      return (
        <Spin
          size={36}
          block={true}
          className="flex justify-center items-center h-full"
        />
      );
    }

    // 展示搜索列表
    if (debounceQueryName && queryName) {
      return (
        <div className="overflow-y-auto h-full thin-scrollbar">
          {Array.isArray(searchRes) && searchRes?.length ? (
            searchRes.map(item => (
              <SearchNodeItem
                key={item.node_id}
                spaceType={selectedSpaceType}
                nodeInfo={item}
                onClick={handleNodeChange}
              />
            ))
          ) : (
            <Empty
              description="暂无数据"
              className="flex items-center h-full"
            />
          )}
        </div>
      );
    }
    // 展示全量数据
    return treeData.length ? (
      <Tree
        blockNode={true}
        className="thin-scrollbar"
        expandedKeys={expandedKeys}
        fieldNames={{
          key: 'node_id',
          title: 'node_name',
        }}
        virtualListProps={{ height: '100%' }}
        onExpand={setExpandedKeys}>
        {generatorTreeNodes(treeData)}
      </Tree>
    ) : (
      <Empty />
    );
  };
  const pathname = window.location.pathname;

  const options = allSpaceOptions;

  // 加载虚拟组织树数据的effect
  const [, { getVirtualTreeNodes, addVirtualTreeNodes }] = useModel([
    globalModel,
  ]);

  useEffect(() => {
    // 如果选择了虚拟组织类型且还没有加载数据，则加载对应的树数据
    if (
      selectedSpaceType !== SpaceType.Expert &&
      !virtualTreeMaps[selectedSpaceType]?.isLoaded
    ) {
      getVirtualTreeNodes(selectedSpaceType)
        .then((data: any) => {
          const treeData = data?.value || data || [];
          addVirtualTreeNodes(selectedSpaceType, treeData);
        })
        .catch(error => {
          console.error('加载空间树数据失败:', error);
          addVirtualTreeNodes(selectedSpaceType, []);
        });
    }
  }, [
    selectedSpaceType,
    virtualTreeMaps,
    getVirtualTreeNodes,
    addVirtualTreeNodes,
  ]);

  // 判断是否在数据管理或指标故事管理模块
  const isInManageModule =
    pathname.startsWith(MetricStoryManageUrls.MetricStory) ||
    pathname.startsWith(DataManageUrls.DataManage);

  return (
    <div className="bg-white border border-solid border-line2 rounded">
      <div className="border-b border-solid border-line2 p-5">
        {!allowChangeSpaceType && (
          <div className="space-y-3">
            <div className="text-base font-medium">{virtualItem?.NameZh}</div>
            <Input
              className="w-full"
              value={queryName}
              prefix={<IconSearch />}
              allowClear
              placeholder={`请搜索${virtualItem?.NameZh}空间名称`}
              onChange={handleSearch}
            />
          </div>
        )}

        {/* 数据管理和指标故事管理模块：显示两个下拉框在一行 */}
        {allowChangeSpaceType && isInManageModule && (
          <div className="space-y-3">
            <div className="flex gap-4">
              <div className="flex-1">
                <Select
                  className="w-full"
                  value={selectedSpaceType}
                  options={options}
                  placeholder="请选择空间类型"
                  onChange={handleSpaceTypeChange}
                />
              </div>
              {selectedSpaceType !== SpaceType.Expert && (
                <div className="flex-1">
                  <Input
                    className="w-full"
                    value={queryName}
                    prefix={<IconSearch />}
                    allowClear
                    placeholder="请搜索空间名称"
                    onChange={handleSearch}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {/* 其他模块：保持原有设计 */}
        {allowChangeSpaceType && !isInManageModule && (
          <div className={cs(['flex justify-between gap-4'])}>
            <Select
              className="min-w-[222px]"
              value={selectedSpaceType}
              options={options}
              placeholder="请选择空间类型"
              onChange={handleSpaceTypeChange}
            />
            {selectedSpaceType !== SpaceType.Expert && (
              <Input
                className="w-full"
                value={queryName}
                prefix={<IconSearch />}
                allowClear
                placeholder={`请搜索${virtualItem?.NameZh}空间名称`}
                onChange={handleSearch}
              />
            )}
          </div>
        )}
      </div>
      {selectedSpaceType !== SpaceType.Expert && (
        <div
          className={cs([
            'h-[300px] min-w-[500px] max-w-[800px]',
            'pt-4 px-5',
          ])}>
          {renderContent()}
        </div>
      )}
    </div>
  );
};

export default Popup;
