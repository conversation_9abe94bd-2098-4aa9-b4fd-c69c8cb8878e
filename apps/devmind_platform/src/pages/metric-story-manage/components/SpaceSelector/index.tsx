import { FC, useRef } from 'react';

import { Avatar, Trigger, Tooltip } from '@arco-design/web-react';
import { IconCaretDown, IconCaretUp } from '@arco-design/web-react/icon';
import { useBoolean } from '@byted/hooks';
import { useModel } from '@reduck/core';
import cs from 'classnames';

import { useVirtualSpaceType } from '@/hooks/useSpaceType';
import { SpaceType } from '@/interface';
import globalModel from '@/model/global';

import Popup, { Props as PopupProps } from './components/Popup';

interface Props extends Omit<PopupProps, 'onClose' | 'spaceType'> {
  logoSize?: number;
  nameSize?: number;
  allowExpand?: boolean; // 是否支持展开属性选择
  // nodeNamePass?: string; // 传递过来的树节点的显示名称
}

const SpaceSelector: FC<Props> = props => {
  const {
    nodeId,
    treeData,
    allowExpand = true,
    allowChangeSpaceType = false,
    logoSize,
    nameSize,
    onChange,
  } = props;
  const { virtualSpaceKey, virtualItem } = useVirtualSpaceType();
  const [{ nodeIdRecord }] = useModel([globalModel]);

  const { state: visible, toggle, setFalse } = useBoolean(false);
  const nodeRef = useRef(null);

  // 优化后，通过全局的 nodeIdRecord 获取节点名称
  const nodeName = nodeIdRecord[nodeId]?.node_name ?? '无组织节点';

  // JD
  return (
    <Trigger
      popupVisible={visible}
      popup={() => (
        <Popup
          spaceType={virtualSpaceKey as SpaceType}
          nodeId={nodeId}
          treeData={treeData}
          allowChangeSpaceType={allowChangeSpaceType}
          onClose={setFalse}
          onChange={onChange}
        />
      )}
      popupAlign={{ bottom: 7 }}
      style={{ zIndex: 999 }}
      position="bl"
      getPopupContainer={() => nodeRef?.current ?? document.body}
      trigger="click"
      onClickOutside={setFalse}>
      <Tooltip
        disabled={!allowExpand}
        content="点击切换22222"
        getPopupContainer={() => document.getElementById('root') as HTMLElement}
        popupHoverStay={false}>
        <div
          className={cs([
            'flex items-center gap-2',
            allowExpand && 'cursor-pointer',
          ])}
          ref={nodeRef}
          onClick={() => allowExpand && toggle()}>
          <Avatar
            size={logoSize || 32}
            style={{ background: '#0057fe', color: 'white' }}>
            {nodeName![0]}
          </Avatar>
          <span
            className="text-xl font-medium"
            style={nameSize ? { fontSize: nameSize } : {}}>{`${nodeName}${
            allowChangeSpaceType ? '(空间2222)' : ''
          }`}</span>
          {allowExpand &&
            (visible ? (
              <IconCaretUp className="!text-xs" />
            ) : (
              <IconCaretDown className="!text-xs" />
            ))}
        </div>
      </Tooltip>
    </Trigger>
  );
};

export default SpaceSelector;
